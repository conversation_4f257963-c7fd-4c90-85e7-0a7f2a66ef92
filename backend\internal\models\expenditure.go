package models

import (
	"time"
)

// ExpenditureApplication 支出申请模型
type ExpenditureApplication struct {
	BaseModel
	ApplicationNo       string            `gorm:"uniqueIndex;size:64;not null" json:"application_no"`
	Title               string            `gorm:"size:255;not null" json:"title"`
	Type                ApplicationType   `gorm:"not null" json:"type"`
	TotalAmount         float64           `gorm:"type:decimal(18,2);not null" json:"total_amount"`
	Status              ApprovalStatus    `gorm:"size:20;default:'draft'" json:"status"`
	PaymentStatus       PaymentStatus     `gorm:"size:20;default:'unpaid'" json:"payment_status"`
	WorkflowInstanceID  *string           `gorm:"size:64" json:"workflow_instance_id"`
	RelatedApplicationID *uint            `gorm:"index" json:"related_application_id"`
	
	// 申请人信息
	ApplicantID    uint         `gorm:"index;not null" json:"applicant_id"`
	DepartmentID   uint         `gorm:"index;not null" json:"department_id"`
	Applicant      User         `gorm:"foreignKey:ApplicantID" json:"applicant,omitempty"`
	Department     Organization `gorm:"foreignKey:DepartmentID" json:"department,omitempty"`
	
	// 关联的事前申请
	RelatedApplication *ExpenditureApplication `gorm:"foreignKey:RelatedApplicationID" json:"related_application,omitempty"`
	
	// 明细和附件
	Details     []ExpenditureDetail     `gorm:"foreignKey:ApplicationID" json:"details,omitempty"`
	Attachments []ExpenditureAttachment `gorm:"foreignKey:ApplicationID" json:"attachments,omitempty"`
	BudgetLinks []ApplicationBudgetLink `gorm:"foreignKey:ApplicationID" json:"budget_links,omitempty"`
}

// TableName 指定表名
func (ExpenditureApplication) TableName() string {
	return "expenditure_applications"
}

// ExpenditureDetail 支出明细模型
type ExpenditureDetail struct {
	BaseModel
	ApplicationID uint      `gorm:"index;not null" json:"application_id"`
	ExpenseType   string    `gorm:"size:50;not null" json:"expense_type"`
	ExpenseDate   time.Time `gorm:"not null" json:"expense_date"`
	InvoiceNo     string    `gorm:"size:100" json:"invoice_no"`
	InvoiceCode   string    `gorm:"size:50" json:"invoice_code"`
	Amount        float64   `gorm:"type:decimal(18,2);not null" json:"amount"`
	TaxAmount     float64   `gorm:"type:decimal(18,2);default:0" json:"tax_amount"`
	Description   string    `gorm:"size:500" json:"description"`
	
	// 关联字段
	Application ExpenditureApplication `gorm:"foreignKey:ApplicationID" json:"application,omitempty"`
}

// TableName 指定表名
func (ExpenditureDetail) TableName() string {
	return "expenditure_details"
}

// ExpenditureAttachment 支出附件模型
type ExpenditureAttachment struct {
	BaseModel
	ApplicationID uint   `gorm:"index;not null" json:"application_id"`
	FileName      string `gorm:"size:255;not null" json:"file_name"`
	FileSize      int64  `gorm:"not null" json:"file_size"`
	FileType      string `gorm:"size:50" json:"file_type"`
	FilePath      string `gorm:"size:500;not null" json:"file_path"`
	FileURL       string `gorm:"size:500" json:"file_url"`
	
	// 关联字段
	Application ExpenditureApplication `gorm:"foreignKey:ApplicationID" json:"application,omitempty"`
}

// TableName 指定表名
func (ExpenditureAttachment) TableName() string {
	return "expenditure_attachments"
}

// ApplicationBudgetLink 申请预算关联模型
type ApplicationBudgetLink struct {
	BaseModel
	ApplicationID uint           `gorm:"index;not null" json:"application_id"`
	BudgetItemID  uint           `gorm:"index;not null" json:"budget_item_id"`
	Amount        float64        `gorm:"type:decimal(18,2);not null" json:"amount"`
	LinkType      BudgetLinkType `gorm:"not null" json:"link_type"`
	
	// 关联字段
	Application ExpenditureApplication `gorm:"foreignKey:ApplicationID" json:"application,omitempty"`
	BudgetItem  BudgetItem             `gorm:"foreignKey:BudgetItemID" json:"budget_item,omitempty"`
}

// TableName 指定表名
func (ApplicationBudgetLink) TableName() string {
	return "application_budget_links"
}

// Payment 支付记录模型
type Payment struct {
	BaseModel
	PaymentNo     string        `gorm:"uniqueIndex;size:64;not null" json:"payment_no"`
	ApplicationID uint          `gorm:"index;not null" json:"application_id"`
	Amount        float64       `gorm:"type:decimal(18,2);not null" json:"amount"`
	PaymentMethod string        `gorm:"size:50" json:"payment_method"`
	PaymentDate   *time.Time    `json:"payment_date"`
	Status        PaymentStatus `gorm:"size:20;default:'unpaid'" json:"status"`
	Remark        string        `gorm:"size:500" json:"remark"`
	
	// 支付人信息
	PayerID *uint `gorm:"index" json:"payer_id"`
	Payer   *User `gorm:"foreignKey:PayerID" json:"payer,omitempty"`
	
	// 关联字段
	Application ExpenditureApplication `gorm:"foreignKey:ApplicationID" json:"application,omitempty"`
}

// TableName 指定表名
func (Payment) TableName() string {
	return "payments"
}

// ExpenseStandard 费用标准模型
type ExpenseStandard struct {
	BaseModel
	Name         string  `gorm:"size:100;not null" json:"name"`
	Type         string  `gorm:"size:50;not null" json:"type"` // travel, meal, accommodation
	Level        string  `gorm:"size:50" json:"level"`         // 职级
	City         string  `gorm:"size:50" json:"city"`          // 城市
	StandardType string  `gorm:"size:20;not null" json:"standard_type"` // daily, per_time
	Amount       float64 `gorm:"type:decimal(18,2);not null" json:"amount"`
	Unit         string  `gorm:"size:20" json:"unit"`
	Status       Status  `gorm:"size:20;default:'active'" json:"status"`
	Description  string  `gorm:"size:500" json:"description"`
	
	// 有效期
	StartDate *time.Time `json:"start_date"`
	EndDate   *time.Time `json:"end_date"`
}

// TableName 指定表名
func (ExpenseStandard) TableName() string {
	return "expense_standards"
}

// Invoice 发票台账模型
type Invoice struct {
	BaseModel
	InvoiceCode   string    `gorm:"size:50;not null" json:"invoice_code"`
	InvoiceNo     string    `gorm:"size:100;not null" json:"invoice_no"`
	InvoiceDate   time.Time `gorm:"not null" json:"invoice_date"`
	Amount        float64   `gorm:"type:decimal(18,2);not null" json:"amount"`
	TaxAmount     float64   `gorm:"type:decimal(18,2);default:0" json:"tax_amount"`
	TotalAmount   float64   `gorm:"type:decimal(18,2);not null" json:"total_amount"`
	SellerName    string    `gorm:"size:200" json:"seller_name"`
	SellerTaxNo   string    `gorm:"size:50" json:"seller_tax_no"`
	BuyerName     string    `gorm:"size:200" json:"buyer_name"`
	BuyerTaxNo    string    `gorm:"size:50" json:"buyer_tax_no"`
	CheckCode     string    `gorm:"size:50" json:"check_code"`
	IsUsed        bool      `gorm:"default:false" json:"is_used"`
	UsedAt        *time.Time `json:"used_at"`
	ApplicationID *uint     `gorm:"index" json:"application_id"`
	
	// 关联字段
	Application *ExpenditureApplication `gorm:"foreignKey:ApplicationID" json:"application,omitempty"`
}

// TableName 指定表名
func (Invoice) TableName() string {
	return "invoices"
}

// 添加唯一索引
func (Invoice) TableIndexes() []string {
	return []string{
		"idx_invoice_code_no", // 发票代码+号码唯一索引
	}
}
