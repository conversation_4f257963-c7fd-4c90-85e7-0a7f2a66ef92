package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"
)

// SystemConfig 系统配置模型
type SystemConfig struct {
	BaseModel
	ConfigKey   string `gorm:"uniqueIndex;size:100;not null" json:"config_key"`
	ConfigValue string `gorm:"type:text" json:"config_value"`
	ConfigType  string `gorm:"size:20;default:'string'" json:"config_type"` // string, number, boolean, json
	Category    string `gorm:"size:50" json:"category"`
	Description string `gorm:"size:255" json:"description"`
	IsSystem    bool   `gorm:"default:false" json:"is_system"`
	Status      Status `gorm:"size:20;default:'active'" json:"status"`
}

// TableName 指定表名
func (SystemConfig) TableName() string {
	return "system_configs"
}

// Notification 通知模型
type Notification struct {
	BaseModel
	Title       string           `gorm:"size:200;not null" json:"title"`
	Content     string           `gorm:"type:text" json:"content"`
	Type        string           `gorm:"size:20;not null" json:"type"` // system, workflow, reminder
	Priority    int              `gorm:"default:1" json:"priority"`    // 1:低 2:中 3:高
	Status      Status           `gorm:"size:20;default:'active'" json:"status"`
	PublishTime *time.Time       `json:"publish_time"`
	ExpireTime  *time.Time       `json:"expire_time"`
	
	// 发送人信息
	SenderID *uint `gorm:"index" json:"sender_id"`
	Sender   *User `gorm:"foreignKey:SenderID" json:"sender,omitempty"`
	
	// 接收记录
	Recipients []NotificationRecipient `gorm:"foreignKey:NotificationID" json:"recipients,omitempty"`
}

// TableName 指定表名
func (Notification) TableName() string {
	return "notifications"
}

// NotificationRecipient 通知接收记录模型
type NotificationRecipient struct {
	BaseModel
	NotificationID uint       `gorm:"index;not null" json:"notification_id"`
	RecipientID    uint       `gorm:"index;not null" json:"recipient_id"`
	IsRead         bool       `gorm:"default:false" json:"is_read"`
	ReadTime       *time.Time `json:"read_time"`
	
	// 关联字段
	Notification Notification `gorm:"foreignKey:NotificationID" json:"notification,omitempty"`
	Recipient    User         `gorm:"foreignKey:RecipientID" json:"recipient,omitempty"`
}

// TableName 指定表名
func (NotificationRecipient) TableName() string {
	return "notification_recipients"
}

// AuditLog 审计日志模型
type AuditLog struct {
	BaseModel
	UserID      *uint      `gorm:"index" json:"user_id"`
	Action      string     `gorm:"size:255;not null" json:"action"`
	TargetID    string     `gorm:"size:64" json:"target_id"`
	TargetType  string     `gorm:"size:64" json:"target_type"`
	OldValue    JSONData   `gorm:"type:jsonb" json:"old_value"`
	NewValue    JSONData   `gorm:"type:jsonb" json:"new_value"`
	IPAddress   string     `gorm:"size:64" json:"ip_address"`
	UserAgent   string     `gorm:"size:500" json:"user_agent"`
	
	// 关联字段
	User *User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// TableName 指定表名
func (AuditLog) TableName() string {
	return "audit_logs"
}

// FileUpload 文件上传记录模型
type FileUpload struct {
	BaseModel
	FileName     string `gorm:"size:255;not null" json:"file_name"`
	OriginalName string `gorm:"size:255;not null" json:"original_name"`
	FileSize     int64  `gorm:"not null" json:"file_size"`
	FileType     string `gorm:"size:50" json:"file_type"`
	FilePath     string `gorm:"size:500;not null" json:"file_path"`
	FileURL      string `gorm:"size:500" json:"file_url"`
	BusinessType string `gorm:"size:50" json:"business_type"`
	BusinessID   string `gorm:"size:64" json:"business_id"`
	
	// 上传人信息
	UploaderID uint `gorm:"index" json:"uploader_id"`
	Uploader   User `gorm:"foreignKey:UploaderID" json:"uploader,omitempty"`
}

// TableName 指定表名
func (FileUpload) TableName() string {
	return "file_uploads"
}

// SystemStats 系统统计模型
type SystemStats struct {
	BaseModel
	StatDate    string   `gorm:"type:date;uniqueIndex:idx_stat_date_type" json:"stat_date"`
	StatType    string   `gorm:"size:50;uniqueIndex:idx_stat_date_type" json:"stat_type"`
	StatValue   float64  `gorm:"type:decimal(18,2)" json:"stat_value"`
	StatData    JSONData `gorm:"type:jsonb" json:"stat_data"`
	Description string   `gorm:"size:255" json:"description"`
}

// TableName 指定表名
func (SystemStats) TableName() string {
	return "system_stats"
}

// JSONData JSON数据类型
type JSONData map[string]interface{}

// Value 实现 driver.Valuer 接口
func (j JSONData) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan 实现 sql.Scanner 接口
func (j *JSONData) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	
	return json.Unmarshal(bytes, j)
}

// OperationLog 操作日志模型
type OperationLog struct {
	BaseModel
	UserID      *uint    `gorm:"index" json:"user_id"`
	Module      string   `gorm:"size:50;not null" json:"module"`
	Operation   string   `gorm:"size:100;not null" json:"operation"`
	Method      string   `gorm:"size:10" json:"method"`
	URL         string   `gorm:"size:500" json:"url"`
	IPAddress   string   `gorm:"size:64" json:"ip_address"`
	UserAgent   string   `gorm:"size:500" json:"user_agent"`
	RequestData JSONData `gorm:"type:jsonb" json:"request_data"`
	ResponseData JSONData `gorm:"type:jsonb" json:"response_data"`
	Duration    int64    `json:"duration"` // 毫秒
	Status      int      `json:"status"`
	
	// 关联字段
	User *User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// TableName 指定表名
func (OperationLog) TableName() string {
	return "operation_logs"
}
