package service

import (
	"errors"
	"quality_control/backend/internal/models"

	"gorm.io/gorm"
)

type BudgetService struct {
	db *gorm.DB
}

func NewBudgetService(db *gorm.DB) *BudgetService {
	return &BudgetService{db: db}
}

// CreateBudgetSchemeRequest 创建预算方案请求
type CreateBudgetSchemeRequest struct {
	Name        string `json:"name" binding:"required"`
	Year        int    `json:"year" binding:"required"`
	Description string `json:"description"`
	StartDate   string `json:"start_date" binding:"required"`
	EndDate     string `json:"end_date" binding:"required"`
}

// UpdateBudgetSchemeRequest 更新预算方案请求
type UpdateBudgetSchemeRequest struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	StartDate   string `json:"start_date"`
	EndDate     string `json:"end_date"`
}

// BudgetSchemeListRequest 预算方案列表请求
type BudgetSchemeListRequest struct {
	models.PageRequest
	Name   string                 `form:"name"`
	Year   int                    `form:"year"`
	Status models.ApprovalStatus  `form:"status"`
}

// CreateBudgetScheme 创建预算方案
func (s *BudgetService) CreateBudgetScheme(req *CreateBudgetSchemeRequest, creatorID uint) (*models.BudgetScheme, error) {
	// 检查同年度是否已有预算方案
	var count int64
	if err := s.db.Model(&models.BudgetScheme{}).
		Where("year = ? AND status != ?", req.Year, models.ApprovalStatusCancelled).
		Count(&count).Error; err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.New("该年度已存在预算方案")
	}

	scheme := &models.BudgetScheme{
		Name:        req.Name,
		Year:        req.Year,
		Description: req.Description,
		StartDate:   req.StartDate,
		EndDate:     req.EndDate,
		CreatorID:   creatorID,
		Status:      models.ApprovalStatusDraft,
	}

	if err := s.db.Create(scheme).Error; err != nil {
		return nil, err
	}

	return scheme, nil
}

// GetBudgetSchemes 获取预算方案列表
func (s *BudgetService) GetBudgetSchemes(req *BudgetSchemeListRequest) (*UserListResponse, error) {
	req.SetDefaults()
	
	query := s.db.Model(&models.BudgetScheme{})
	
	if req.Name != "" {
		query = query.Where("name LIKE ?", "%"+req.Name+"%")
	}
	if req.Year > 0 {
		query = query.Where("year = ?", req.Year)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	var schemes []models.BudgetScheme
	if err := query.Preload("Creator").
		Offset(req.GetOffset()).Limit(req.PageSize).
		Order("created_at DESC").
		Find(&schemes).Error; err != nil {
		return nil, err
	}

	// 转换为通用响应格式
	var users []models.User
	return &UserListResponse{
		List: users,
		PageInfo: models.PageInfo{
			Page:     req.Page,
			PageSize: req.PageSize,
			Total:    total,
		},
	}, nil
}

// GetBudgetScheme 获取预算方案详情
func (s *BudgetService) GetBudgetScheme(id uint) (*models.BudgetScheme, error) {
	var scheme models.BudgetScheme
	if err := s.db.Preload("Creator").Preload("BudgetData").
		Where("id = ?", id).First(&scheme).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("预算方案不存在")
		}
		return nil, err
	}

	return &scheme, nil
}

// UpdateBudgetScheme 更新预算方案
func (s *BudgetService) UpdateBudgetScheme(id uint, req *UpdateBudgetSchemeRequest) (*models.BudgetScheme, error) {
	var scheme models.BudgetScheme
	if err := s.db.Where("id = ?", id).First(&scheme).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("预算方案不存在")
		}
		return nil, err
	}

	// 只有草稿状态才能修改
	if scheme.Status != models.ApprovalStatusDraft {
		return nil, errors.New("只有草稿状态的预算方案才能修改")
	}

	updates := map[string]interface{}{}
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.StartDate != "" {
		updates["start_date"] = req.StartDate
	}
	if req.EndDate != "" {
		updates["end_date"] = req.EndDate
	}

	if len(updates) > 0 {
		if err := s.db.Model(&scheme).Updates(updates).Error; err != nil {
			return nil, err
		}
	}

	return s.GetBudgetScheme(id)
}

// DeleteBudgetScheme 删除预算方案
func (s *BudgetService) DeleteBudgetScheme(id uint) error {
	var scheme models.BudgetScheme
	if err := s.db.Where("id = ?", id).First(&scheme).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("预算方案不存在")
		}
		return err
	}

	// 只有草稿状态才能删除
	if scheme.Status != models.ApprovalStatusDraft {
		return errors.New("只有草稿状态的预算方案才能删除")
	}

	return s.db.Delete(&scheme).Error
}

// SubmitBudgetScheme 提交预算方案
func (s *BudgetService) SubmitBudgetScheme(id uint) error {
	var scheme models.BudgetScheme
	if err := s.db.Where("id = ?", id).First(&scheme).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("预算方案不存在")
		}
		return err
	}

	if scheme.Status != models.ApprovalStatusDraft {
		return errors.New("只有草稿状态的预算方案才能提交")
	}

	// 检查是否有预算数据
	var count int64
	if err := s.db.Model(&models.BudgetData{}).Where("scheme_id = ?", id).Count(&count).Error; err != nil {
		return err
	}
	if count == 0 {
		return errors.New("请先添加预算数据")
	}

	return s.db.Model(&scheme).Update("status", models.ApprovalStatusPending).Error
}

// ApproveBudgetScheme 审批预算方案
func (s *BudgetService) ApproveBudgetScheme(id uint, approved bool, comment string) error {
	var scheme models.BudgetScheme
	if err := s.db.Where("id = ?", id).First(&scheme).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("预算方案不存在")
		}
		return err
	}

	if scheme.Status != models.ApprovalStatusPending {
		return errors.New("只有待审批状态的预算方案才能审批")
	}

	var newStatus models.ApprovalStatus
	if approved {
		newStatus = models.ApprovalStatusApproved
	} else {
		newStatus = models.ApprovalStatusRejected
	}

	return s.db.Model(&scheme).Update("status", newStatus).Error
}

// CreateBudgetItemRequest 创建预算科目请求
type CreateBudgetItemRequest struct {
	Name         string `json:"name" binding:"required"`
	Code         string `json:"code" binding:"required"`
	ParentID     *uint  `json:"parent_id"`
	Type         string `json:"type" binding:"required,oneof=income expense"`
	Category     string `json:"category"`
	Unit         string `json:"unit"`
	Description  string `json:"description"`
	IsControlled bool   `json:"is_controlled"`
	ControlType  int    `json:"control_type"`
}

// CreateBudgetItem 创建预算科目
func (s *BudgetService) CreateBudgetItem(req *CreateBudgetItemRequest) (*models.BudgetItem, error) {
	// 检查代码是否已存在
	var count int64
	if err := s.db.Model(&models.BudgetItem{}).Where("code = ?", req.Code).Count(&count).Error; err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.New("预算科目代码已存在")
	}

	// 计算层级
	level := 1
	if req.ParentID != nil {
		var parent models.BudgetItem
		if err := s.db.Where("id = ?", *req.ParentID).First(&parent).Error; err != nil {
			return nil, errors.New("父科目不存在")
		}
		level = parent.Level + 1
	}

	// 获取排序号
	var maxSort int
	s.db.Model(&models.BudgetItem{}).Where("parent_id = ?", req.ParentID).
		Select("COALESCE(MAX(sort), 0)").Scan(&maxSort)

	item := &models.BudgetItem{
		Name:         req.Name,
		Code:         req.Code,
		ParentID:     req.ParentID,
		Level:        level,
		Type:         req.Type,
		Category:     req.Category,
		Unit:         req.Unit,
		Sort:         maxSort + 1,
		Description:  req.Description,
		IsControlled: req.IsControlled,
		ControlType:  req.ControlType,
		Status:       models.StatusActive,
	}

	if err := s.db.Create(item).Error; err != nil {
		return nil, err
	}

	return item, nil
}

// GetBudgetItemTree 获取预算科目树
func (s *BudgetService) GetBudgetItemTree() ([]BudgetItemTreeNode, error) {
	var items []models.BudgetItem
	if err := s.db.Where("status = ?", models.StatusActive).
		Order("level ASC, sort ASC").Find(&items).Error; err != nil {
		return nil, err
	}

	return s.buildBudgetItemTree(items, nil), nil
}

// BudgetItemTreeNode 预算科目树节点
type BudgetItemTreeNode struct {
	models.BudgetItem
	Children []BudgetItemTreeNode `json:"children"`
}

// buildBudgetItemTree 构建预算科目树
func (s *BudgetService) buildBudgetItemTree(items []models.BudgetItem, parentID *uint) []BudgetItemTreeNode {
	var nodes []BudgetItemTreeNode
	
	for _, item := range items {
		if (parentID == nil && item.ParentID == nil) || 
		   (parentID != nil && item.ParentID != nil && *item.ParentID == *parentID) {
			node := BudgetItemTreeNode{
				BudgetItem: item,
				Children:   s.buildBudgetItemTree(items, &item.ID),
			}
			nodes = append(nodes, node)
		}
	}
	
	return nodes
}

// BudgetAnalysisRequest 预算分析请求
type BudgetAnalysisRequest struct {
	models.PageRequest
	Year           int    `form:"year"`
	DepartmentIDs  []uint `form:"department_ids"`
	MonthRange     []int  `form:"month_range"`
	BudgetType     string `form:"budget_type"`
}

// GetBudgetAnalysis 获取预算分析数据
func (s *BudgetService) GetBudgetAnalysis(req *BudgetAnalysisRequest) (*UserListResponse, error) {
	req.SetDefaults()

	// 这里应该实现具体的预算分析逻辑
	// 暂时返回空数据
	var users []models.User
	return &UserListResponse{
		List: users,
		PageInfo: models.PageInfo{
			Page:     req.Page,
			PageSize: req.PageSize,
			Total:    0,
		},
	}, nil
}
