import request from './request'

// ==================== 报销申请相关 ====================

// 获取事前申请列表
export const getPreApplications = (params) => {
  return request.get('/expenditure/pre-applications', { params })
}

// 创建事前申请
export const createPreApplication = (data) => {
  return request.post('/expenditure/pre-applications', data)
}

// 获取事前申请详情
export const getPreApplication = (id) => {
  return request.get(`/expenditure/pre-applications/${id}`)
}

// 更新事前申请
export const updatePreApplication = (id, data) => {
  return request.put(`/expenditure/pre-applications/${id}`, data)
}

// 删除事前申请
export const deletePreApplication = (id) => {
  return request.delete(`/expenditure/pre-applications/${id}`)
}

// 提交事前申请审批
export const submitPreApplication = (id) => {
  return request.post(`/expenditure/pre-applications/${id}/submit`)
}

// ==================== 报销申请相关 ====================

// 获取报销申请列表
export const getExpenseApplications = (params) => {
  return request.get('/expenditure/applications', { params })
}

// 创建报销申请
export const createExpenseApplication = (data) => {
  return request.post('/expenditure/applications', data)
}

// 获取报销申请详情
export const getExpenseApplication = (id) => {
  return request.get(`/expenditure/applications/${id}`)
}

// 更新报销申请
export const updateExpenseApplication = (id, data) => {
  return request.put(`/expenditure/applications/${id}`, data)
}

// 删除报销申请
export const deleteExpenseApplication = (id) => {
  return request.delete(`/expenditure/applications/${id}`)
}

// 保存报销申请草稿
export const saveDraftExpenseApplication = (data) => {
  return request.post('/expenditure/applications/draft', data)
}

// 提交报销申请审批
export const submitExpenseApplication = (id) => {
  return request.post(`/expenditure/applications/${id}/submit`)
}

// 撤回报销申请
export const withdrawExpenseApplication = (id) => {
  return request.post(`/expenditure/applications/${id}/withdraw`)
}

// ==================== OCR发票识别相关 ====================

// OCR发票识别
export const ocrInvoiceRecognition = (file) => {
  const formData = new FormData()
  formData.append('file', file)
  
  return request.post('/expenditure/ocr/invoice', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 批量OCR识别
export const batchOcrRecognition = (files) => {
  const formData = new FormData()
  files.forEach(file => {
    formData.append('files', file)
  })
  
  return request.post('/expenditure/ocr/batch', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// ==================== 预算信息相关 ====================

// 获取预算项目树
export const getBudgetItems = (params) => {
  return request.get('/expenditure/budget-items', { params })
}

// 获取成本中心列表
export const getCostCenters = (params) => {
  return request.get('/expenditure/cost-centers', { params })
}

// 获取预算信息
export const getBudgetInfo = (budgetItemId, params) => {
  return request.get(`/expenditure/budget-info/${budgetItemId}`, { params })
}

// 检查预算余额
export const checkBudgetBalance = (data) => {
  return request.post('/expenditure/budget-check', data)
}

// ==================== 审批流程相关 ====================

// 获取审批流程
export const getApprovalFlow = (params) => {
  return request.get('/expenditure/approval-flow', { params })
}

// 获取待审批列表
export const getPendingApprovals = (params) => {
  return request.get('/expenditure/pending-approvals', { params })
}

// 审批报销申请
export const approveExpenseApplication = (id, data) => {
  return request.post(`/expenditure/applications/${id}/approve`, data)
}

// 审批支出申请（别名）
export const approveExpenditureApplication = approveExpenseApplication

// 批量审批申请
export const batchApproveApplications = (data) => {
  return request.post('/expenditure/applications/batch-approve', data)
}

// 驳回报销申请
export const rejectExpenseApplication = (id, data) => {
  return request.post(`/expenditure/applications/${id}/reject`, data)
}



// ==================== 支付管理相关 ====================

// 获取待支付列表
export const getPendingPayments = (params) => {
  return request.get('/expenditure/payments/pending', { params })
}

// 创建支付申请
export const createPaymentRequest = (data) => {
  return request.post('/expenditure/payments', data)
}

// 确认支付
export const confirmPayment = (id, data) => {
  return request.post(`/expenditure/payments/${id}/confirm`, data)
}

// 批量支付
export const batchPayment = (data) => {
  return request.post('/expenditure/payments/batch', data)
}

// 获取支付记录
export const getPaymentRecords = (params) => {
  return request.get('/expenditure/payments/records', { params })
}

// ==================== 费用分析相关 ====================

// 获取费用分析数据
export const getExpenseAnalysis = (params) => {
  return request.get('/expenditure/analysis', { params })
}

// 获取支出分析数据（别名）
export const getExpenditureAnalysis = getExpenseAnalysis

// 获取费用趋势数据
export const getExpenseTrend = (params) => {
  return request.get('/expenditure/trend', { params })
}

// 获取部门费用统计
export const getDepartmentExpenseStats = (params) => {
  return request.get('/expenditure/department-stats', { params })
}

// 获取费用类型统计
export const getExpenseTypeStats = (params) => {
  return request.get('/expenditure/type-stats', { params })
}

// ==================== 费用控制相关 ====================

// 获取费用控制规则
export const getExpenseControlRules = (params) => {
  return request.get('/expenditure/control-rules', { params })
}

// 创建费用控制规则
export const createExpenseControlRule = (data) => {
  return request.post('/expenditure/control-rules', data)
}

// 更新费用控制规则
export const updateExpenseControlRule = (id, data) => {
  return request.put(`/expenditure/control-rules/${id}`, data)
}

// 删除费用控制规则
export const deleteExpenseControlRule = (id) => {
  return request.delete(`/expenditure/control-rules/${id}`)
}

// 获取费用预警信息
export const getExpenseAlerts = (params) => {
  return request.get('/expenditure/alerts', { params })
}

// 处理费用预警
export const handleExpenseAlert = (id, data) => {
  return request.post(`/expenditure/alerts/${id}/handle`, data)
}

// ==================== 报表导出相关 ====================

// 导出费用报表
export const exportExpenseReport = (type, params) => {
  return request.get(`/expenditure/reports/${type}/export`, {
    params,
    responseType: 'blob'
  })
}

// 获取费用汇总报表
export const getExpenseSummaryReport = (params) => {
  return request.get('/expenditure/reports/summary', { params })
}

// 获取费用明细报表
export const getExpenseDetailReport = (params) => {
  return request.get('/expenditure/reports/detail', { params })
}

// 获取部门费用报表
export const getDepartmentExpenseReport = (params) => {
  return request.get('/expenditure/reports/department', { params })
}

// ==================== 费用模板相关 ====================

// 获取费用模板列表
export const getExpenseTemplates = (params) => {
  return request.get('/expenditure/templates', { params })
}

// 创建费用模板
export const createExpenseTemplate = (data) => {
  return request.post('/expenditure/templates', data)
}

// 应用费用模板
export const applyExpenseTemplate = (templateId, data) => {
  return request.post(`/expenditure/templates/${templateId}/apply`, data)
}

// ==================== 费用导入导出相关 ====================

// 导入费用数据
export const importExpenseData = (file, params) => {
  const formData = new FormData()
  formData.append('file', file)
  
  Object.keys(params || {}).forEach(key => {
    formData.append(key, params[key])
  })
  
  return request.post('/expenditure/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 下载费用导入模板
export const downloadExpenseTemplate = (type) => {
  return request.get(`/expenditure/template/${type}`, {
    responseType: 'blob'
  })
}

// 导出费用数据
export const exportExpenseData = (params) => {
  return request.get('/expenditure/export', {
    params,
    responseType: 'blob'
  })
}

// ==================== 费用统计相关 ====================

// 获取费用统计概览
export const getExpenseStatistics = (params) => {
  return request.get('/expenditure/statistics', { params })
}

// 获取支出统计概览（别名）
export const getExpenditureStatistics = getExpenseStatistics

// 获取费用排行榜
export const getExpenseRanking = (params) => {
  return request.get('/expenditure/ranking', { params })
}

// 获取费用对比分析
export const getExpenseComparison = (params) => {
  return request.get('/expenditure/comparison', { params })
}

// ==================== 发票管理相关 ====================

// 获取发票列表
export const getInvoices = (params) => {
  return request.get('/expenditure/invoices', { params })
}

// 上传发票
export const uploadInvoice = (file, data) => {
  const formData = new FormData()
  formData.append('file', file)
  
  Object.keys(data || {}).forEach(key => {
    formData.append(key, data[key])
  })
  
  return request.post('/expenditure/invoices/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 验证发票真伪
export const validateInvoice = (invoiceData) => {
  return request.post('/expenditure/invoices/validate', invoiceData)
}

// 获取发票详情
export const getInvoiceDetail = (id) => {
  return request.get(`/expenditure/invoices/${id}`)
}

// ==================== 费用标准相关 ====================

// 获取费用标准列表
export const getExpenseStandards = (params) => {
  return request.get('/expenditure/standards', { params })
}

// 创建费用标准
export const createExpenseStandard = (data) => {
  return request.post('/expenditure/standards', data)
}

// 更新费用标准
export const updateExpenseStandard = (id, data) => {
  return request.put(`/expenditure/standards/${id}`, data)
}

// 删除费用标准
export const deleteExpenseStandard = (id) => {
  return request.delete(`/expenditure/standards/${id}`)
}

// 检查费用标准
export const checkExpenseStandard = (data) => {
  return request.post('/expenditure/standards/check', data)
}
