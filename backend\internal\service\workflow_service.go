package service

import (
	"errors"
	"fmt"
	"quality_control/backend/internal/models"

	"gorm.io/gorm"
)

// WorkflowService 工作流服务
type WorkflowService struct {
	db *gorm.DB
}

// NewWorkflowService 创建工作流服务
func NewWorkflowService(db *gorm.DB) *WorkflowService {
	return &WorkflowService{db: db}
}

// WorkflowDefinitionListRequest 工作流定义列表请求
type WorkflowDefinitionListRequest struct {
	models.PageRequest
	Name     string `form:"name"`
	Category string `form:"category"`
	Status   string `form:"status"`
}

// WorkflowTaskListRequest 工作流任务列表请求
type WorkflowTaskListRequest struct {
	models.PageRequest
	Status     string `form:"status"`
	AssigneeID *uint  `form:"assignee_id"`
	TaskType   string `form:"task_type"`
}

// CreateWorkflowDefinitionRequest 创建工作流定义请求
type CreateWorkflowDefinitionRequest struct {
	Name        string                 `json:"name" binding:"required"`
	Code        string                 `json:"code" binding:"required"`
	Category    string                 `json:"category" binding:"required"`
	Description string                 `json:"description"`
	Definition  map[string]interface{} `json:"definition"`
}

// UpdateWorkflowDefinitionRequest 更新工作流定义请求
type UpdateWorkflowDefinitionRequest struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Definition  map[string]interface{} `json:"definition"`
}

// WorkflowDefinitionListResponse 工作流定义列表响应
type WorkflowDefinitionListResponse struct {
	List     []models.WorkflowDefinition `json:"list"`
	PageInfo models.PageInfo             `json:"page_info"`
}

// WorkflowTaskListResponse 工作流任务列表响应
type WorkflowTaskListResponse struct {
	List     []models.WorkflowTask `json:"list"`
	PageInfo models.PageInfo       `json:"page_info"`
}

// GetWorkflowDefinitions 获取工作流定义列表
func (s *WorkflowService) GetWorkflowDefinitions(req *WorkflowDefinitionListRequest) (*WorkflowDefinitionListResponse, error) {
	req.SetDefaults()

	query := s.db.Model(&models.WorkflowDefinition{})

	if req.Name != "" {
		query = query.Where("name LIKE ?", "%"+req.Name+"%")
	}
	if req.Category != "" {
		query = query.Where("category = ?", req.Category)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	var definitions []models.WorkflowDefinition
	if err := query.Offset(req.GetOffset()).Limit(req.PageSize).
		Order("created_at DESC").
		Find(&definitions).Error; err != nil {
		return nil, err
	}

	return &WorkflowDefinitionListResponse{
		List: definitions,
		PageInfo: models.PageInfo{
			Page:     req.Page,
			PageSize: req.PageSize,
			Total:    total,
		},
	}, nil
}

// GetWorkflowDefinition 获取工作流定义详情
func (s *WorkflowService) GetWorkflowDefinition(id uint) (*models.WorkflowDefinition, error) {
	var definition models.WorkflowDefinition
	if err := s.db.Where("id = ?", id).First(&definition).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("工作流定义不存在")
		}
		return nil, err
	}
	return &definition, nil
}

// CreateWorkflowDefinition 创建工作流定义
func (s *WorkflowService) CreateWorkflowDefinition(req *CreateWorkflowDefinitionRequest, creatorID uint) (*models.WorkflowDefinition, error) {
	// 检查代码是否已存在
	var count int64
	if err := s.db.Model(&models.WorkflowDefinition{}).Where("code = ?", req.Code).Count(&count).Error; err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.New("工作流代码已存在")
	}

	definition := &models.WorkflowDefinition{
		Name:        req.Name,
		Code:        req.Code,
		Category:    req.Category,
		Description: req.Description,
		Definition:  req.Definition,
		CreatorID:   creatorID,
		Status:      models.StatusActive,
		IsActive:    true,
		Version:     1,
	}

	if err := s.db.Create(definition).Error; err != nil {
		return nil, err
	}

	return definition, nil
}

// UpdateWorkflowDefinition 更新工作流定义
func (s *WorkflowService) UpdateWorkflowDefinition(id uint, req *UpdateWorkflowDefinitionRequest) error {
	var definition models.WorkflowDefinition
	if err := s.db.Where("id = ?", id).First(&definition).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("工作流定义不存在")
		}
		return err
	}

	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.Definition != nil {
		updates["definition"] = req.Definition
	}

	return s.db.Model(&definition).Updates(updates).Error
}

// DeleteWorkflowDefinition 删除工作流定义
func (s *WorkflowService) DeleteWorkflowDefinition(id uint) error {
	var definition models.WorkflowDefinition
	if err := s.db.Where("id = ?", id).First(&definition).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("工作流定义不存在")
		}
		return err
	}

	// 检查是否有正在运行的实例
	var count int64
	if err := s.db.Model(&models.WorkflowInstance{}).
		Where("definition_id = ? AND status = ?", id, models.ApprovalStatusPending).
		Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return errors.New("存在正在运行的工作流实例，无法删除")
	}

	return s.db.Delete(&definition).Error
}

// GetUserTodoTasks 获取用户待办任务
func (s *WorkflowService) GetUserTodoTasks(userID uint, req *WorkflowTaskListRequest) (*WorkflowTaskListResponse, error) {
	req.SetDefaults()

	query := s.db.Model(&models.WorkflowTask{}).
		Where("assignee_id = ? AND status = ?", userID, "pending")

	if req.TaskType != "" {
		query = query.Where("task_type = ?", req.TaskType)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	var tasks []models.WorkflowTask
	if err := query.Offset(req.GetOffset()).Limit(req.PageSize).
		Order("created_at DESC").
		Find(&tasks).Error; err != nil {
		return nil, err
	}

	return &WorkflowTaskListResponse{
		List: tasks,
		PageInfo: models.PageInfo{
			Page:     req.Page,
			PageSize: req.PageSize,
			Total:    total,
		},
	}, nil
}

// GetUserDoneTasks 获取用户已办任务
func (s *WorkflowService) GetUserDoneTasks(userID uint, req *WorkflowTaskListRequest) (*WorkflowTaskListResponse, error) {
	req.SetDefaults()

	query := s.db.Model(&models.WorkflowTask{}).
		Where("assignee_id = ? AND status IN ?", userID, []string{"completed", "approved", "rejected"})

	if req.TaskType != "" {
		query = query.Where("task_type = ?", req.TaskType)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	var tasks []models.WorkflowTask
	if err := query.Offset(req.GetOffset()).Limit(req.PageSize).
		Order("completed_at DESC").
		Find(&tasks).Error; err != nil {
		return nil, err
	}

	return &WorkflowTaskListResponse{
		List: tasks,
		PageInfo: models.PageInfo{
			Page:     req.Page,
			PageSize: req.PageSize,
			Total:    total,
		},
	}, nil
}

// GetWorkflowTasks 获取工作流任务列表
func (s *WorkflowService) GetWorkflowTasks(req *WorkflowTaskListRequest) (*WorkflowTaskListResponse, error) {
	req.SetDefaults()

	query := s.db.Model(&models.WorkflowTask{})

	// 添加调试日志
	fmt.Printf("查询条件: Status=%s, AssigneeID=%v, TaskType=%s\n", req.Status, req.AssigneeID, req.TaskType)

	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.AssigneeID != nil {
		query = query.Where("assignee_id = ?", *req.AssigneeID)
	}
	if req.TaskType != "" {
		query = query.Where("task_type = ?", req.TaskType)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		fmt.Printf("Count查询错误: %v\n", err)
		return nil, err
	}
	fmt.Printf("总记录数: %d\n", total)

	var tasks []models.WorkflowTask
	if err := query.Offset(req.GetOffset()).Limit(req.PageSize).
		Order("created_at DESC").
		Find(&tasks).Error; err != nil {
		fmt.Printf("Find查询错误: %v\n", err)
		return nil, err
	}
	fmt.Printf("查询到的任务数量: %d\n", len(tasks))

	return &WorkflowTaskListResponse{
		List: tasks,
		PageInfo: models.PageInfo{
			Page:     req.Page,
			PageSize: req.PageSize,
			Total:    total,
		},
	}, nil
}
