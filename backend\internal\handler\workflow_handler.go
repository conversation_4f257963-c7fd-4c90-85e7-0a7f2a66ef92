package handler

import (
	"net/http"
	"quality_control/backend/internal/models"
	"quality_control/backend/internal/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

// WorkflowHandler 工作流处理器
type WorkflowHandler struct {
	workflowService *service.WorkflowService
}

// NewWorkflowHandler 创建工作流处理器
func NewWorkflowHandler(workflowService *service.WorkflowService) *WorkflowHandler {
	return &WorkflowHandler{
		workflowService: workflowService,
	}
}

// GetWorkflowDefinitions 获取工作流定义列表
// @Summary 获取工作流定义列表
// @Description 获取工作流定义列表
// @Tags 工作流管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码"
// @Param page_size query int false "每页数量"
// @Param name query string false "名称"
// @Param category query string false "分类"
// @Param status query string false "状态"
// @Success 200 {object} models.Response{data=service.UserListResponse}
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /workflow/definitions [get]
func (h *WorkflowHandler) GetWorkflowDefinitions(c *gin.Context) {
	var req service.WorkflowDefinitionListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	resp, err := h.workflowService.GetWorkflowDefinitions(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "获取工作流定义列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    resp,
	})
}

// GetWorkflowDefinition 获取工作流定义详情
// @Summary 获取工作流定义详情
// @Description 获取工作流定义详情
// @Tags 工作流管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "工作流定义ID"
// @Success 200 {object} models.Response{data=models.WorkflowDefinition}
// @Failure 400 {object} models.Response
// @Failure 404 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /workflow/definitions/{id} [get]
func (h *WorkflowHandler) GetWorkflowDefinition(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "无效的ID",
		})
		return
	}

	definition, err := h.workflowService.GetWorkflowDefinition(uint(id))
	if err != nil {
		if err.Error() == "工作流定义不存在" {
			c.JSON(http.StatusNotFound, models.Response{
				Code:    404,
				Message: err.Error(),
			})
		} else {
			c.JSON(http.StatusInternalServerError, models.Response{
				Code:    500,
				Message: "获取工作流定义失败: " + err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    definition,
	})
}

// CreateWorkflowDefinition 创建工作流定义
// @Summary 创建工作流定义
// @Description 创建工作流定义
// @Tags 工作流管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param data body service.CreateWorkflowDefinitionRequest true "工作流定义信息"
// @Success 200 {object} models.Response{data=models.WorkflowDefinition}
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /workflow/definitions [post]
func (h *WorkflowHandler) CreateWorkflowDefinition(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.Response{
			Code:    401,
			Message: "未授权",
		})
		return
	}

	var req service.CreateWorkflowDefinitionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	definition, err := h.workflowService.CreateWorkflowDefinition(&req, userID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "创建工作流定义失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "创建成功",
		Data:    definition,
	})
}

// UpdateWorkflowDefinition 更新工作流定义
// @Summary 更新工作流定义
// @Description 更新工作流定义
// @Tags 工作流管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "工作流定义ID"
// @Param data body service.UpdateWorkflowDefinitionRequest true "工作流定义信息"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 404 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /workflow/definitions/{id} [put]
func (h *WorkflowHandler) UpdateWorkflowDefinition(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "无效的ID",
		})
		return
	}

	var req service.UpdateWorkflowDefinitionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	err = h.workflowService.UpdateWorkflowDefinition(uint(id), &req)
	if err != nil {
		if err.Error() == "工作流定义不存在" {
			c.JSON(http.StatusNotFound, models.Response{
				Code:    404,
				Message: err.Error(),
			})
		} else {
			c.JSON(http.StatusInternalServerError, models.Response{
				Code:    500,
				Message: "更新工作流定义失败: " + err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "更新成功",
	})
}

// DeleteWorkflowDefinition 删除工作流定义
// @Summary 删除工作流定义
// @Description 删除工作流定义
// @Tags 工作流管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "工作流定义ID"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 404 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /workflow/definitions/{id} [delete]
func (h *WorkflowHandler) DeleteWorkflowDefinition(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "无效的ID",
		})
		return
	}

	err = h.workflowService.DeleteWorkflowDefinition(uint(id))
	if err != nil {
		if err.Error() == "工作流定义不存在" {
			c.JSON(http.StatusNotFound, models.Response{
				Code:    404,
				Message: err.Error(),
			})
		} else {
			c.JSON(http.StatusInternalServerError, models.Response{
				Code:    500,
				Message: "删除工作流定义失败: " + err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "删除成功",
	})
}

// GetUserTodoTasks 获取用户待办任务
// @Summary 获取用户待办任务
// @Description 获取用户待办任务
// @Tags 工作流管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码"
// @Param page_size query int false "每页数量"
// @Param task_type query string false "任务类型"
// @Success 200 {object} models.Response{data=service.WorkflowTaskListResponse}
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /workflow/tasks/todo [get]
func (h *WorkflowHandler) GetUserTodoTasks(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.Response{
			Code:    401,
			Message: "未授权",
		})
		return
	}

	var req service.WorkflowTaskListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	resp, err := h.workflowService.GetUserTodoTasks(userID.(uint), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "获取待办任务失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    resp,
	})
}

// GetUserDoneTasks 获取用户已办任务
// @Summary 获取用户已办任务
// @Description 获取用户已办任务
// @Tags 工作流管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码"
// @Param page_size query int false "每页数量"
// @Param task_type query string false "任务类型"
// @Success 200 {object} models.Response{data=service.WorkflowTaskListResponse}
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /workflow/tasks/done [get]
func (h *WorkflowHandler) GetUserDoneTasks(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.Response{
			Code:    401,
			Message: "未授权",
		})
		return
	}

	var req service.WorkflowTaskListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	resp, err := h.workflowService.GetUserDoneTasks(userID.(uint), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "获取已办任务失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    resp,
	})
}

// GetWorkflowTasks 获取工作流任务列表
// @Summary 获取工作流任务列表
// @Description 获取工作流任务列表
// @Tags 工作流管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码"
// @Param page_size query int false "每页数量"
// @Param status query string false "状态"
// @Param assignee_id query int false "处理人ID"
// @Param task_type query string false "任务类型"
// @Success 200 {object} models.Response{data=service.UserListResponse}
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /workflow/tasks [get]
func (h *WorkflowHandler) GetWorkflowTasks(c *gin.Context) {
	var req service.WorkflowTaskListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	resp, err := h.workflowService.GetWorkflowTasks(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "获取工作流任务列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    resp,
	})
}
