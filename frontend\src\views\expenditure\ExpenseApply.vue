<template>
  <div class="expense-apply">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <a-button type="text" class="back-btn" @click="goBack">
            <ArrowLeftOutlined />
          </a-button>
          <div class="page-title-section">
            <h1 class="page-title">报销申请</h1>
            <p class="page-subtitle">创建新的费用报销申请</p>
          </div>
        </div>
        <div class="header-actions">
          <a-button @click="saveDraft" :loading="draftLoading">
            <template #icon><SaveOutlined /></template>
            保存草稿
          </a-button>
          <a-button type="primary" @click="handleSubmit" :loading="submitLoading">
            <template #icon><SendOutlined /></template>
            提交申请
          </a-button>
        </div>
      </div>
    </div>

    <!-- 进度指示器 -->
    <div class="progress-section">
      <a-steps :current="currentStep" size="small" class="form-steps">
        <a-step title="基本信息" description="填写申请基础信息" />
        <a-step title="费用明细" description="添加费用明细项目" />
        <a-step title="预算确认" description="确认预算占用情况" />
        <a-step title="提交审批" description="提交审批流程" />
      </a-steps>
    </div>

    <!-- 表单内容 -->
    <div class="form-container">

      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
        class="expense-form"
      >
        <!-- 基本信息卡片 -->
        <div class="form-section" v-show="currentStep === 0">
          <a-card class="form-card" :bordered="false">
            <template #title>
              <div class="card-title">
                <UserOutlined class="title-icon" />
                <span>基本信息</span>
              </div>
            </template>

            <div class="form-content">
              <a-row :gutter="[24, 16]">
                <a-col :xs="24" :sm="12" :lg="8">
                  <a-form-item label="申请人" name="applicant_name">
                    <a-input
                      v-model:value="formData.applicant_name"
                      disabled
                      class="readonly-input"
                    >
                      <template #prefix>
                        <UserOutlined />
                      </template>
                    </a-input>
                  </a-form-item>
                </a-col>
                <a-col :xs="24" :sm="12" :lg="8">
                  <a-form-item label="申请部门" name="department_name">
                    <a-input
                      v-model:value="formData.department_name"
                      disabled
                      class="readonly-input"
                    >
                      <template #prefix>
                        <ApartmentOutlined />
                      </template>
                    </a-input>
                  </a-form-item>
                </a-col>
                <a-col :xs="24" :sm="12" :lg="8">
                  <a-form-item label="申请日期" name="apply_date">
                    <a-date-picker
                      v-model:value="formData.apply_date"
                      style="width: 100%"
                      format="YYYY-MM-DD"
                      placeholder="选择申请日期"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="[24, 16]">
                <a-col :xs="24" :lg="12">
                  <a-form-item label="关联事前申请" name="pre_application_id">
                    <a-select
                      v-model:value="formData.pre_application_id"
                      placeholder="请选择事前申请单（可选）"
                      allow-clear
                      show-search
                      :filter-option="filterPreApplication"
                      @change="handlePreApplicationChange"
                    >
                      <template #suffixIcon>
                        <LinkOutlined />
                      </template>
                      <a-select-option v-for="app in preApplications" :key="app.id" :value="app.id">
                        <div class="pre-app-option">
                          <div class="option-title">{{ app.title }}</div>
                          <div class="option-meta">
                            <a-tag size="small" :color="getExpenseTypeColor(app.expense_type)">
                              {{ getExpenseTypeName(app.expense_type) }}
                            </a-tag>
                            <span class="option-amount">{{ formatMoney(app.amount) }}</span>
                          </div>
                        </div>
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :xs="24" :lg="12">
                  <a-form-item label="报销类型" name="expense_type">
                    <a-select
                      v-model:value="formData.expense_type"
                      placeholder="请选择报销类型"
                      @change="handleExpenseTypeChange"
                    >
                      <template #suffixIcon>
                        <TagOutlined />
                      </template>
                      <a-select-option value="travel">
                        <div class="expense-type-option">
                          <CarOutlined class="option-icon" />
                          <span>差旅费</span>
                        </div>
                      </a-select-option>
                      <a-select-option value="office">
                        <div class="expense-type-option">
                          <DesktopOutlined class="option-icon" />
                          <span>办公费</span>
                        </div>
                      </a-select-option>
                      <a-select-option value="training">
                        <div class="expense-type-option">
                          <BookOutlined class="option-icon" />
                          <span>培训费</span>
                        </div>
                      </a-select-option>
                      <a-select-option value="entertainment">
                        <div class="expense-type-option">
                          <CoffeeOutlined class="option-icon" />
                          <span>招待费</span>
                        </div>
                      </a-select-option>
                      <a-select-option value="other">
                        <div class="expense-type-option">
                          <EllipsisOutlined class="option-icon" />
                          <span>其他</span>
                        </div>
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>

              <a-form-item label="申请事由" name="reason">
                <a-textarea
                  v-model:value="formData.reason"
                  placeholder="请详细描述报销事由，包括时间、地点、目的等信息"
                  :rows="4"
                  show-count
                  :maxlength="500"
                />
              </a-form-item>
            </div>
          </a-card>
        </div>

        <!-- 费用明细卡片 -->
        <div class="form-section" v-show="currentStep === 1">
          <a-card class="form-card" :bordered="false">
            <template #title>
              <div class="card-title">
                <CalculatorOutlined class="title-icon" />
                <span>费用明细</span>
                <a-badge :count="formData.expense_items.length" class="title-badge" />
              </div>
            </template>

            <div class="form-content">
              <!-- 操作工具栏 -->
              <div class="expense-toolbar">
                <div class="toolbar-left">
                  <a-upload
                    :before-upload="handleOCRUpload"
                    :show-upload-list="false"
                    accept="image/*,.pdf"
                    :loading="ocrLoading"
                  >
                    <a-button type="primary" :loading="ocrLoading">
                      <template #icon><CameraOutlined /></template>
                      智能识别发票
                    </a-button>
                  </a-upload>
                  <a-button @click="addExpenseItem">
                    <template #icon><PlusOutlined /></template>
                    手动添加
                  </a-button>
                  <a-button @click="batchImport">
                    <template #icon><ImportOutlined /></template>
                    批量导入
                  </a-button>
                </div>
                <div class="toolbar-right">
                  <a-button
                    v-if="formData.expense_items.length > 0"
                    type="text"
                    danger
                    @click="clearAllItems"
                  >
                    <template #icon><DeleteOutlined /></template>
                    清空全部
                  </a-button>
                </div>
              </div>

              <!-- 费用明细表格 -->
              <div class="expense-table-container">
                <a-table
                  :columns="expenseColumns"
                  :data-source="formData.expense_items"
                  :pagination="false"
                  row-key="id"
                  size="middle"
                  class="expense-table"
                  :scroll="{ x: 1200 }"
                >
                  <template #bodyCell="{ column, record, index }">
                    <template v-if="column.key === 'index'">
                      <div class="row-index">{{ index + 1 }}</div>
                    </template>
                    <template v-else-if="column.key === 'expense_type'">
                      <a-select
                        v-model:value="record.expense_type"
                        style="width: 100%"
                        size="small"
                      >
                        <a-select-option value="travel">差旅费</a-select-option>
                        <a-select-option value="office">办公费</a-select-option>
                        <a-select-option value="training">培训费</a-select-option>
                        <a-select-option value="entertainment">招待费</a-select-option>
                        <a-select-option value="other">其他</a-select-option>
                      </a-select>
                    </template>
                    <template v-else-if="column.key === 'occur_date'">
                      <a-date-picker
                        v-model:value="record.occur_date"
                        style="width: 100%"
                        size="small"
                        format="YYYY-MM-DD"
                      />
                    </template>
                    <template v-else-if="column.key === 'invoice_no'">
                      <a-input
                        v-model:value="record.invoice_no"
                        placeholder="发票号码"
                        size="small"
                      />
                    </template>
                    <template v-else-if="column.key === 'amount'">
                      <a-input-number
                        v-model:value="record.amount"
                        :min="0"
                        :precision="2"
                        style="width: 100%"
                        size="small"
                        placeholder="0.00"
                        @change="calculateTotal"
                      >
                        <template #addonBefore>¥</template>
                      </a-input-number>
                    </template>
                    <template v-else-if="column.key === 'tax_amount'">
                      <a-input-number
                        v-model:value="record.tax_amount"
                        :min="0"
                        :precision="2"
                        style="width: 100%"
                        size="small"
                        placeholder="0.00"
                        @change="calculateTotal"
                      >
                        <template #addonBefore>¥</template>
                      </a-input-number>
                    </template>
                    <template v-else-if="column.key === 'remark'">
                      <a-input
                        v-model:value="record.remark"
                        placeholder="备注说明"
                        size="small"
                      />
                    </template>
                    <template v-else-if="column.key === 'action'">
                      <a-popconfirm
                        title="确定要删除这条明细吗？"
                        @confirm="removeExpenseItem(index)"
                      >
                        <a-button type="text" danger size="small">
                          <template #icon><DeleteOutlined /></template>
                        </a-button>
                      </a-popconfirm>
                    </template>
                  </template>

                  <template #emptyText>
                    <a-empty description="暂无费用明细">
                      <a-button type="primary" @click="addExpenseItem">
                        <template #icon><PlusOutlined /></template>
                        添加第一条明细
                      </a-button>
                    </a-empty>
                  </template>
                </a-table>
              </div>

              <!-- 费用汇总 -->
              <div class="expense-summary" v-if="formData.expense_items.length > 0">
                <div class="summary-header">
                  <h4>费用汇总</h4>
                </div>
                <a-row :gutter="[24, 16]">
                  <a-col :xs="12" :sm="6">
                    <div class="summary-item total">
                      <div class="summary-value">¥{{ expenseSummary.total_amount.toFixed(2) }}</div>
                      <div class="summary-label">费用总额</div>
                    </div>
                  </a-col>
                  <a-col :xs="12" :sm="6">
                    <div class="summary-item tax">
                      <div class="summary-value">¥{{ expenseSummary.total_tax.toFixed(2) }}</div>
                      <div class="summary-label">税额合计</div>
                    </div>
                  </a-col>
                  <a-col :xs="12" :sm="6">
                    <div class="summary-item count">
                      <div class="summary-value">{{ expenseSummary.item_count }}</div>
                      <div class="summary-label">明细条数</div>
                    </div>
                  </a-col>
                  <a-col :xs="12" :sm="6">
                    <div class="summary-item avg">
                      <div class="summary-value">¥{{ expenseSummary.avg_amount.toFixed(2) }}</div>
                      <div class="summary-label">平均金额</div>
                    </div>
                  </a-col>
                </a-row>
              </div>
            </div>
          </a-card>
        </div>

        <!-- 预算信息卡片 -->
        <div class="form-section" v-show="currentStep === 2">
          <a-card class="form-card" :bordered="false">
            <template #title>
              <div class="card-title">
                <FundOutlined class="title-icon" />
                <span>预算信息</span>
                <a-tag v-if="budgetInfo" :color="budgetInfo.is_sufficient ? 'green' : 'red'" class="title-badge">
                  {{ budgetInfo.is_sufficient ? '余额充足' : '余额不足' }}
                </a-tag>
              </div>
            </template>

            <div class="form-content">
              <a-row :gutter="[24, 16]">
                <a-col :xs="24" :lg="12">
                  <a-form-item label="预算项目" name="budget_item_id">
                    <a-tree-select
                      v-model:value="formData.budget_item_id"
                      :tree-data="budgetItems"
                      :field-names="{ children: 'children', label: 'name', value: 'id' }"
                      placeholder="请选择预算项目"
                      allow-clear
                      show-search
                      tree-default-expand-all
                      @change="handleBudgetItemChange"
                    >
                      <template #suffixIcon>
                        <FundOutlined />
                      </template>
                    </a-tree-select>
                  </a-form-item>
                </a-col>
                <a-col :xs="24" :lg="12">
                  <a-form-item label="成本中心" name="cost_center">
                    <a-select
                      v-model:value="formData.cost_center"
                      placeholder="请选择成本中心"
                      show-search
                      :filter-option="filterCostCenter"
                    >
                      <template #suffixIcon>
                        <BankOutlined />
                      </template>
                      <a-select-option v-for="center in costCenters" :key="center.id" :value="center.id">
                        <div class="cost-center-option">
                          <div class="option-name">{{ center.name }}</div>
                          <div class="option-code">{{ center.code }}</div>
                        </div>
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>

              <!-- 预算占用展示 -->
              <div v-if="budgetInfo" class="budget-occupation">
                <div class="budget-alert">
                  <a-alert
                    :type="budgetInfo.is_sufficient ? 'success' : 'warning'"
                    :message="budgetInfo.is_sufficient ? '预算余额充足，可以正常提交' : '预算余额不足，请谨慎提交'"
                    :description="getBudgetAlertDescription()"
                    show-icon
                    closable
                  />
                </div>

                <div class="budget-details">
                  <div class="budget-progress">
                    <div class="progress-header">
                      <span class="progress-title">预算执行进度</span>
                      <span class="progress-percentage">{{ getBudgetUsagePercentage() }}%</span>
                    </div>
                    <a-progress
                      :percent="getBudgetUsagePercentage()"
                      :stroke-color="getBudgetProgressColor()"
                      :show-info="false"
                      stroke-width={8}
                    />
                  </div>

                  <a-row :gutter="[24, 16]" class="budget-stats">
                    <a-col :xs="12" :sm="6">
                      <div class="budget-stat-item total">
                        <div class="stat-value">¥{{ formatNumber(budgetInfo.total_budget) }}</div>
                        <div class="stat-label">预算总额</div>
                      </div>
                    </a-col>
                    <a-col :xs="12" :sm="6">
                      <div class="budget-stat-item used">
                        <div class="stat-value">¥{{ formatNumber(budgetInfo.used_budget) }}</div>
                        <div class="stat-label">已使用</div>
                      </div>
                    </a-col>
                    <a-col :xs="12" :sm="6">
                      <div class="budget-stat-item available">
                        <div class="stat-value">¥{{ formatNumber(budgetInfo.available_budget) }}</div>
                        <div class="stat-label">可用余额</div>
                      </div>
                    </a-col>
                    <a-col :xs="12" :sm="6">
                      <div class="budget-stat-item current">
                        <div class="stat-value">¥{{ formatNumber(expenseSummary.total_amount) }}</div>
                        <div class="stat-label">本次占用</div>
                      </div>
                    </a-col>
                  </a-row>
                </div>
              </div>

              <div v-else-if="formData.budget_item_id" class="budget-loading">
                <a-spin>
                  <div class="loading-text">正在加载预算信息...</div>
                </a-spin>
              </div>
            </div>
          </a-card>
        </div>

        <!-- 附件上传和审批流程卡片 -->
        <div class="form-section" v-show="currentStep === 3">
          <a-row :gutter="[24, 24]">
            <!-- 附件上传 -->
            <a-col :xs="24" :lg="12">
              <a-card class="form-card" :bordered="false">
                <template #title>
                  <div class="card-title">
                    <PaperClipOutlined class="title-icon" />
                    <span>附件上传</span>
                    <a-badge :count="formData.attachments.length" class="title-badge" />
                  </div>
                </template>

                <div class="form-content">
                  <a-upload
                    v-model:file-list="formData.attachments"
                    :before-upload="beforeUpload"
                    multiple
                    :max-count="10"
                    list-type="picture-card"
                    class="upload-container"
                  >
                    <div class="upload-trigger">
                      <PlusOutlined />
                      <div class="upload-text">上传附件</div>
                    </div>
                  </a-upload>

                  <div class="upload-tips">
                    <a-typography-text type="secondary" class="tips-text">
                      <InfoCircleOutlined class="tips-icon" />
                      支持格式：PDF、JPG、PNG、Excel、Word，单个文件不超过10MB，最多上传10个文件
                    </a-typography-text>
                  </div>
                </div>
              </a-card>
            </a-col>

            <!-- 审批流程预览 -->
            <a-col :xs="24" :lg="12">
              <a-card class="form-card" :bordered="false">
                <template #title>
                  <div class="card-title">
                    <NodeIndexOutlined class="title-icon" />
                    <span>审批流程</span>
                    <a-tag color="blue" class="title-badge">{{ approvalFlow.length }}个节点</a-tag>
                  </div>
                </template>

                <div class="form-content">
                  <div class="approval-flow">
                    <a-timeline class="flow-timeline">
                      <a-timeline-item
                        v-for="(step, index) in approvalFlow"
                        :key="index"
                        :color="getTimelineColor(step.status)"
                      >
                        <template #dot>
                          <div class="timeline-dot">
                            <component :is="getTimelineIcon(step.status)" />
                          </div>
                        </template>
                        <div class="timeline-content">
                          <div class="step-header">
                            <h4 class="step-name">{{ step.name }}</h4>
                            <a-tag :color="getStepStatusColor(step.status)" size="small">
                              {{ getStepStatusText(step.status) }}
                            </a-tag>
                          </div>
                          <div class="step-details">
                            <div class="step-approver">
                              <UserOutlined class="detail-icon" />
                              <span>{{ step.approver }}</span>
                            </div>
                            <div v-if="step.estimated_time" class="step-time">
                              <ClockCircleOutlined class="detail-icon" />
                              <span>预计用时：{{ step.estimated_time }}</span>
                            </div>
                          </div>
                        </div>
                      </a-timeline-item>
                    </a-timeline>
                  </div>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 步骤导航 -->
        <div class="step-navigation">
          <div class="nav-content">
            <div class="nav-left">
              <a-button v-if="currentStep > 0" @click="prevStep">
                <template #icon><LeftOutlined /></template>
                上一步
              </a-button>
            </div>
            <div class="nav-right">
              <a-button v-if="currentStep < 3" type="primary" @click="nextStep">
                下一步
                <template #icon><RightOutlined /></template>
              </a-button>
              <a-button v-else type="primary" @click="handleSubmit" :loading="submitLoading">
                <template #icon><SendOutlined /></template>
                提交申请
              </a-button>
            </div>
          </div>
        </div>
      </a-form>
    </div>

    <!-- OCR识别结果模态框 -->
    <a-modal
      v-model:open="ocrModalVisible"
      title="OCR识别结果"
      width="800px"
      @ok="confirmOCRResult"
      @cancel="cancelOCRResult"
    >
      <a-table
        :columns="ocrColumns"
        :data-source="ocrResults"
        :pagination="false"
        size="small"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'selected'">
            <a-checkbox v-model:checked="record.selected" />
          </template>
          <template v-else-if="column.key === 'amount'">
            <a-input-number v-model:value="record.amount" :precision="2" />
          </template>
          <template v-else-if="column.key === 'tax_amount'">
            <a-input-number v-model:value="record.tax_amount" :precision="2" />
          </template>
        </template>
      </a-table>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  ArrowLeftOutlined,
  CameraOutlined,
  PlusOutlined,
  ImportOutlined,
  UploadOutlined,
  SaveOutlined,
  SendOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
  ApartmentOutlined,
  LinkOutlined,
  TagOutlined,
  CarOutlined,
  DesktopOutlined,
  BookOutlined,
  CoffeeOutlined,
  EllipsisOutlined,
  CalculatorOutlined,
  DeleteOutlined,
  FundOutlined,
  BankOutlined,
  PaperClipOutlined,
  InfoCircleOutlined,
  NodeIndexOutlined,
  LeftOutlined,
  RightOutlined
} from '@ant-design/icons-vue'
import {
  getPreApplications,
  getBudgetItems,
  getCostCenters,
  getBudgetInfo,
  getApprovalFlow,
  createExpenseApplication,
  saveDraftExpenseApplication,
  ocrInvoiceRecognition
} from '@/api/expenditure'

const router = useRouter()

// 响应式数据
const formRef = ref()
const submitLoading = ref(false)
const draftLoading = ref(false)
const ocrLoading = ref(false)
const ocrModalVisible = ref(false)
const currentStep = ref(0)
const preApplications = ref([])
const budgetItems = ref([])
const costCenters = ref([])
const budgetInfo = ref(null)
const approvalFlow = ref([])
const ocrResults = ref([])

// 表单数据
const formData = reactive({
  applicant_name: '当前用户',
  department_name: '当前部门',
  apply_date: dayjs(),
  pre_application_id: null,
  expense_type: null,
  reason: '',
  budget_item_id: null,
  cost_center: null,
  expense_items: [],
  attachments: []
})

// 计算属性
const expenseSummary = computed(() => {
  const items = formData.expense_items
  const total_amount = items.reduce((sum, item) => sum + (item.amount || 0), 0)
  const total_tax = items.reduce((sum, item) => sum + (item.tax_amount || 0), 0)
  const item_count = items.length
  const avg_amount = item_count > 0 ? total_amount / item_count : 0
  
  return {
    total_amount,
    total_tax,
    item_count,
    avg_amount
  }
})

// 表格列配置
const expenseColumns = [
  { title: '序号', key: 'index', width: 60, align: 'center' },
  { title: '费用类型', key: 'expense_type', width: 120 },
  { title: '发生日期', key: 'occur_date', width: 130 },
  { title: '发票号码', key: 'invoice_no', width: 150 },
  { title: '金额', key: 'amount', width: 120, align: 'right' },
  { title: '税额', key: 'tax_amount', width: 120, align: 'right' },
  { title: '备注', key: 'remark', width: 200 },
  { title: '操作', key: 'action', width: 80, align: 'center' }
]

const ocrColumns = [
  { title: '选择', key: 'selected', width: 60 },
  { title: '发票号码', dataIndex: 'invoice_no', key: 'invoice_no' },
  { title: '开票日期', dataIndex: 'invoice_date', key: 'invoice_date' },
  { title: '金额', key: 'amount' },
  { title: '税额', key: 'tax_amount' },
  { title: '开票单位', dataIndex: 'company', key: 'company' }
]

// 表单验证规则
const formRules = {
  apply_date: [{ required: true, message: '请选择申请日期', trigger: 'change' }],
  expense_type: [{ required: true, message: '请选择报销类型', trigger: 'change' }],
  reason: [{ required: true, message: '请输入申请事由', trigger: 'blur' }],
  budget_item_id: [{ required: true, message: '请选择预算项目', trigger: 'change' }]
}

// 工具函数
const formatMoney = (amount) => {
  return (amount / 100).toFixed(2) + '元'
}

const formatNumber = (num) => {
  return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

const getExpenseTypeColor = (type) => {
  const colors = {
    'travel': 'blue',
    'office': 'green',
    'training': 'orange',
    'entertainment': 'purple',
    'other': 'default'
  }
  return colors[type] || 'default'
}

const getExpenseTypeName = (type) => {
  const names = {
    'travel': '差旅费',
    'office': '办公费',
    'training': '培训费',
    'entertainment': '招待费',
    'other': '其他'
  }
  return names[type] || '未知'
}

const getTimelineColor = (status) => {
  const colors = {
    'pending': 'blue',
    'current': 'orange',
    'completed': 'green',
    'rejected': 'red'
  }
  return colors[status] || 'gray'
}

const getTimelineIcon = (status) => {
  const icons = {
    'pending': ClockCircleOutlined,
    'current': ExclamationCircleOutlined,
    'completed': CheckCircleOutlined,
    'rejected': ExclamationCircleOutlined
  }
  return icons[status] || ClockCircleOutlined
}

const getStepStatusColor = (status) => {
  const colors = {
    'pending': 'default',
    'current': 'processing',
    'completed': 'success',
    'rejected': 'error'
  }
  return colors[status] || 'default'
}

const getStepStatusText = (status) => {
  const texts = {
    'pending': '待审批',
    'current': '审批中',
    'completed': '已完成',
    'rejected': '已拒绝'
  }
  return texts[status] || '未知'
}

const getBudgetUsagePercentage = () => {
  if (!budgetInfo.value) return 0
  return Math.round((budgetInfo.value.used_budget / budgetInfo.value.total_budget) * 100)
}

const getBudgetProgressColor = () => {
  const percentage = getBudgetUsagePercentage()
  if (percentage >= 90) return '#ff4d4f'
  if (percentage >= 80) return '#faad14'
  return '#52c41a'
}

const getBudgetAlertDescription = () => {
  if (!budgetInfo.value) return ''
  const remaining = budgetInfo.value.available_budget - expenseSummary.value.total_amount
  if (remaining >= 0) {
    return `提交后剩余预算：¥${formatNumber(remaining)}`
  } else {
    return `超出预算：¥${formatNumber(Math.abs(remaining))}，需要额外审批`
  }
}

// 筛选函数
const filterPreApplication = (input, option) => {
  return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const filterCostCenter = (input, option) => {
  return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 数据加载函数
const loadPreApplications = async () => {
  try {
    const response = await getPreApplications({ status: 'approved' })
    preApplications.value = response.data || []
  } catch (error) {
    message.error('加载事前申请失败')
  }
}

const loadBudgetItems = async () => {
  try {
    const response = await getBudgetItems()
    budgetItems.value = response.data || []
  } catch (error) {
    message.error('加载预算项目失败')
  }
}

const loadCostCenters = async () => {
  try {
    const response = await getCostCenters()
    costCenters.value = response.data || []
  } catch (error) {
    message.error('加载成本中心失败')
  }
}

const loadApprovalFlow = async () => {
  try {
    const response = await getApprovalFlow({
      expense_type: formData.expense_type,
      amount: expenseSummary.value.total_amount
    })
    approvalFlow.value = response.data || []
  } catch (error) {
    message.error('加载审批流程失败')
  }
}

// 步骤导航函数
const nextStep = () => {
  if (currentStep.value < 3) {
    if (validateCurrentStep()) {
      currentStep.value++
      if (currentStep.value === 2 && formData.budget_item_id) {
        handleBudgetItemChange(formData.budget_item_id)
      }
      if (currentStep.value === 3) {
        loadApprovalFlow()
      }
    }
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const validateCurrentStep = () => {
  switch (currentStep.value) {
    case 0:
      if (!formData.expense_type) {
        message.error('请选择报销类型')
        return false
      }
      if (!formData.reason.trim()) {
        message.error('请输入申请事由')
        return false
      }
      return true
    case 1:
      if (formData.expense_items.length === 0) {
        message.error('请至少添加一条费用明细')
        return false
      }
      return true
    case 2:
      if (!formData.budget_item_id) {
        message.error('请选择预算项目')
        return false
      }
      return true
    default:
      return true
  }
}

// 事件处理函数
const goBack = () => {
  router.go(-1)
}

const handleExpenseTypeChange = (value) => {
  // 更新现有明细的费用类型
  formData.expense_items.forEach(item => {
    if (!item.expense_type) {
      item.expense_type = value
    }
  })
  loadApprovalFlow()
}

const handlePreApplicationChange = (value) => {
  if (value) {
    const preApp = preApplications.value.find(app => app.id === value)
    if (preApp) {
      // 自动填充相关信息
      formData.expense_type = preApp.expense_type
      formData.reason = preApp.reason
      formData.budget_item_id = preApp.budget_item_id

      // 填充费用明细
      formData.expense_items = preApp.expense_items || []
      calculateTotal()
    }
  }
}

const handleBudgetItemChange = async (value) => {
  if (value) {
    try {
      const response = await getBudgetInfo(value)
      budgetInfo.value = response.data
    } catch (error) {
      message.error('获取预算信息失败')
      budgetInfo.value = null
    }
  } else {
    budgetInfo.value = null
  }
}

const addExpenseItem = () => {
  formData.expense_items.push({
    id: Date.now() + Math.random(),
    expense_type: formData.expense_type || 'other',
    occur_date: dayjs(),
    invoice_no: '',
    amount: 0,
    tax_amount: 0,
    remark: ''
  })
}

const removeExpenseItem = (index) => {
  formData.expense_items.splice(index, 1)
  calculateTotal()
}

const clearAllItems = () => {
  formData.expense_items = []
  calculateTotal()
  message.success('已清空所有费用明细')
}

const calculateTotal = () => {
  // 触发计算属性更新
  if (formData.expense_type && expenseSummary.value.total_amount > 0) {
    loadApprovalFlow()
  }
}

const handleOCRUpload = async (file) => {
  ocrLoading.value = true
  try {
    const response = await ocrInvoiceRecognition(file)
    ocrResults.value = response.data.map(item => ({
      ...item,
      selected: true,
      id: Date.now() + Math.random()
    }))
    ocrModalVisible.value = true
    message.success(`识别到 ${response.data.length} 张发票`)
  } catch (error) {
    message.error('OCR识别失败，请检查图片质量或网络连接')
  } finally {
    ocrLoading.value = false
  }
  return false // 阻止自动上传
}

const confirmOCRResult = () => {
  const selectedResults = ocrResults.value.filter(item => item.selected)
  selectedResults.forEach(result => {
    formData.expense_items.push({
      id: Date.now() + Math.random(),
      expense_type: formData.expense_type || 'other',
      occur_date: dayjs(result.invoice_date),
      invoice_no: result.invoice_no,
      amount: result.amount || 0,
      tax_amount: result.tax_amount || 0,
      remark: result.company || ''
    })
  })

  ocrModalVisible.value = false
  ocrResults.value = []
  calculateTotal()
  message.success(`成功添加 ${selectedResults.length} 条费用明细`)
}

const cancelOCRResult = () => {
  ocrModalVisible.value = false
  ocrResults.value = []
}

const batchImport = () => {
  message.info('批量导入功能开发中，敬请期待...')
}

const beforeUpload = (file) => {
  const isValidType = [
    'image/jpeg',
    'image/png',
    'application/pdf',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ].includes(file.type)

  if (!isValidType) {
    message.error('只能上传 JPG/PNG/PDF/Excel/Word 格式的文件!')
    return false
  }

  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过 10MB!')
    return false
  }

  return false // 阻止自动上传，手动处理
}

const saveDraft = async () => {
  draftLoading.value = true
  try {
    await saveDraftExpenseApplication(formData)
    message.success('草稿保存成功')
  } catch (error) {
    message.error('保存草稿失败')
  } finally {
    draftLoading.value = false
  }
}

const handleSubmit = async () => {
  // 最终验证
  if (!validateCurrentStep()) {
    return
  }

  if (formData.expense_items.length === 0) {
    message.error('请至少添加一条费用明细')
    return
  }

  if (!formData.budget_item_id) {
    message.error('请选择预算项目')
    return
  }

  // 预算不足警告
  if (!budgetInfo.value?.is_sufficient) {
    const confirmed = await new Promise((resolve) => {
      Modal.confirm({
        title: '预算余额不足',
        content: '当前预算余额不足以支付本次申请金额，是否继续提交？',
        onOk: () => resolve(true),
        onCancel: () => resolve(false)
      })
    })
    if (!confirmed) return
  }

  submitLoading.value = true
  try {
    await createExpenseApplication(formData)
    message.success('申请提交成功，请等待审批')
    router.push('/expenditure/list')
  } catch (error) {
    message.error('提交失败，请重试')
  } finally {
    submitLoading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadPreApplications()
  loadBudgetItems()
  loadCostCenters()
})
</script>

<style scoped>
/* 报销申请页面 - 企业级设计 */
.expense-apply {
  background: var(--bg-secondary);
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-xl);
  max-width: 1400px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.back-btn {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.back-btn:hover {
  background: var(--bg-hover);
  color: var(--primary-color);
}

.page-title-section {
  display: flex;
  flex-direction: column;
}

.page-title {
  margin: 0;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
}

.page-subtitle {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
  margin-top: 2px;
}

.header-actions {
  display: flex;
  gap: var(--spacing-md);
}

/* 进度指示器 */
.progress-section {
  background: var(--bg-primary);
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid var(--border-light);
}

.form-steps {
  max-width: 800px;
  margin: 0 auto;
}

.form-steps :deep(.ant-steps-item-title) {
  font-weight: var(--font-weight-medium);
}

/* 表单容器 */
.form-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--spacing-xl);
}

.expense-form {
  width: 100%;
}

.form-section {
  margin-bottom: var(--spacing-xl);
}

/* 表单卡片 */
.form-card {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
}

.form-card:hover {
  box-shadow: var(--shadow-lg);
}

.card-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.title-icon {
  color: var(--primary-color);
  font-size: 16px;
}

.title-badge {
  margin-left: auto;
}

.form-content {
  padding: var(--spacing-lg);
}

/* 只读输入框 */
.readonly-input {
  background: var(--bg-tertiary) !important;
  color: var(--text-secondary) !important;
}

/* 选择器选项样式 */
.pre-app-option {
  padding: var(--spacing-xs) 0;
}

.option-title {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: 2px;
}

.option-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.option-amount {
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
}

.expense-type-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.option-icon {
  color: var(--primary-color);
}

.cost-center-option {
  display: flex;
  flex-direction: column;
}

.option-name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.option-code {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

/* 费用明细样式 */
.expense-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
}

.toolbar-left {
  display: flex;
  gap: var(--spacing-sm);
}

.toolbar-right {
  display: flex;
  gap: var(--spacing-sm);
}

.expense-table-container {
  margin-bottom: var(--spacing-lg);
  border-radius: var(--radius-md);
  overflow: hidden;
  border: 1px solid var(--border-light);
}

.expense-table {
  background: var(--bg-primary);
}

.expense-table :deep(.ant-table-thead > tr > th) {
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-light);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.expense-table :deep(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid var(--border-light);
}

.expense-table :deep(.ant-table-tbody > tr:hover > td) {
  background: var(--bg-hover);
}

.row-index {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--primary-color-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  margin: 0 auto;
}

/* 费用汇总样式 */
.expense-summary {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-tertiary) 100%);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  border: 1px solid var(--border-light);
}

.summary-header {
  margin-bottom: var(--spacing-md);
}

.summary-header h4 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.summary-item {
  text-align: center;
  padding: var(--spacing-md);
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  transition: all 0.2s ease;
}

.summary-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.summary-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-xs);
}

.summary-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.summary-item.total .summary-value {
  color: var(--primary-color);
}

.summary-item.tax .summary-value {
  color: var(--warning-color);
}

.summary-item.count .summary-value {
  color: var(--success-color);
}

.summary-item.avg .summary-value {
  color: var(--info-color);
}

/* 预算信息样式 */
.budget-occupation {
  margin-top: var(--spacing-lg);
}

.budget-alert {
  margin-bottom: var(--spacing-lg);
}

.budget-details {
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
}

.budget-progress {
  margin-bottom: var(--spacing-lg);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.progress-title {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.progress-percentage {
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
}

.budget-stats {
  margin-top: var(--spacing-md);
}

.budget-stat-item {
  text-align: center;
  padding: var(--spacing-md);
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  transition: all 0.2s ease;
}

.budget-stat-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.budget-stat-item.total .stat-value {
  color: var(--primary-color);
}

.budget-stat-item.used .stat-value {
  color: var(--warning-color);
}

.budget-stat-item.available .stat-value {
  color: var(--success-color);
}

.budget-stat-item.current .stat-value {
  color: var(--error-color);
}

.budget-loading {
  text-align: center;
  padding: var(--spacing-2xl);
}

.loading-text {
  margin-top: var(--spacing-md);
  color: var(--text-secondary);
}

/* 附件上传样式 */
.upload-container {
  margin-bottom: var(--spacing-md);
}

.upload-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  border: 2px dashed var(--border-medium);
  border-radius: var(--radius-md);
  background: var(--bg-tertiary);
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-trigger:hover {
  border-color: var(--primary-color);
  background: var(--primary-color-light);
}

.upload-text {
  margin-top: var(--spacing-sm);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.upload-tips {
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.tips-text {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-xs);
}

.tips-icon {
  color: var(--info-color);
}

/* 审批流程样式 */
.approval-flow {
  max-height: 400px;
  overflow-y: auto;
}

.flow-timeline {
  padding: var(--spacing-md);
}

.timeline-dot {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--bg-primary);
  border: 2px solid var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 14px;
}

.timeline-content {
  padding-left: var(--spacing-md);
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.step-name {
  margin: 0;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.step-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.step-approver,
.step-time {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.detail-icon {
  color: var(--text-tertiary);
  font-size: 12px;
}

/* 步骤导航 */
.step-navigation {
  background: var(--bg-primary);
  border-top: 1px solid var(--border-light);
  padding: var(--spacing-lg) var(--spacing-xl);
  position: sticky;
  bottom: 0;
  z-index: 100;
}

.nav-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.nav-left,
.nav-right {
  display: flex;
  gap: var(--spacing-md);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .form-container {
    padding: var(--spacing-lg);
  }

  .header-content {
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .progress-section {
    padding: var(--spacing-md) var(--spacing-lg);
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .form-container {
    padding: var(--spacing-md);
  }

  .toolbar-left {
    flex-wrap: wrap;
  }

  .expense-table-container {
    overflow-x: auto;
  }

  .budget-stats {
    margin-top: var(--spacing-sm);
  }

  .nav-content {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .nav-left,
  .nav-right {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .page-title {
    font-size: var(--font-size-xl);
  }

  .form-content {
    padding: var(--spacing-md);
  }

  .toolbar-left {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .summary-item,
  .budget-stat-item {
    padding: var(--spacing-sm);
  }

  .summary-value,
  .stat-value {
    font-size: var(--font-size-md);
  }
}
</style>
