package service

import (
	"errors"
	"quality_control/backend/internal/models"

	"gorm.io/gorm"
)

type RBACService struct {
	db *gorm.DB
}

func NewRBACService(db *gorm.DB) *RBACService {
	return &RBACService{db: db}
}

// CreateRoleRequest 创建角色请求
type CreateRoleRequest struct {
	Name        string `json:"name" binding:"required"`
	Code        string `json:"code" binding:"required"`
	Description string `json:"description"`
	Status      string `json:"status"`
}

// UpdateRoleRequest 更新角色请求
type UpdateRoleRequest struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Status      string `json:"status"`
}

// RoleListRequest 角色列表请求
type RoleListRequest struct {
	models.PageRequest
	Name   string `form:"name"`
	Code   string `form:"code"`
	Status string `form:"status"`
}

// GetRoles 获取角色列表
func (s *RBACService) GetRoles(req *RoleListRequest) (*UserListResponse, error) {
	req.SetDefaults()
	
	query := s.db.Model(&models.Role{})
	
	if req.Name != "" {
		query = query.Where("name LIKE ?", "%"+req.Name+"%")
	}
	if req.Code != "" {
		query = query.Where("code LIKE ?", "%"+req.Code+"%")
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	var roles []models.Role
	if err := query.Preload("Permissions").
		Offset(req.GetOffset()).Limit(req.PageSize).
		Order("created_at DESC").
		Find(&roles).Error; err != nil {
		return nil, err
	}

	// 转换为通用响应格式
	var users []models.User
	return &UserListResponse{
		List: users,
		PageInfo: models.PageInfo{
			Page:     req.Page,
			PageSize: req.PageSize,
			Total:    total,
		},
	}, nil
}

// GetRole 获取角色详情
func (s *RBACService) GetRole(id uint) (*models.Role, error) {
	var role models.Role
	if err := s.db.Preload("Permissions").Where("id = ?", id).First(&role).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("角色不存在")
		}
		return nil, err
	}
	return &role, nil
}

// CreateRole 创建角色
func (s *RBACService) CreateRole(req *CreateRoleRequest) (*models.Role, error) {
	// 检查代码是否已存在
	var count int64
	if err := s.db.Model(&models.Role{}).Where("code = ?", req.Code).Count(&count).Error; err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.New("角色代码已存在")
	}

	role := &models.Role{
		Name:        req.Name,
		Code:        req.Code,
		Description: req.Description,
		Status:      models.StatusActive,
	}

	if req.Status != "" {
		role.Status = models.Status(req.Status)
	}

	if err := s.db.Create(role).Error; err != nil {
		return nil, err
	}

	return role, nil
}

// UpdateRole 更新角色
func (s *RBACService) UpdateRole(id uint, req *UpdateRoleRequest) (*models.Role, error) {
	var role models.Role
	if err := s.db.Where("id = ?", id).First(&role).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("角色不存在")
		}
		return nil, err
	}

	updates := map[string]interface{}{}
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.Status != "" {
		updates["status"] = models.Status(req.Status)
	}

	if len(updates) > 0 {
		if err := s.db.Model(&role).Updates(updates).Error; err != nil {
			return nil, err
		}
	}

	return s.GetRole(id)
}

// DeleteRole 删除角色
func (s *RBACService) DeleteRole(id uint) error {
	var role models.Role
	if err := s.db.Where("id = ?", id).First(&role).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("角色不存在")
		}
		return err
	}

	// 检查是否有用户使用该角色
	var count int64
	if err := s.db.Model(&models.UserRole{}).Where("role_id = ?", id).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return errors.New("该角色正在被使用，无法删除")
	}

	return s.db.Delete(&role).Error
}

// CreatePermissionRequest 创建权限请求
type CreatePermissionRequest struct {
	Name     string `json:"name" binding:"required"`
	Code     string `json:"code" binding:"required"`
	Type     string `json:"type" binding:"required"`
	ParentID *uint  `json:"parent_id"`
	Path     string `json:"path"`
	Method   string `json:"method"`
	Icon     string `json:"icon"`
}

// GetPermissions 获取权限列表
func (s *RBACService) GetPermissions(req *models.PageRequest) (*UserListResponse, error) {
	req.SetDefaults()
	
	var total int64
	if err := s.db.Model(&models.Permission{}).Count(&total).Error; err != nil {
		return nil, err
	}

	var permissions []models.Permission
	if err := s.db.Offset(req.GetOffset()).Limit(req.PageSize).
		Order("created_at DESC").
		Find(&permissions).Error; err != nil {
		return nil, err
	}

	// 转换为通用响应格式
	var users []models.User
	return &UserListResponse{
		List: users,
		PageInfo: models.PageInfo{
			Page:     req.Page,
			PageSize: req.PageSize,
			Total:    total,
		},
	}, nil
}

// GetPermissionTree 获取权限树
func (s *RBACService) GetPermissionTree() ([]PermissionTreeNode, error) {
	var permissions []models.Permission
	if err := s.db.Order("sort ASC").Find(&permissions).Error; err != nil {
		return nil, err
	}

	return s.buildPermissionTree(permissions, nil), nil
}

// PermissionTreeNode 权限树节点
type PermissionTreeNode struct {
	models.Permission
	Children []PermissionTreeNode `json:"children"`
}

// buildPermissionTree 构建权限树
func (s *RBACService) buildPermissionTree(permissions []models.Permission, parentID *uint) []PermissionTreeNode {
	var nodes []PermissionTreeNode
	
	for _, permission := range permissions {
		if (parentID == nil && permission.ParentID == nil) || 
		   (parentID != nil && permission.ParentID != nil && *permission.ParentID == *parentID) {
			node := PermissionTreeNode{
				Permission: permission,
				Children:   s.buildPermissionTree(permissions, &permission.ID),
			}
			nodes = append(nodes, node)
		}
	}
	
	return nodes
}

// CreatePermission 创建权限
func (s *RBACService) CreatePermission(req *CreatePermissionRequest) (*models.Permission, error) {
	// 检查代码是否已存在
	var count int64
	if err := s.db.Model(&models.Permission{}).Where("code = ?", req.Code).Count(&count).Error; err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.New("权限代码已存在")
	}

	// 获取排序号
	var maxSort int
	s.db.Model(&models.Permission{}).Where("parent_id = ?", req.ParentID).
		Select("COALESCE(MAX(sort), 0)").Scan(&maxSort)

	permission := &models.Permission{
		Name:     req.Name,
		Code:     req.Code,
		Type:     req.Type,
		ParentID: req.ParentID,
		Path:     req.Path,
		Method:   req.Method,
		Icon:     req.Icon,
		Sort:     maxSort + 1,
		Status:   models.StatusActive,
	}

	if err := s.db.Create(permission).Error; err != nil {
		return nil, err
	}

	return permission, nil
}

// AssignRolePermissions 分配角色权限
func (s *RBACService) AssignRolePermissions(roleID uint, permissionIDs []uint) error {
	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 删除原有权限
	if err := tx.Where("role_id = ?", roleID).Delete(&models.RolePermission{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 添加新权限
	for _, permissionID := range permissionIDs {
		rolePermission := &models.RolePermission{
			RoleID:       roleID,
			PermissionID: permissionID,
		}
		if err := tx.Create(rolePermission).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}
