import request from './request'

// 系统配置管理
export const getSystemConfigs = (params) => {
  return request.get('/system/configs', { params })
}

export const getSystemConfig = (key) => {
  return request.get(`/system/configs/${key}`)
}

export const createSystemConfig = (data) => {
  return request.post('/system/configs', data)
}

export const updateSystemConfig = (id, data) => {
  return request.put(`/system/configs/${id}`, data)
}

export const deleteSystemConfig = (id) => {
  return request.delete(`/system/configs/${id}`)
}

export const updateSystemConfigByKey = (key, value) => {
  return request.put(`/system/configs/key/${key}`, { config_value: value })
}

// 通知管理
export const getNotifications = (params) => {
  return request.get('/system/notifications', { params })
}

export const getNotification = (id) => {
  return request.get(`/system/notifications/${id}`)
}

export const createNotification = (data) => {
  return request.post('/system/notifications', data)
}

export const updateNotification = (id, data) => {
  return request.put(`/system/notifications/${id}`, data)
}

export const deleteNotification = (id) => {
  return request.delete(`/system/notifications/${id}`)
}

export const markNotificationAsRead = (id) => {
  return request.put(`/system/notifications/${id}/read`)
}

export const markAllNotificationsAsRead = () => {
  return request.put('/system/notifications/read-all')
}

export const getUnreadNotificationCount = () => {
  return request.get('/system/notifications/unread-count')
}

// 审计日志管理
export const getAuditLogs = (params) => {
  return request.get('/system/audit-logs', { params })
}

export const getAuditLog = (id) => {
  return request.get(`/system/audit-logs/${id}`)
}

export const exportAuditLogs = (params) => {
  return request.get('/system/audit-logs/export', { 
    params,
    responseType: 'blob'
  })
}

// 文件上传管理
export const getFileUploads = (params) => {
  return request.get('/system/files', { params })
}

export const uploadFile = (file, businessType, businessId) => {
  const formData = new FormData()
  formData.append('file', file)
  if (businessType) formData.append('business_type', businessType)
  if (businessId) formData.append('business_id', businessId)
  
  return request.post('/system/files/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

export const deleteFile = (id) => {
  return request.delete(`/system/files/${id}`)
}

export const downloadFile = (id) => {
  return request.get(`/system/files/${id}/download`, {
    responseType: 'blob'
  })
}

// 系统统计
export const getSystemStats = () => {
  return request.get('/system/stats')
}

export const getDashboardData = () => {
  return request.get('/system/dashboard')
}

// 系统健康检查
export const getSystemHealth = () => {
  return request.get('/system/health')
}

// 清理系统缓存
export const clearSystemCache = () => {
  return request.post('/system/cache/clear')
}
