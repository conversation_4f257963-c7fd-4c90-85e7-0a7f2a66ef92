server:
  port: "8080"
  mode: "debug"

database:
  host: "*************"
  port: 5432
  user: "zhikong"
  password: "196717myh"
  dbname: "postgres"
  sslmode: "disable"
  timezone: "Asia/Shanghai"

redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0

jwt:
  secret: "hospital-management-secret-key-2024"
  expire_time: 24

log:
  level: "info"
  format: "json"

ocr:
  secret_id: ""
  secret_key: ""
  region: "ap-beijing"

minio:
  endpoint: "localhost:9000"
  access_key_id: "minioadmin"
  secret_access_key: "minioadmin"
  use_ssl: false
  bucket_name: "hospital-files"

rabbitmq:
  url: "amqp://guest:guest@localhost:5672/"
