package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// BaseModel 基础模型
type BaseModel struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// UUIDModel 带UUID的基础模型
type UUIDModel struct {
	ID        string         `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// BeforeCreate UUID模型创建前钩子
func (u *UUIDModel) BeforeCreate(tx *gorm.DB) error {
	if u.ID == "" {
		u.ID = uuid.New().String()
	}
	return nil
}

// Status 状态枚举
type Status string

const (
	StatusActive   Status = "active"
	StatusInactive Status = "inactive"
	StatusDeleted  Status = "deleted"
)

// ApprovalStatus 审批状态枚举
type ApprovalStatus string

const (
	ApprovalStatusDraft     ApprovalStatus = "draft"     // 草稿
	ApprovalStatusPending   ApprovalStatus = "pending"   // 待审批
	ApprovalStatusApproved  ApprovalStatus = "approved"  // 已通过
	ApprovalStatusRejected  ApprovalStatus = "rejected"  // 已驳回
	ApprovalStatusWithdrawn ApprovalStatus = "withdrawn" // 已撤回
	ApprovalStatusCancelled ApprovalStatus = "cancelled" // 已取消
)

// PaymentStatus 支付状态枚举
type PaymentStatus string

const (
	PaymentStatusUnpaid PaymentStatus = "unpaid" // 未支付
	PaymentStatusPaid   PaymentStatus = "paid"   // 已支付
	PaymentStatusPartly PaymentStatus = "partly" // 部分支付
)

// BudgetLinkType 预算关联类型
type BudgetLinkType int

const (
	BudgetLinkTypeFreeze BudgetLinkType = 1 // 冻结
	BudgetLinkTypeDeduct BudgetLinkType = 2 // 扣减
)

// ApplicationType 申请类型
type ApplicationType int

const (
	ApplicationTypeGeneral ApplicationType = 1 // 通用报销
	ApplicationTypeTravel  ApplicationType = 2 // 差旅报销
	ApplicationTypeLoan    ApplicationType = 3 // 借款申请
)

// Response 统一响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// PageInfo 分页信息
type PageInfo struct {
	Page     int   `json:"page"`
	PageSize int   `json:"page_size"`
	Total    int64 `json:"total"`
}

// PageRequest 分页请求
type PageRequest struct {
	Page     int `form:"page" json:"page"`
	PageSize int `form:"page_size" json:"page_size"`
}

// GetOffset 获取偏移量
func (p *PageRequest) GetOffset() int {
	return (p.Page - 1) * p.PageSize
}

// SetDefaults 设置默认值
func (p *PageRequest) SetDefaults() {
	if p.Page <= 0 {
		p.Page = 1
	}
	if p.PageSize <= 0 {
		p.PageSize = 20
	}
}
