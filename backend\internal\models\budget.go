package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
)

// BudgetScheme 预算方案模型
type BudgetScheme struct {
	BaseModel
	Name        string         `gorm:"size:100;not null" json:"name"`
	Year        int            `gorm:"not null;index" json:"year"`
	Status      ApprovalStatus `gorm:"size:20;default:'draft'" json:"status"`
	Description string         `gorm:"size:500" json:"description"`
	StartDate   string         `gorm:"type:date" json:"start_date"`
	EndDate     string         `gorm:"type:date" json:"end_date"`
	
	// 创建人信息
	CreatorID uint `gorm:"index" json:"creator_id"`
	Creator   User `gorm:"foreignKey:CreatorID" json:"creator,omitempty"`
	
	// 关联预算数据
	BudgetData []BudgetData `gorm:"foreignKey:SchemeID" json:"budget_data,omitempty"`
}

// TableName 指定表名
func (BudgetScheme) TableName() string {
	return "budget_schemes"
}

// BudgetItem 预算科目模型
type BudgetItem struct {
	BaseModel
	Name        string `gorm:"size:100;not null" json:"name"`
	Code        string `gorm:"uniqueIndex;size:50;not null" json:"code"`
	ParentID    *uint  `gorm:"index" json:"parent_id"`
	Level       int    `gorm:"default:1" json:"level"`
	Type        string `gorm:"size:20;not null" json:"type"` // income, expense
	Category    string `gorm:"size:50" json:"category"`
	Unit        string `gorm:"size:20" json:"unit"`
	Sort        int    `gorm:"default:0" json:"sort"`
	Status      Status `gorm:"size:20;default:'active'" json:"status"`
	Description string `gorm:"size:255" json:"description"`
	
	// 控制属性
	IsControlled bool `gorm:"default:true" json:"is_controlled"` // 是否受预算控制
	ControlType  int  `gorm:"default:1" json:"control_type"`     // 1:刚性控制 2:柔性控制
	
	// 自关联
	Parent   *BudgetItem  `gorm:"foreignKey:ParentID" json:"parent,omitempty"`
	Children []BudgetItem `gorm:"foreignKey:ParentID" json:"children,omitempty"`
}

// TableName 指定表名
func (BudgetItem) TableName() string {
	return "budget_items"
}

// BudgetData 预算数据模型
type BudgetData struct {
	BaseModel
	SchemeID       uint    `gorm:"index;not null" json:"scheme_id"`
	BudgetItemID   uint    `gorm:"index;not null" json:"budget_item_id"`
	OrganizationID uint    `gorm:"index;not null" json:"organization_id"`
	CostCenterID   *uint   `gorm:"index" json:"cost_center_id"`
	
	// 预算金额（按月）
	January   float64 `gorm:"type:decimal(18,2);default:0" json:"january"`
	February  float64 `gorm:"type:decimal(18,2);default:0" json:"february"`
	March     float64 `gorm:"type:decimal(18,2);default:0" json:"march"`
	April     float64 `gorm:"type:decimal(18,2);default:0" json:"april"`
	May       float64 `gorm:"type:decimal(18,2);default:0" json:"may"`
	June      float64 `gorm:"type:decimal(18,2);default:0" json:"june"`
	July      float64 `gorm:"type:decimal(18,2);default:0" json:"july"`
	August    float64 `gorm:"type:decimal(18,2);default:0" json:"august"`
	September float64 `gorm:"type:decimal(18,2);default:0" json:"september"`
	October   float64 `gorm:"type:decimal(18,2);default:0" json:"october"`
	November  float64 `gorm:"type:decimal(18,2);default:0" json:"november"`
	December  float64 `gorm:"type:decimal(18,2);default:0" json:"december"`
	
	// 总计
	TotalAmount float64 `gorm:"type:decimal(18,2);default:0" json:"total_amount"`
	
	// 状态
	Status ApprovalStatus `gorm:"size:20;default:'draft'" json:"status"`
	
	// 关联字段
	Scheme       BudgetScheme `gorm:"foreignKey:SchemeID" json:"scheme,omitempty"`
	BudgetItem   BudgetItem   `gorm:"foreignKey:BudgetItemID" json:"budget_item,omitempty"`
	Organization Organization `gorm:"foreignKey:OrganizationID" json:"organization,omitempty"`
	CostCenter   *CostCenter  `gorm:"foreignKey:CostCenterID" json:"cost_center,omitempty"`
}

// TableName 指定表名
func (BudgetData) TableName() string {
	return "budget_data"
}

// BudgetExecution 预算执行模型
type BudgetExecution struct {
	BaseModel
	BudgetDataID   uint    `gorm:"index;not null" json:"budget_data_id"`
	Month          int     `gorm:"not null" json:"month"`
	ExecutedAmount float64 `gorm:"type:decimal(18,2);default:0" json:"executed_amount"`
	FrozenAmount   float64 `gorm:"type:decimal(18,2);default:0" json:"frozen_amount"`
	AvailableAmount float64 `gorm:"type:decimal(18,2);default:0" json:"available_amount"`
	
	// 关联字段
	BudgetData BudgetData `gorm:"foreignKey:BudgetDataID" json:"budget_data,omitempty"`
}

// TableName 指定表名
func (BudgetExecution) TableName() string {
	return "budget_executions"
}

// BudgetAdjustment 预算调整模型
type BudgetAdjustment struct {
	BaseModel
	Title          string         `gorm:"size:200;not null" json:"title"`
	BudgetDataID   uint           `gorm:"index;not null" json:"budget_data_id"`
	AdjustmentType string         `gorm:"size:20;not null" json:"adjustment_type"` // increase, decrease, transfer
	OriginalAmount float64        `gorm:"type:decimal(18,2)" json:"original_amount"`
	AdjustAmount   float64        `gorm:"type:decimal(18,2)" json:"adjust_amount"`
	NewAmount      float64        `gorm:"type:decimal(18,2)" json:"new_amount"`
	Reason         string         `gorm:"size:500" json:"reason"`
	Status         ApprovalStatus `gorm:"size:20;default:'draft'" json:"status"`
	
	// 申请人信息
	ApplicantID uint `gorm:"index" json:"applicant_id"`
	Applicant   User `gorm:"foreignKey:ApplicantID" json:"applicant,omitempty"`
	
	// 关联字段
	BudgetData BudgetData `gorm:"foreignKey:BudgetDataID" json:"budget_data,omitempty"`
}

// TableName 指定表名
func (BudgetAdjustment) TableName() string {
	return "budget_adjustments"
}

// MonthlyAmounts 月度金额结构体
type MonthlyAmounts struct {
	January   float64 `json:"january"`
	February  float64 `json:"february"`
	March     float64 `json:"march"`
	April     float64 `json:"april"`
	May       float64 `json:"may"`
	June      float64 `json:"june"`
	July      float64 `json:"july"`
	August    float64 `json:"august"`
	September float64 `json:"september"`
	October   float64 `json:"october"`
	November  float64 `json:"november"`
	December  float64 `json:"december"`
}

// Value 实现 driver.Valuer 接口
func (m MonthlyAmounts) Value() (driver.Value, error) {
	return json.Marshal(m)
}

// Scan 实现 sql.Scanner 接口
func (m *MonthlyAmounts) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	
	return json.Unmarshal(bytes, m)
}
