package logger

import (
	"os"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

var Logger *logrus.Logger

func Init(level, format string) {
	Logger = logrus.New()

	// 设置日志级别
	switch level {
	case "debug":
		Logger.SetLevel(logrus.DebugLevel)
	case "info":
		Logger.SetLevel(logrus.InfoLevel)
	case "warn":
		Logger.SetLevel(logrus.WarnLevel)
	case "error":
		Logger.SetLevel(logrus.ErrorLevel)
	default:
		Logger.SetLevel(logrus.InfoLevel)
	}

	// 设置日志格式
	if format == "json" {
		Logger.SetFormatter(&logrus.JSONFormatter{})
	} else {
		Logger.SetFormatter(&logrus.TextFormatter{
			FullTimestamp: true,
		})
	}

	// 设置输出
	Logger.SetOutput(os.Stdout)
}

// Debug 记录调试信息
func Debug(args ...interface{}) {
	Logger.Debug(args...)
}

// Info 记录信息
func Info(args ...interface{}) {
	Logger.Info(args...)
}

// Warn 记录警告
func Warn(args ...interface{}) {
	Logger.Warn(args...)
}

// Error 记录错误
func Error(args ...interface{}) {
	Logger.Error(args...)
}

// Fatal 记录致命错误并退出
func Fatal(args ...interface{}) {
	Logger.Fatal(args...)
}

// WithFields 添加字段
func WithFields(fields logrus.Fields) *logrus.Entry {
	return Logger.WithFields(fields)
}

// GinLogger 返回gin的日志中间件
func GinLogger() gin.HandlerFunc {
	return gin.LoggerWithWriter(Logger.Writer())
}
