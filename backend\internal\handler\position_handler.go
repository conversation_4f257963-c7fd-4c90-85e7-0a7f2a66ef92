package handler

import (
	"net/http"
	"quality_control/backend/internal/models"
	"quality_control/backend/internal/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

type PositionHandler struct {
	positionService *service.PositionService
}

func NewPositionHandler(positionService *service.PositionService) *PositionHandler {
	return &PositionHandler{
		positionService: positionService,
	}
}

// GetPositions 获取岗位列表
// @Summary 获取岗位列表
// @Description 获取岗位列表，支持分页和筛选
// @Tags 岗位管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param name query string false "岗位名称"
// @Param code query string false "岗位代码"
// @Param organization_id query int false "组织ID"
// @Param level query int false "岗位级别"
// @Param status query string false "状态"
// @Success 200 {object} models.Response{data=service.UserListResponse}
// @Failure 400 {object} models.Response
// @Router /positions [get]
func (h *PositionHandler) GetPositions(c *gin.Context) {
	var req service.PositionListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	req.SetDefaults()

	resp, err := h.positionService.GetPositions(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "获取岗位列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    resp,
	})
}

// GetPosition 获取岗位详情
// @Summary 获取岗位详情
// @Description 根据ID获取岗位详情
// @Tags 岗位管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "岗位ID"
// @Success 200 {object} models.Response{data=models.Position}
// @Failure 400 {object} models.Response
// @Router /positions/{id} [get]
func (h *PositionHandler) GetPosition(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "岗位ID格式错误",
		})
		return
	}

	position, err := h.positionService.GetPosition(uint(id))
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    position,
	})
}

// CreatePosition 创建岗位
// @Summary 创建岗位
// @Description 创建新的岗位
// @Tags 岗位管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body service.CreatePositionRequest true "创建岗位请求"
// @Success 200 {object} models.Response{data=models.Position}
// @Failure 400 {object} models.Response
// @Router /positions [post]
func (h *PositionHandler) CreatePosition(c *gin.Context) {
	var req service.CreatePositionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	position, err := h.positionService.CreatePosition(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "创建成功",
		Data:    position,
	})
}

// UpdatePosition 更新岗位
// @Summary 更新岗位
// @Description 更新岗位信息
// @Tags 岗位管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "岗位ID"
// @Param request body service.UpdatePositionRequest true "更新岗位请求"
// @Success 200 {object} models.Response{data=models.Position}
// @Failure 400 {object} models.Response
// @Router /positions/{id} [put]
func (h *PositionHandler) UpdatePosition(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "岗位ID格式错误",
		})
		return
	}

	var req service.UpdatePositionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	position, err := h.positionService.UpdatePosition(uint(id), &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "更新成功",
		Data:    position,
	})
}

// DeletePosition 删除岗位
// @Summary 删除岗位
// @Description 删除岗位
// @Tags 岗位管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "岗位ID"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Router /positions/{id} [delete]
func (h *PositionHandler) DeletePosition(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "岗位ID格式错误",
		})
		return
	}

	if err := h.positionService.DeletePosition(uint(id)); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "删除成功",
	})
}

// AssignUserPositionsRequest 分配用户岗位请求
type AssignUserPositionsRequest struct {
	PositionIDs []uint `json:"position_ids" binding:"required"`
}

// AssignUserPositions 分配用户岗位
// @Summary 分配用户岗位
// @Description 为用户分配岗位
// @Tags 岗位管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "用户ID"
// @Param request body AssignUserPositionsRequest true "分配岗位请求"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Router /users/{id}/positions [post]
func (h *PositionHandler) AssignUserPositions(c *gin.Context) {
	idStr := c.Param("id")
	userID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "用户ID格式错误",
		})
		return
	}

	var req AssignUserPositionsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	if err := h.positionService.AssignUserPositions(uint(userID), req.PositionIDs); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "分配成功",
	})
}

// GetUserPositions 获取用户岗位
// @Summary 获取用户岗位
// @Description 获取用户的岗位列表
// @Tags 岗位管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "用户ID"
// @Success 200 {object} models.Response{data=[]models.Position}
// @Failure 400 {object} models.Response
// @Router /users/{id}/positions [get]
func (h *PositionHandler) GetUserPositions(c *gin.Context) {
	idStr := c.Param("id")
	userID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "用户ID格式错误",
		})
		return
	}

	positions, err := h.positionService.GetUserPositions(uint(userID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "获取用户岗位失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    positions,
	})
}
