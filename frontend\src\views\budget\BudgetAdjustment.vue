<template>
  <div class="budget-adjustment">
    <a-card :bordered="false">
      <template #title>
        <h3>预算调整</h3>
      </template>
      
      <template #extra>
        <a-button type="primary" @click="showAdjustmentModal">
          <template #icon><PlusOutlined /></template>
          新建调整
        </a-button>
      </template>

      <!-- 筛选区 -->
      <div class="filter-section">
        <a-form layout="inline" :model="filterForm" @finish="handleSearch">
          <a-form-item label="调整编号">
            <a-input v-model:value="filterForm.adjustment_no" placeholder="请输入调整编号" />
          </a-form-item>
          
          <a-form-item label="申请人">
            <a-input v-model:value="filterForm.applicant" placeholder="请输入申请人" />
          </a-form-item>
          
          <a-form-item label="调整类型">
            <a-select v-model:value="filterForm.adjustment_type" placeholder="请选择类型" allow-clear style="width: 150px">
              <a-select-option value="increase">预算增加</a-select-option>
              <a-select-option value="decrease">预算减少</a-select-option>
              <a-select-option value="transfer">预算调拨</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="状态">
            <a-select v-model:value="filterForm.status" placeholder="请选择状态" allow-clear style="width: 150px">
              <a-select-option value="draft">草稿</a-select-option>
              <a-select-option value="pending">待审批</a-select-option>
              <a-select-option value="approved">已通过</a-select-option>
              <a-select-option value="rejected">已驳回</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon><SearchOutlined /></template>
                查询
              </a-button>
              <a-button @click="resetFilter">
                <template #icon><ReloadOutlined /></template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 数据表格 -->
      <div class="table-section">
        <a-table
          :columns="columns"
          :data-source="dataSource"
          :pagination="pagination"
          :loading="loading"
          row-key="id"
          @change="handleTableChange"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'adjustment_no'">
              <a @click="viewDetail(record)">{{ record.adjustment_no }}</a>
            </template>
            
            <template v-else-if="column.key === 'adjustment_type'">
              <a-tag :color="getTypeColor(record.adjustment_type)">
                {{ getTypeName(record.adjustment_type) }}
              </a-tag>
            </template>
            
            <template v-else-if="column.key === 'adjustment_amount'">
              <span :class="getAmountClass(record.adjustment_type)">
                {{ formatMoney(record.adjustment_amount) }}
              </span>
            </template>
            
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusName(record.status) }}
              </a-tag>
            </template>
            
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="viewDetail(record)">查看</a>
                <a @click="editAdjustment(record)" v-if="record.status === 'draft'">编辑</a>
                <a @click="approveAdjustment(record)" v-if="record.status === 'pending' && canApprove">审批</a>
                <a @click="deleteAdjustment(record)" v-if="record.status === 'draft'">删除</a>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-card>

    <!-- 新建/编辑调整模态框 -->
    <a-modal
      v-model:open="adjustmentModalVisible"
      :title="isEdit ? '编辑预算调整' : '新建预算调整'"
      width="800px"
      @ok="handleAdjustmentSubmit"
      @cancel="handleAdjustmentCancel"
    >
      <a-form
        ref="adjustmentFormRef"
        :model="adjustmentForm"
        :rules="adjustmentRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="调整类型" name="adjustment_type">
              <a-select v-model:value="adjustmentForm.adjustment_type" placeholder="请选择调整类型">
                <a-select-option value="increase">预算增加</a-select-option>
                <a-select-option value="decrease">预算减少</a-select-option>
                <a-select-option value="transfer">预算调拨</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="调整金额" name="adjustment_amount">
              <a-input-number
                v-model:value="adjustmentForm.adjustment_amount"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="请输入调整金额"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="源预算项目" name="source_budget_item" v-if="adjustmentForm.adjustment_type === 'transfer'">
          <a-tree-select
            v-model:value="adjustmentForm.source_budget_item"
            :tree-data="budgetItems"
            :field-names="{ children: 'children', label: 'name', value: 'id' }"
            placeholder="请选择源预算项目"
          />
        </a-form-item>
        
        <a-form-item label="目标预算项目" name="target_budget_item">
          <a-tree-select
            v-model:value="adjustmentForm.target_budget_item"
            :tree-data="budgetItems"
            :field-names="{ children: 'children', label: 'name', value: 'id' }"
            placeholder="请选择目标预算项目"
          />
        </a-form-item>
        
        <a-form-item label="调整原因" name="reason">
          <a-textarea
            v-model:value="adjustmentForm.reason"
            placeholder="请输入调整原因"
            :rows="4"
          />
        </a-form-item>
        
        <a-form-item label="附件">
          <a-upload
            v-model:file-list="adjustmentForm.attachments"
            :before-upload="beforeUpload"
            multiple
          >
            <a-button>
              <template #icon><UploadOutlined /></template>
              上传附件
            </a-button>
          </a-upload>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="预算调整详情"
      width="1000px"
      :footer="null"
    >
      <adjustment-detail
        v-if="detailModalVisible"
        :adjustment-id="selectedAdjustmentId"
      />
    </a-modal>

    <!-- 审批模态框 -->
    <a-modal
      v-model:open="approvalModalVisible"
      title="预算调整审批"
      width="600px"
      @ok="handleApprovalSubmit"
      @cancel="approvalModalVisible = false"
    >
      <a-form layout="vertical">
        <a-form-item label="审批意见">
          <a-textarea
            v-model:value="approvalForm.comment"
            placeholder="请输入审批意见"
            :rows="4"
          />
        </a-form-item>
        
        <a-form-item label="审批结果">
          <a-radio-group v-model:value="approvalForm.result">
            <a-radio value="approved">通过</a-radio>
            <a-radio value="rejected">驳回</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  UploadOutlined
} from '@ant-design/icons-vue'
import {
  getBudgetAdjustments,
  createBudgetAdjustment,
  updateBudgetAdjustment,
  deleteBudgetAdjustment,
  reviewBudgetAdjustment,
  getBudgetItems
} from '@/api/budget'
import AdjustmentDetail from './components/AdjustmentDetail.vue'

// 响应式数据
const loading = ref(false)
const dataSource = ref([])
const budgetItems = ref([])
const adjustmentModalVisible = ref(false)
const detailModalVisible = ref(false)
const approvalModalVisible = ref(false)
const selectedAdjustmentId = ref(null)
const isEdit = ref(false)
const canApprove = ref(true) // 根据用户权限设置
const adjustmentFormRef = ref()

// 筛选表单
const filterForm = reactive({
  adjustment_no: '',
  applicant: '',
  adjustment_type: null,
  status: null
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 调整表单
const adjustmentForm = reactive({
  adjustment_type: null,
  adjustment_amount: null,
  source_budget_item: null,
  target_budget_item: null,
  reason: '',
  attachments: []
})

// 审批表单
const approvalForm = reactive({
  comment: '',
  result: 'approved'
})

// 表单验证规则
const adjustmentRules = {
  adjustment_type: [{ required: true, message: '请选择调整类型', trigger: 'change' }],
  adjustment_amount: [{ required: true, message: '请输入调整金额', trigger: 'blur' }],
  target_budget_item: [{ required: true, message: '请选择目标预算项目', trigger: 'change' }],
  reason: [{ required: true, message: '请输入调整原因', trigger: 'blur' }]
}

// 表格列配置
const columns = [
  { title: '调整编号', key: 'adjustment_no', width: 150 },
  { title: '调整类型', key: 'adjustment_type', width: 100 },
  { title: '调整金额', key: 'adjustment_amount', width: 120 },
  { title: '申请人', dataIndex: 'applicant_name', key: 'applicant_name', width: 100 },
  { title: '申请时间', dataIndex: 'apply_date', key: 'apply_date', width: 120 },
  { title: '状态', key: 'status', width: 100 },
  { title: '操作', key: 'action', width: 200, fixed: 'right' }
]

// 工具函数
const formatMoney = (amount) => {
  return (amount / 10000).toFixed(2) + '万'
}

const getTypeColor = (type) => {
  const colors = {
    'increase': 'green',
    'decrease': 'red',
    'transfer': 'blue'
  }
  return colors[type] || 'default'
}

const getTypeName = (type) => {
  const names = {
    'increase': '预算增加',
    'decrease': '预算减少',
    'transfer': '预算调拨'
  }
  return names[type] || '未知'
}

const getAmountClass = (type) => {
  return type === 'increase' ? 'amount-increase' : type === 'decrease' ? 'amount-decrease' : 'amount-transfer'
}

const getStatusColor = (status) => {
  const colors = {
    'draft': 'default',
    'pending': 'orange',
    'approved': 'green',
    'rejected': 'red'
  }
  return colors[status] || 'default'
}

const getStatusName = (status) => {
  const names = {
    'draft': '草稿',
    'pending': '待审批',
    'approved': '已通过',
    'rejected': '已驳回'
  }
  return names[status] || '未知'
}

// 数据加载函数
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      ...filterForm,
      page: pagination.current,
      page_size: pagination.pageSize
    }
    
    const response = await getBudgetAdjustments(params)
    dataSource.value = response.data.list || []
    pagination.total = response.data.total || 0
  } catch (error) {
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadBudgetItems = async () => {
  try {
    const response = await getBudgetItems()
    budgetItems.value = response.data || []
  } catch (error) {
    message.error('加载预算项目失败')
  }
}

// 事件处理函数
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const resetFilter = () => {
  Object.assign(filterForm, {
    adjustment_no: '',
    applicant: '',
    adjustment_type: null,
    status: null
  })
  pagination.current = 1
  loadData()
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

const showAdjustmentModal = () => {
  isEdit.value = false
  resetAdjustmentForm()
  adjustmentModalVisible.value = true
}

const resetAdjustmentForm = () => {
  Object.assign(adjustmentForm, {
    adjustment_type: null,
    adjustment_amount: null,
    source_budget_item: null,
    target_budget_item: null,
    reason: '',
    attachments: []
  })
}

const handleAdjustmentSubmit = async () => {
  try {
    await adjustmentFormRef.value.validate()
    
    if (isEdit.value) {
      await updateBudgetAdjustment(selectedAdjustmentId.value, adjustmentForm)
      message.success('更新成功')
    } else {
      await createBudgetAdjustment(adjustmentForm)
      message.success('创建成功')
    }
    
    adjustmentModalVisible.value = false
    loadData()
  } catch (error) {
    message.error('操作失败')
  }
}

const handleAdjustmentCancel = () => {
  adjustmentModalVisible.value = false
  resetAdjustmentForm()
}

const viewDetail = (record) => {
  selectedAdjustmentId.value = record.id
  detailModalVisible.value = true
}

const editAdjustment = (record) => {
  isEdit.value = true
  selectedAdjustmentId.value = record.id
  Object.assign(adjustmentForm, record)
  adjustmentModalVisible.value = true
}

const approveAdjustment = (record) => {
  selectedAdjustmentId.value = record.id
  approvalForm.comment = ''
  approvalForm.result = 'approved'
  approvalModalVisible.value = true
}

const handleApprovalSubmit = async () => {
  try {
    await reviewBudgetAdjustment(selectedAdjustmentId.value, approvalForm)
    message.success('审批成功')
    approvalModalVisible.value = false
    loadData()
  } catch (error) {
    message.error('审批失败')
  }
}

const deleteAdjustment = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这个预算调整吗？删除后无法恢复。',
    onOk: async () => {
      try {
        await deleteBudgetAdjustment(record.id)
        message.success('删除成功')
        loadData()
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

const beforeUpload = (file) => {
  return false // 阻止自动上传
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
  loadBudgetItems()
})
</script>

<style scoped>
.budget-adjustment {
  padding: 24px;
}

.filter-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.table-section {
  margin-bottom: 24px;
}

.amount-increase {
  color: #52c41a;
  font-weight: 500;
}

.amount-decrease {
  color: #ff4d4f;
  font-weight: 500;
}

.amount-transfer {
  color: #1890ff;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .budget-adjustment {
    padding: 16px;
  }
  
  .filter-section {
    padding: 12px;
  }
}
</style>
