package service

import (
	"errors"
	"quality_control/backend/internal/models"

	"gorm.io/gorm"
)

type PositionService struct {
	db *gorm.DB
}

func NewPositionService(db *gorm.DB) *PositionService {
	return &PositionService{db: db}
}

// CreatePositionRequest 创建岗位请求
type CreatePositionRequest struct {
	Name           string `json:"name" binding:"required"`
	Code           string `json:"code" binding:"required"`
	OrganizationID uint   `json:"organization_id" binding:"required"`
	Level          int    `json:"level"`
	Description    string `json:"description"`
}

// UpdatePositionRequest 更新岗位请求
type UpdatePositionRequest struct {
	Name        string `json:"name"`
	Level       int    `json:"level"`
	Description string `json:"description"`
}

// PositionListRequest 岗位列表请求
type PositionListRequest struct {
	models.PageRequest
	Name           string `form:"name"`
	Code           string `form:"code"`
	OrganizationID uint   `form:"organization_id"`
	Level          int    `form:"level"`
	Status         string `form:"status"`
}

// GetPositions 获取岗位列表
func (s *PositionService) GetPositions(req *PositionListRequest) (*UserListResponse, error) {
	req.SetDefaults()
	
	query := s.db.Model(&models.Position{})
	
	if req.Name != "" {
		query = query.Where("name LIKE ?", "%"+req.Name+"%")
	}
	if req.Code != "" {
		query = query.Where("code LIKE ?", "%"+req.Code+"%")
	}
	if req.OrganizationID > 0 {
		query = query.Where("organization_id = ?", req.OrganizationID)
	}
	if req.Level > 0 {
		query = query.Where("level = ?", req.Level)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	var positions []models.Position
	if err := query.Preload("Organization").
		Offset(req.GetOffset()).Limit(req.PageSize).
		Order("created_at DESC").
		Find(&positions).Error; err != nil {
		return nil, err
	}

	// 转换为通用响应格式
	var users []models.User
	return &UserListResponse{
		List: users,
		PageInfo: models.PageInfo{
			Page:     req.Page,
			PageSize: req.PageSize,
			Total:    total,
		},
	}, nil
}

// GetPosition 获取岗位详情
func (s *PositionService) GetPosition(id uint) (*models.Position, error) {
	var position models.Position
	if err := s.db.Preload("Organization").Where("id = ?", id).First(&position).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("岗位不存在")
		}
		return nil, err
	}
	return &position, nil
}

// CreatePosition 创建岗位
func (s *PositionService) CreatePosition(req *CreatePositionRequest) (*models.Position, error) {
	// 检查代码是否已存在
	var count int64
	if err := s.db.Model(&models.Position{}).Where("code = ?", req.Code).Count(&count).Error; err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.New("岗位代码已存在")
	}

	// 检查组织是否存在
	var org models.Organization
	if err := s.db.Where("id = ?", req.OrganizationID).First(&org).Error; err != nil {
		return nil, errors.New("组织不存在")
	}

	position := &models.Position{
		Name:           req.Name,
		Code:           req.Code,
		OrganizationID: req.OrganizationID,
		Level:          req.Level,
		Description:    req.Description,
		Status:         models.StatusActive,
	}

	if err := s.db.Create(position).Error; err != nil {
		return nil, err
	}

	return s.GetPosition(position.ID)
}

// UpdatePosition 更新岗位
func (s *PositionService) UpdatePosition(id uint, req *UpdatePositionRequest) (*models.Position, error) {
	var position models.Position
	if err := s.db.Where("id = ?", id).First(&position).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("岗位不存在")
		}
		return nil, err
	}

	updates := map[string]interface{}{}
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Level > 0 {
		updates["level"] = req.Level
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}

	if len(updates) > 0 {
		if err := s.db.Model(&position).Updates(updates).Error; err != nil {
			return nil, err
		}
	}

	return s.GetPosition(id)
}

// DeletePosition 删除岗位
func (s *PositionService) DeletePosition(id uint) error {
	var position models.Position
	if err := s.db.Where("id = ?", id).First(&position).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("岗位不存在")
		}
		return err
	}

	// 检查是否有用户使用该岗位
	var count int64
	if err := s.db.Model(&models.UserPosition{}).Where("position_id = ?", id).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return errors.New("该岗位正在被使用，无法删除")
	}

	return s.db.Delete(&position).Error
}

// AssignUserPositions 分配用户岗位
func (s *PositionService) AssignUserPositions(userID uint, positionIDs []uint) error {
	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 删除原有岗位
	if err := tx.Where("user_id = ?", userID).Delete(&models.UserPosition{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 添加新岗位
	for _, positionID := range positionIDs {
		userPosition := &models.UserPosition{
			UserID:     userID,
			PositionID: positionID,
		}
		if err := tx.Create(userPosition).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}

// GetUserPositions 获取用户岗位
func (s *PositionService) GetUserPositions(userID uint) ([]models.Position, error) {
	var positions []models.Position
	if err := s.db.Joins("JOIN user_positions ON positions.id = user_positions.position_id").
		Where("user_positions.user_id = ?", userID).
		Find(&positions).Error; err != nil {
		return nil, err
	}
	return positions, nil
}
