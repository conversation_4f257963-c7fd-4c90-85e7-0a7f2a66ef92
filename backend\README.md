# 医院运营管理系统后端

基于内部控制理念的医院运营管理系统后端服务，使用Go语言开发。

## 技术栈

- **语言**: Go 1.21
- **框架**: Gin
- **数据库**: PostgreSQL
- **缓存**: Redis
- **ORM**: GORM
- **认证**: JWT
- **文档**: Swagger

## 项目结构

```
backend/
├── cmd/                    # 命令行工具
├── configs/               # 配置文件
├── internal/              # 内部代码
│   ├── config/           # 配置管理
│   ├── database/         # 数据库连接和迁移
│   ├── handler/          # HTTP处理器
│   ├── middleware/       # 中间件
│   ├── models/           # 数据模型
│   ├── router/           # 路由配置
│   └── service/          # 业务逻辑
├── pkg/                   # 公共包
│   └── logger/           # 日志工具
├── scripts/              # 脚本文件
├── go.mod                # Go模块文件
├── go.sum                # 依赖校验文件
├── main.go               # 程序入口
└── README.md             # 项目说明
```

## 功能模块

### 1. 基础平台模块
- **用户管理**: 用户CRUD、角色分配、状态管理
- **组织架构**: 树形组织结构、部门管理
- **权限管理**: RBAC权限控制、角色权限分配
- **认证授权**: JWT认证、登录登出、密码管理

### 2. 预算管理模块
- **预算方案**: 年度预算方案编制、审批
- **预算科目**: 树形科目结构、收支分类
- **预算控制**: 刚性/柔性控制、预算监控

### 3. 支出控制模块
- **报销申请**: 支出申请创建、明细管理
- **审批流程**: 多级审批、状态跟踪
- **支付管理**: 支付记录、状态更新
- **费用标准**: 差旅费、餐费等标准管理

## API接口

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户退出
- `GET /api/auth/profile` - 获取用户信息
- `POST /api/auth/refresh` - 刷新token
- `POST /api/auth/change-password` - 修改密码

### 用户管理接口
- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建用户
- `GET /api/users/:id` - 获取用户详情
- `PUT /api/users/:id` - 更新用户
- `DELETE /api/users/:id` - 删除用户
- `POST /api/users/:id/reset-password` - 重置密码
- `PATCH /api/users/:id/status` - 切换用户状态

### 组织架构接口
- `GET /api/organizations/tree` - 获取组织架构树
- `GET /api/organizations` - 获取组织列表
- `POST /api/organizations` - 创建组织
- `GET /api/organizations/:id` - 获取组织详情
- `PUT /api/organizations/:id` - 更新组织
- `DELETE /api/organizations/:id` - 删除组织
- `PUT /api/organizations/:id/move` - 移动组织

### 预算管理接口
- `GET /api/budget/schemes` - 获取预算方案列表
- `POST /api/budget/schemes` - 创建预算方案
- `GET /api/budget/schemes/:id` - 获取预算方案详情
- `PUT /api/budget/schemes/:id` - 更新预算方案
- `DELETE /api/budget/schemes/:id` - 删除预算方案
- `POST /api/budget/schemes/:id/submit` - 提交预算方案
- `POST /api/budget/schemes/:id/approve` - 审批预算方案
- `GET /api/budget/items/tree` - 获取预算科目树
- `POST /api/budget/items` - 创建预算科目

### 支出控制接口
- `GET /api/expenditure/applications` - 获取支出申请列表
- `POST /api/expenditure/applications` - 创建支出申请
- `GET /api/expenditure/applications/:id` - 获取支出申请详情
- `PUT /api/expenditure/applications/:id` - 更新支出申请
- `DELETE /api/expenditure/applications/:id` - 删除支出申请
- `POST /api/expenditure/applications/:id/submit` - 提交支出申请
- `POST /api/expenditure/applications/:id/approve` - 审批支出申请
- `POST /api/expenditure/payments` - 创建支付记录

## 数据库设计

### 核心表结构
- `users` - 用户表
- `roles` - 角色表
- `permissions` - 权限表
- `organizations` - 组织架构表
- `positions` - 岗位表
- `cost_centers` - 成本中心表
- `budget_schemes` - 预算方案表
- `budget_items` - 预算科目表
- `budget_data` - 预算数据表
- `expenditure_applications` - 支出申请表
- `expenditure_details` - 支出明细表
- `payments` - 支付记录表

## 快速开始

### 1. 环境要求
- Go 1.21+
- PostgreSQL 12+
- Redis 6+

### 2. 安装依赖
```bash
go mod download
```

### 3. 配置数据库
修改 `configs/config.yaml` 中的数据库配置

### 4. 数据库迁移
```bash
go run cmd/migrate/simple.go -drop
```

### 5. 启动服务
```bash
go run main.go
```

### 6. 访问API
- 健康检查: http://localhost:8080/health
- API文档: http://localhost:8080/swagger/index.html

## 默认账户

- 用户名: `admin`
- 密码: `password`

## 开发说明

### 添加新功能
1. 在 `internal/models/` 中定义数据模型
2. 在 `internal/service/` 中实现业务逻辑
3. 在 `internal/handler/` 中实现HTTP处理器
4. 在 `internal/router/` 中注册路由

### 数据库迁移
使用 `cmd/migrate/simple.go` 进行数据库迁移和初始化

### 日志管理
使用 `pkg/logger` 包进行统一日志管理

## 部署

### Docker部署
```bash
docker build -t hospital-management .
docker run -p 8080:8080 hospital-management
```

### 生产环境配置
1. 修改 `configs/config.yaml` 中的生产环境配置
2. 设置环境变量 `GIN_MODE=release`
3. 配置反向代理和HTTPS

## 许可证

MIT License
