<template>
  <div class="user-manage">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="page-title-section">
            <h1 class="page-title">用户管理</h1>
            <p class="page-subtitle">管理系统用户账号和权限</p>
          </div>
        </div>
        <div class="header-actions">
          <a-button @click="syncUsersFromHIS">
            <template #icon><SyncOutlined /></template>
            同步HIS
          </a-button>
          <a-button @click="showImportModal">
            <template #icon><ImportOutlined /></template>
            批量导入
          </a-button>
          <a-button type="primary" @click="showCreateModal">
            <template #icon><PlusOutlined /></template>
            新建用户
          </a-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <a-row :gutter="[24, 16]">
        <a-col :xs="12" :sm="6">
          <div class="stat-card total">
            <div class="stat-icon">
              <TeamOutlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ userStats.total }}</div>
              <div class="stat-label">用户总数</div>
            </div>
          </div>
        </a-col>
        <a-col :xs="12" :sm="6">
          <div class="stat-card active">
            <div class="stat-icon">
              <CheckCircleOutlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ userStats.active }}</div>
              <div class="stat-label">活跃用户</div>
            </div>
          </div>
        </a-col>
        <a-col :xs="12" :sm="6">
          <div class="stat-card disabled">
            <div class="stat-icon">
              <StopOutlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ userStats.disabled }}</div>
              <div class="stat-label">禁用用户</div>
            </div>
          </div>
        </a-col>
        <a-col :xs="12" :sm="6">
          <div class="stat-card online">
            <div class="stat-icon">
              <GlobalOutlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ userStats.online }}</div>
              <div class="stat-label">在线用户</div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <a-card class="filter-card" :bordered="false">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch" class="filter-form">
          <a-form-item label="用户状态">
            <a-select
              v-model:value="searchForm.status"
              style="width: 120px"
              placeholder="全部状态"
              allow-clear
            >
              <a-select-option :value="1">在职</a-select-option>
              <a-select-option :value="2">离职</a-select-option>
              <a-select-option :value="0">禁用</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="用户角色">
            <a-select
              v-model:value="searchForm.role_id"
              style="width: 150px"
              placeholder="全部角色"
              allow-clear
            >
              <a-select-option v-for="role in roleList" :key="role.id" :value="role.id">
                {{ role.name }}
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="所属部门">
            <a-tree-select
              v-model:value="searchForm.department_id"
              :tree-data="orgTreeData"
              :field-names="{ children: 'children', label: 'name', value: 'id' }"
              placeholder="选择部门"
              allow-clear
              style="width: 200px"
            />
          </a-form-item>

          <a-form-item>
            <a-input-search
              v-model:value="searchForm.keyword"
              placeholder="搜索用户名、姓名、工号..."
              style="width: 250px"
              @search="handleSearch"
            />
          </a-form-item>

          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon><SearchOutlined /></template>
                查询
              </a-button>
              <a-button @click="resetSearch">
                <template #icon><ReloadOutlined /></template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

      <!-- 用户表格 -->
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="tableLoading"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'avatar'">
            <a-avatar :src="record.avatar" :icon="h(UserOutlined)" />
          </template>
          <template v-else-if="column.key === 'gender'">
            <a-tag :color="getGenderColor(record.gender)">
              {{ getGenderName(record.gender) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'department'">
            {{ record.department?.name || '-' }}
          </template>
          <template v-else-if="column.key === 'roles'">
            <a-space wrap>
              <a-tag v-for="role in record.roles" :key="role.id" color="blue">
                {{ role.name }}
              </a-tag>
            </a-space>
          </template>
          <template v-else-if="column.key === 'status'">
            <a-tag :color="getUserStatusColor(record.status)">
              {{ getUserStatusName(record.status) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'last_login_at'">
            {{ record.last_login_at ? formatDate(record.last_login_at) : '从未登录' }}
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a @click="editUser(record)">编辑</a>
              <a @click="assignRoles(record)">分配角色</a>
              <a @click="resetPassword(record)">重置密码</a>
              <a-dropdown>
                <a>更多 <DownOutlined /></a>
                <template #overlay>
                  <a-menu @click="({ key }) => handleMoreAction(key, record)">
                    <a-menu-item key="toggle-status">
                      {{ record.status === 1 ? '禁用' : '启用' }}
                    </a-menu-item>
                    <a-menu-item key="view-detail">查看详情</a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" style="color: #ff4d4f">删除</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑用户模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :confirm-loading="modalLoading"
      width="800px"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="用户名" name="username">
              <a-input v-model:value="formData.username" placeholder="请输入用户名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="真实姓名" name="real_name">
              <a-input v-model:value="formData.real_name" placeholder="请输入真实姓名" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="邮箱" name="email">
              <a-input v-model:value="formData.email" placeholder="请输入邮箱" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="手机号" name="phone">
              <a-input v-model:value="formData.phone" placeholder="请输入手机号" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="工号" name="employee_no">
              <a-input v-model:value="formData.employee_no" placeholder="请输入工号" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="性别" name="gender">
              <a-select v-model:value="formData.gender" placeholder="请选择性别">
                <a-select-option :value="0">未知</a-select-option>
                <a-select-option :value="1">男</a-select-option>
                <a-select-option :value="2">女</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="所属部门" name="department_id">
              <a-tree-select
                v-model:value="formData.department_id"
                :tree-data="orgTreeData"
                :field-names="{ children: 'children', label: 'name', value: 'id' }"
                placeholder="请选择部门"
                tree-default-expand-all
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="职务" name="job_title">
              <a-input v-model:value="formData.job_title" placeholder="请输入职务" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="职级" name="job_level">
              <a-input-number v-model:value="formData.job_level" :min="1" :max="10" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="入职日期" name="hire_date">
              <a-date-picker v-model:value="formData.hire_date" style="width: 100%" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="生日" name="birthday">
              <a-date-picker v-model:value="formData.birthday" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="状态" name="status">
              <a-select v-model:value="formData.status" placeholder="请选择状态">
                <a-select-option :value="1">在职</a-select-option>
                <a-select-option :value="2">离职</a-select-option>
                <a-select-option :value="0">禁用</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item v-if="!formData.id" label="初始密码" name="password">
          <a-input-password v-model:value="formData.password" placeholder="请输入初始密码" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 角色分配模态框 -->
    <a-modal
      v-model:open="roleModalVisible"
      title="分配角色"
      :confirm-loading="roleModalLoading"
      @ok="handleRoleModalOk"
      @cancel="roleModalVisible = false"
    >
      <div v-if="currentUser">
        <p>为用户 <strong>{{ currentUser.real_name }}</strong> 分配角色：</p>
        <a-checkbox-group v-model:value="selectedRoles" style="width: 100%">
          <a-row>
            <a-col v-for="role in allRoles" :key="role.id" :span="24" style="margin-bottom: 8px">
              <a-checkbox :value="role.id">
                {{ role.name }}
                <span style="color: #999; margin-left: 8px">{{ role.description }}</span>
              </a-checkbox>
            </a-col>
          </a-row>
        </a-checkbox-group>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, h } from 'vue'
import { message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  PlusOutlined,
  ImportOutlined,
  SyncOutlined,
  SearchOutlined,
  ReloadOutlined,
  UserOutlined,
  DownOutlined,
  TeamOutlined,
  CheckCircleOutlined,
  StopOutlined,
  GlobalOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  KeyOutlined
} from '@ant-design/icons-vue'
import {
  getUsers,
  createUser,
  updateUser,
  deleteUser,
  resetUserPassword,
  toggleUserStatus,
  assignUserRoles,
  syncUsers
} from '@/api/user'
import { getOrganizationTree } from '@/api/organization'
import { getRoles } from '@/api/rbac'

// 响应式数据
const tableData = ref([])
const tableLoading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const roleModalVisible = ref(false)
const roleModalLoading = ref(false)
const formRef = ref()
const orgTreeData = ref([])
const allRoles = ref([])
const currentUser = ref(null)
const selectedRoles = ref([])

// 统计数据
const userStats = reactive({
  total: 156,
  active: 142,
  disabled: 12,
  online: 38
})

// 搜索表单
const searchForm = reactive({
  username: '',
  real_name: '',
  department_id: null,
  status: null,
  role_id: null,
  keyword: ''
})

// 表单数据
const formData = reactive({
  id: null,
  username: '',
  password: '',
  real_name: '',
  email: '',
  phone: '',
  employee_no: '',
  gender: 0,
  birthday: null,
  department_id: null,
  job_title: '',
  job_level: 1,
  hire_date: null,
  status: 1
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 计算属性
const modalTitle = computed(() => formData.id ? '编辑用户' : '新增用户')

// 表格列配置
const columns = [
  { title: '头像', key: 'avatar', width: 80 },
  { title: '用户名', dataIndex: 'username', key: 'username' },
  { title: '真实姓名', dataIndex: 'real_name', key: 'real_name' },
  { title: '工号', dataIndex: 'employee_no', key: 'employee_no' },
  { title: '性别', key: 'gender', width: 80 },
  { title: '部门', key: 'department', width: 120 },
  { title: '职务', dataIndex: 'job_title', key: 'job_title' },
  { title: '角色', key: 'roles', width: 150 },
  { title: '状态', key: 'status', width: 80 },
  { title: '最后登录', key: 'last_login_at', width: 150 },
  { title: '操作', key: 'action', width: 200, fixed: 'right' }
]

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 50, message: '用户名长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  real_name: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
  email: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  department_id: [{ required: true, message: '请选择所属部门', trigger: 'change' }],
  password: [
    { required: true, message: '请输入初始密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ]
}

// 获取性别名称
const getGenderName = (gender) => {
  const names = { 0: '未知', 1: '男', 2: '女' }
  return names[gender] || '未知'
}

// 获取性别颜色
const getGenderColor = (gender) => {
  const colors = { 0: 'default', 1: 'blue', 2: 'pink' }
  return colors[gender] || 'default'
}

// 获取用户状态名称
const getUserStatusName = (status) => {
  const names = { 0: '禁用', 1: '在职', 2: '离职' }
  return names[status] || '未知'
}

// 获取用户状态颜色
const getUserStatusColor = (status) => {
  const colors = { 0: 'red', 1: 'green', 2: 'orange' }
  return colors[status] || 'default'
}

// 格式化日期
const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 加载用户列表
const loadUsers = async () => {
  tableLoading.value = true
  try {
    const params = {
      page: pagination.current,
      page_size: pagination.pageSize,
      ...searchForm
    }
    
    const response = await getUsers(params)
    tableData.value = response.data.list || []
    pagination.total = response.data.total || 0
  } catch (error) {
    message.error('加载用户列表失败')
  } finally {
    tableLoading.value = false
  }
}

// 加载组织树
const loadOrganizationTree = async () => {
  try {
    const response = await getOrganizationTree()
    orgTreeData.value = response.data || []
  } catch (error) {
    message.error('加载组织架构失败')
  }
}

// 加载角色列表
const loadRoles = async () => {
  try {
    const response = await getRoles()
    allRoles.value = response.data.list || []
  } catch (error) {
    message.error('加载角色列表失败')
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadUsers()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    username: '',
    real_name: '',
    department_id: null,
    status: null
  })
  pagination.current = 1
  loadUsers()
}

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadUsers()
}

// 显示创建模态框
const showCreateModal = () => {
  resetForm()
  modalVisible.value = true
}

// 编辑用户
const editUser = (user) => {
  Object.assign(formData, {
    id: user.id,
    username: user.username,
    real_name: user.real_name,
    email: user.email,
    phone: user.phone,
    employee_no: user.employee_no,
    gender: user.gender,
    birthday: user.birthday ? dayjs(user.birthday) : null,
    department_id: user.department_id,
    job_title: user.job_title,
    job_level: user.job_level,
    hire_date: user.hire_date ? dayjs(user.hire_date) : null,
    status: user.status
  })
  modalVisible.value = true
}

// 分配角色
const assignRoles = async (user) => {
  currentUser.value = user
  selectedRoles.value = user.roles?.map(role => role.id) || []
  roleModalVisible.value = true
}

// 重置密码
const resetPassword = (user) => {
  Modal.confirm({
    title: '重置密码',
    content: `确定要重置用户"${user.real_name}"的密码吗？新密码将设置为：123456`,
    onOk: async () => {
      try {
        await resetUserPassword(user.id, { password: '123456' })
        message.success('密码重置成功')
      } catch (error) {
        message.error('密码重置失败')
      }
    }
  })
}

// 更多操作
const handleMoreAction = async (key, user) => {
  switch (key) {
    case 'toggle-status':
      await toggleStatus(user)
      break
    case 'view-detail':
      viewDetail(user)
      break
    case 'delete':
      await deleteUserConfirm(user)
      break
  }
}

// 切换状态
const toggleStatus = async (user) => {
  const newStatus = user.status === 1 ? 0 : 1
  const action = newStatus === 1 ? '启用' : '禁用'
  
  Modal.confirm({
    title: `${action}用户`,
    content: `确定要${action}用户"${user.real_name}"吗？`,
    onOk: async () => {
      try {
        await toggleUserStatus(user.id, newStatus)
        message.success(`${action}成功`)
        loadUsers()
      } catch (error) {
        message.error(`${action}失败`)
      }
    }
  })
}

// 查看详情
const viewDetail = (user) => {
  message.info('查看详情功能开发中...')
}

// 删除用户确认
const deleteUserConfirm = (user) => {
  Modal.confirm({
    title: '删除用户',
    content: `确定要删除用户"${user.real_name}"吗？删除后不可恢复。`,
    onOk: async () => {
      try {
        await deleteUser(user.id)
        message.success('删除成功')
        loadUsers()
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

// 处理模态框确认
const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    modalLoading.value = true
    
    const submitData = { ...formData }
    if (submitData.birthday) {
      submitData.birthday = submitData.birthday.format('YYYY-MM-DD')
    }
    if (submitData.hire_date) {
      submitData.hire_date = submitData.hire_date.format('YYYY-MM-DD')
    }
    
    if (formData.id) {
      await updateUser(formData.id, submitData)
      message.success('更新成功')
    } else {
      await createUser(submitData)
      message.success('创建成功')
    }
    
    modalVisible.value = false
    loadUsers()
  } catch (error) {
    if (error.errorFields) {
      message.error('请检查表单输入')
    } else {
      message.error('操作失败')
    }
  } finally {
    modalLoading.value = false
  }
}

// 处理模态框取消
const handleModalCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 处理角色分配确认
const handleRoleModalOk = async () => {
  try {
    roleModalLoading.value = true
    await assignUserRoles(currentUser.value.id, selectedRoles.value)
    message.success('角色分配成功')
    roleModalVisible.value = false
    loadUsers()
  } catch (error) {
    message.error('角色分配失败')
  } finally {
    roleModalLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: null,
    username: '',
    password: '',
    real_name: '',
    email: '',
    phone: '',
    employee_no: '',
    gender: 0,
    birthday: null,
    department_id: null,
    job_title: '',
    job_level: 1,
    hire_date: null,
    status: 1
  })
  formRef.value?.resetFields()
}

// 显示导入模态框
const showImportModal = () => {
  message.info('批量导入功能开发中...')
}

// 同步用户
const syncUsersFromHIS = () => {
  Modal.confirm({
    title: '同步HIS用户',
    content: '确定要从HIS系统同步用户信息吗？这可能需要一些时间。',
    onOk: async () => {
      try {
        await syncUsers('his')
        message.success('同步成功')
        loadUsers()
      } catch (error) {
        message.error('同步失败')
      }
    }
  })
}

// 组件挂载时加载数据
onMounted(() => {
  loadUsers()
  loadOrganizationTree()
  loadRoles()
})
</script>

<style scoped>
/* 用户管理页面 - 企业级设计 */
.user-manage {
  background: var(--bg-secondary);
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-active) 100%);
  color: white;
  padding: var(--spacing-2xl) var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.page-title-section {
  flex: 1;
}

.page-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin: 0 0 var(--spacing-sm) 0;
  color: white;
}

.page-subtitle {
  font-size: var(--font-size-md);
  margin: 0;
  opacity: 0.9;
  font-weight: var(--font-weight-normal);
}

.header-actions {
  display: flex;
  gap: var(--spacing-md);
}

/* 统计卡片 */
.stats-section {
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.stat-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.stat-card.total::before {
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-hover));
}

.stat-card.active::before {
  background: linear-gradient(90deg, #00C853, #4CAF50);
}

.stat-card.disabled::before {
  background: linear-gradient(90deg, #F44336, #E57373);
}

.stat-card.online::before {
  background: linear-gradient(90deg, #FF9800, #FFB74D);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-card.total .stat-icon {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-color-hover));
}

.stat-card.active .stat-icon {
  background: linear-gradient(135deg, #00C853, #4CAF50);
}

.stat-card.disabled .stat-icon {
  background: linear-gradient(135deg, #F44336, #E57373);
}

.stat-card.online .stat-icon {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 筛选区域 */
.filter-section {
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.filter-card {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
}

.filter-form {
  width: 100%;
}

.filter-form :deep(.ant-form-item) {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: var(--spacing-lg);
    text-align: center;
  }

  .stats-section,
  .filter-section {
    padding: 0 var(--spacing-md);
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .page-title {
    font-size: var(--font-size-2xl);
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .filter-form {
    flex-direction: column;
  }

  .filter-form :deep(.ant-form-item) {
    margin-bottom: var(--spacing-md);
    width: 100%;
  }
}

@media (max-width: 576px) {
  .page-header {
    padding: var(--spacing-md);
  }

  .page-title {
    font-size: var(--font-size-xl);
  }

  .page-subtitle {
    font-size: var(--font-size-sm);
  }

  .stats-section,
  .filter-section {
    padding: 0 var(--spacing-sm);
  }

  .stat-card {
    padding: var(--spacing-md);
  }
}
</style>
