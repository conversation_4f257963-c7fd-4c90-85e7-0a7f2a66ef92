import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { message } from 'ant-design-vue'

// 导入SystemLayout组件
const SystemLayout = () => import('@/components/SystemLayout.vue')

// 路由配置
const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/components/SystemLayout.vue'),
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: {
          title: '工作台',
          requiresAuth: true
        }
      },
      // 系统管理路由
      {
        path: 'system/users',
        name: 'UserManage',
        component: () => import('@/views/system/UserManage.vue'),
        meta: {
          title: '用户管理',
          requiresAuth: true
        }
      },
      {
        path: 'system/roles',
        name: 'RoleManage',
        component: () => import('@/views/system/RoleManage.vue'),
        meta: {
          title: '角色管理',
          requiresAuth: true
        }
      },
      {
        path: 'system/permissions',
        name: 'PermissionManage',
        component: () => import('@/views/system/PermissionManage.vue'),
        meta: {
          title: '权限管理',
          requiresAuth: true
        }
      },
      {
        path: 'system/positions',
        name: 'PositionManage',
        component: () => import('@/views/system/PositionManage.vue'),
        meta: {
          title: '岗位管理',
          requiresAuth: true
        }
      },
      {
        path: 'system/organizations',
        name: 'OrganizationManage',
        component: () => import('@/views/system/OrganizationManage.vue'),
        meta: {
          title: '组织管理',
          requiresAuth: true
        }
      },
      {
        path: 'system/config',
        name: 'SystemConfig',
        component: () => import('@/views/system/SystemConfig.vue'),
        meta: {
          title: '系统配置',
          requiresAuth: true
        }
      },
      {
        path: 'system/notifications',
        name: 'NotificationManage',
        component: () => import('@/views/system/NotificationManage.vue'),
        meta: {
          title: '通知管理',
          requiresAuth: true
        }
      },
      {
        path: 'system/audit-logs',
        name: 'AuditLog',
        component: () => import('@/views/system/AuditLog.vue'),
        meta: {
          title: '审计日志',
          requiresAuth: true
        }
      },
      {
        path: 'system/workflow-designer',
        name: 'WorkflowDesigner',
        component: () => import('@/views/system/WorkflowDesigner.vue'),
        meta: {
          title: '工作流设计器',
          requiresAuth: true
        }
      }
    ]
  },

  // 预算管理模块
  {
    path: '/budget',
    component: SystemLayout,
    meta: { title: '预算管理', requiresAuth: true },
    children: [
      {
        path: 'compile',
        name: 'BudgetCompile',
        component: () => import('@/views/budget/BudgetCompile.vue'),
        meta: { title: '预算编制', requiresAuth: true }
      },
      {
        path: 'analysis',
        name: 'BudgetAnalysis',
        component: () => import('@/views/budget/BudgetAnalysis.vue'),
        meta: { title: '预算分析', requiresAuth: true }
      },
      {
        path: 'monitor',
        name: 'BudgetMonitor',
        component: () => import('@/views/budget/BudgetMonitor.vue'),
        meta: { title: '预算监控', requiresAuth: true }
      },
      {
        path: 'adjustment',
        name: 'BudgetAdjustment',
        component: () => import('@/views/budget/BudgetAdjustment.vue'),
        meta: { title: '预算调整', requiresAuth: true }
      }
    ]
  },

  // 支出控制管理模块
  {
    path: '/expenditure',
    component: SystemLayout,
    meta: { title: '支出控制', requiresAuth: true },
    children: [
      {
        path: 'apply',
        name: 'ExpenseApply',
        component: () => import('@/views/expenditure/ExpenseApply.vue'),
        meta: { title: '报销申请', requiresAuth: true }
      },
      {
        path: 'list',
        name: 'ExpenseList',
        component: () => import('@/views/expenditure/ExpenseList.vue'),
        meta: { title: '报销管理', requiresAuth: true }
      },
      {
        path: 'approval',
        name: 'ExpenseApproval',
        component: () => import('@/views/expenditure/ExpenseApproval.vue'),
        meta: { title: '报销审批', requiresAuth: true }
      },
      {
        path: 'payment',
        name: 'ExpensePayment',
        component: () => import('@/views/expenditure/ExpensePayment.vue'),
        meta: { title: '支付管理', requiresAuth: true }
      },
      {
        path: 'analysis',
        name: 'ExpenseAnalysis',
        component: () => import('@/views/expenditure/ExpenseAnalysis.vue'),
        meta: { title: '费用分析', requiresAuth: true }
      }
    ]
  },

  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 医院质控系统`
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      message.warning('请先登录')
      next('/login')
      return
    }

    // 如果已登录但没有用户信息，尝试获取
    if (!authStore.user) {
      try {
        await authStore.fetchUserProfile()
      } catch (error) {
        console.error('获取用户信息失败:', error)
        next('/login')
        return
      }
    }
  }

  // 如果已登录用户访问登录页，重定向到首页
  if (to.path === '/login' && authStore.isAuthenticated) {
    next('/')
    return
  }

  next()
})

export default router