package main

import (
	"log"
	"quality_control/backend/internal/config"
	"quality_control/backend/internal/database"
	"quality_control/backend/internal/middleware"
	"quality_control/backend/internal/router"
	"quality_control/backend/pkg/logger"

	"github.com/gin-gonic/gin"
)

// @title 医院运营管理系统 API
// @version 1.0
// @description 基于内部控制理念的医院运营管理系统
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host localhost:8080
// @BasePath /api

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() {
	// 初始化配置
	cfg := config.Load()

	// 初始化日志
	logger.Init(cfg.Log.Level, cfg.Log.Format)

	// 初始化数据库
	db, err := database.Init(cfg.Database)
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}

	// 初始化Redis (可选)
	rdb, err := database.InitRedis(cfg.Redis)
	if err != nil {
		log.Printf("Warning: Failed to initialize Redis: %v", err)
		log.Printf("Continuing without Redis...")
		rdb = nil
	}

	// 设置Gin模式
	if cfg.Server.Mode == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建Gin引擎
	r := gin.New()

	// 添加中间件
	r.Use(middleware.Logger())
	r.Use(middleware.Recovery())
	r.Use(middleware.CORS())

	// 初始化路由
	router.Init(r, db, rdb, cfg)

	// 启动服务器
	log.Printf("Server starting on port %s", cfg.Server.Port)
	if err := r.Run(":" + cfg.Server.Port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
