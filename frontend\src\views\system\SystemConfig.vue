<template>
  <div class="system-config">
    <a-card :bordered="false">
      <template #title>
        <div class="page-header">
          <h3>系统配置</h3>
          <div class="header-actions">
            <a-button type="primary" @click="showCreateModal">
              <template #icon><PlusOutlined /></template>
              新增配置
            </a-button>
            <a-button @click="refreshConfigs">
              <template #icon><ReloadOutlined /></template>
              刷新配置
            </a-button>
          </div>
        </div>
      </template>

      <!-- 配置分组标签 -->
      <a-tabs v-model:activeKey="activeGroup" @change="handleGroupChange">
        <a-tab-pane key="all" tab="全部配置" />
        <a-tab-pane key="system" tab="系统配置" />
        <a-tab-pane key="business" tab="业务配置" />
        <a-tab-pane key="notification" tab="通知配置" />
        <a-tab-pane key="security" tab="安全配置" />
      </a-tabs>

      <!-- 搜索表单 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="配置键">
            <a-input v-model:value="searchForm.config_key" placeholder="请输入配置键" />
          </a-form-item>
          <a-form-item label="配置类型">
            <a-select v-model:value="searchForm.config_type" placeholder="请选择类型" allow-clear>
              <a-select-option value="string">字符串</a-select-option>
              <a-select-option value="number">数字</a-select-option>
              <a-select-option value="boolean">布尔值</a-select-option>
              <a-select-option value="json">JSON</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="状态">
            <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
              <a-select-option :value="1">启用</a-select-option>
              <a-select-option :value="0">禁用</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon><SearchOutlined /></template>
                搜索
              </a-button>
              <a-button @click="resetSearch">
                <template #icon><ReloadOutlined /></template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 配置表格 -->
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="tableLoading"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'config_type'">
            <a-tag :color="getTypeColor(record.config_type)">
              {{ getTypeName(record.config_type) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'config_value'">
            <div class="config-value">
              <span v-if="record.config_type === 'boolean'">
                <a-tag :color="record.config_value === 'true' ? 'green' : 'red'">
                  {{ record.config_value === 'true' ? '是' : '否' }}
                </a-tag>
              </span>
              <span v-else-if="record.config_type === 'json'">
                <a-tooltip :title="record.config_value">
                  <span class="json-preview">{{ getJsonPreview(record.config_value) }}</span>
                </a-tooltip>
              </span>
              <span v-else>{{ record.config_value }}</span>
            </div>
          </template>
          <template v-else-if="column.key === 'is_system'">
            <a-tag :color="record.is_system ? 'orange' : 'blue'">
              {{ record.is_system ? '系统配置' : '自定义配置' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'status'">
            <a-tag :color="record.status === 1 ? 'green' : 'red'">
              {{ record.status === 1 ? '启用' : '禁用' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a @click="editConfig(record)">编辑</a>
              <a @click="toggleStatus(record)" v-if="!record.is_system">
                {{ record.status === 1 ? '禁用' : '启用' }}
              </a>
              <a-dropdown>
                <a>更多 <DownOutlined /></a>
                <template #overlay>
                  <a-menu @click="({ key }) => handleMoreAction(key, record)">
                    <a-menu-item key="copy" v-if="!record.is_system">复制配置</a-menu-item>
                    <a-menu-item key="export">导出配置</a-menu-item>
                    <a-menu-divider v-if="!record.is_system" />
                    <a-menu-item key="delete" v-if="!record.is_system" style="color: #ff4d4f">删除</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑配置模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :confirm-loading="modalLoading"
      width="600px"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-form-item label="配置键" name="config_key">
          <a-input 
            v-model:value="formData.config_key" 
            placeholder="请输入配置键" 
            :disabled="formData.id && formData.is_system"
          />
        </a-form-item>
        
        <a-form-item label="配置类型" name="config_type">
          <a-select 
            v-model:value="formData.config_type" 
            placeholder="请选择配置类型"
            :disabled="formData.id && formData.is_system"
            @change="handleTypeChange"
          >
            <a-select-option value="string">字符串</a-select-option>
            <a-select-option value="number">数字</a-select-option>
            <a-select-option value="boolean">布尔值</a-select-option>
            <a-select-option value="json">JSON</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="配置值" name="config_value">
          <a-input-number 
            v-if="formData.config_type === 'number'"
            v-model:value="formData.config_value" 
            placeholder="请输入数字"
            style="width: 100%"
          />
          <a-select 
            v-else-if="formData.config_type === 'boolean'"
            v-model:value="formData.config_value" 
            placeholder="请选择布尔值"
          >
            <a-select-option value="true">是</a-select-option>
            <a-select-option value="false">否</a-select-option>
          </a-select>
          <a-textarea 
            v-else-if="formData.config_type === 'json'"
            v-model:value="formData.config_value" 
            placeholder="请输入JSON格式的配置值"
            :rows="6"
          />
          <a-input 
            v-else
            v-model:value="formData.config_value" 
            placeholder="请输入配置值"
          />
        </a-form-item>
        
        <a-form-item label="配置分组" name="group">
          <a-select v-model:value="formData.group" placeholder="请选择配置分组">
            <a-select-option value="system">系统配置</a-select-option>
            <a-select-option value="business">业务配置</a-select-option>
            <a-select-option value="notification">通知配置</a-select-option>
            <a-select-option value="security">安全配置</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="配置描述" name="description">
          <a-textarea v-model:value="formData.description" placeholder="请输入配置描述" :rows="3" />
        </a-form-item>
        
        <a-form-item label="状态" name="status" v-if="!formData.is_system">
          <a-radio-group v-model:value="formData.status">
            <a-radio :value="1">启用</a-radio>
            <a-radio :value="0">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import {
  getSystemConfigs,
  createSystemConfig,
  updateSystemConfig,
  deleteSystemConfig
} from '@/api/system'

// 响应式数据
const tableData = ref([])
const tableLoading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const formRef = ref()
const activeGroup = ref('all')

// 搜索表单
const searchForm = reactive({
  config_key: '',
  config_type: '',
  status: null,
  group: ''
})

// 表单数据
const formData = reactive({
  id: null,
  config_key: '',
  config_value: '',
  config_type: 'string',
  group: 'system',
  description: '',
  is_system: false,
  status: 1
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 计算属性
const modalTitle = computed(() => formData.id ? '编辑配置' : '新增配置')

// 表格列配置
const columns = [
  { title: '配置键', dataIndex: 'config_key', key: 'config_key', width: 200 },
  { title: '配置值', key: 'config_value', ellipsis: true },
  { title: '类型', key: 'config_type', width: 100 },
  { title: '分组', dataIndex: 'group', key: 'group', width: 120 },
  { title: '类别', key: 'is_system', width: 120 },
  { title: '状态', key: 'status', width: 80 },
  { title: '描述', dataIndex: 'description', key: 'description', ellipsis: true },
  { title: '操作', key: 'action', width: 150, fixed: 'right' }
]

// 表单验证规则
const formRules = {
  config_key: [{ required: true, message: '请输入配置键', trigger: 'blur' }],
  config_value: [{ required: true, message: '请输入配置值', trigger: 'blur' }],
  config_type: [{ required: true, message: '请选择配置类型', trigger: 'change' }],
  group: [{ required: true, message: '请选择配置分组', trigger: 'change' }]
}

// 获取类型名称
const getTypeName = (type) => {
  const names = { 
    string: '字符串', 
    number: '数字', 
    boolean: '布尔值', 
    json: 'JSON' 
  }
  return names[type] || '未知'
}

// 获取类型颜色
const getTypeColor = (type) => {
  const colors = { 
    string: 'blue', 
    number: 'green', 
    boolean: 'orange', 
    json: 'purple' 
  }
  return colors[type] || 'default'
}

// 获取JSON预览
const getJsonPreview = (jsonStr) => {
  try {
    const obj = JSON.parse(jsonStr)
    const keys = Object.keys(obj)
    if (keys.length > 3) {
      return `{${keys.slice(0, 3).join(', ')}...}`
    }
    return `{${keys.join(', ')}}`
  } catch {
    return jsonStr.substring(0, 20) + '...'
  }
}

// 加载配置列表
const loadConfigs = async () => {
  tableLoading.value = true
  try {
    const params = {
      page: pagination.current,
      page_size: pagination.pageSize,
      ...searchForm
    }
    
    if (activeGroup.value !== 'all') {
      params.group = activeGroup.value
    }
    
    const response = await getSystemConfigs(params)
    tableData.value = response.data.list || []
    pagination.total = response.data.total || 0
  } catch (error) {
    message.error('加载配置列表失败')
  } finally {
    tableLoading.value = false
  }
}

// 处理分组变化
const handleGroupChange = (group) => {
  activeGroup.value = group
  searchForm.group = group === 'all' ? '' : group
  pagination.current = 1
  loadConfigs()
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadConfigs()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    config_key: '',
    config_type: '',
    status: null,
    group: activeGroup.value === 'all' ? '' : activeGroup.value
  })
  pagination.current = 1
  loadConfigs()
}

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadConfigs()
}

// 显示创建模态框
const showCreateModal = () => {
  resetForm()
  formData.group = activeGroup.value === 'all' ? 'system' : activeGroup.value
  modalVisible.value = true
}

// 编辑配置
const editConfig = (config) => {
  Object.assign(formData, {
    id: config.id,
    config_key: config.config_key,
    config_value: config.config_value,
    config_type: config.config_type,
    group: config.group,
    description: config.description,
    is_system: config.is_system,
    status: config.status
  })
  modalVisible.value = true
}

// 处理类型变化
const handleTypeChange = (type) => {
  if (type === 'boolean') {
    formData.config_value = 'true'
  } else if (type === 'number') {
    formData.config_value = 0
  } else if (type === 'json') {
    formData.config_value = '{}'
  } else {
    formData.config_value = ''
  }
}

// 切换状态
const toggleStatus = async (config) => {
  const newStatus = config.status === 1 ? 0 : 1
  const action = newStatus === 1 ? '启用' : '禁用'
  
  Modal.confirm({
    title: `${action}配置`,
    content: `确定要${action}配置"${config.config_key}"吗？`,
    onOk: async () => {
      try {
        await updateSystemConfig(config.id, { status: newStatus })
        message.success(`${action}成功`)
        loadConfigs()
      } catch (error) {
        message.error(`${action}失败`)
      }
    }
  })
}

// 更多操作
const handleMoreAction = async (key, config) => {
  switch (key) {
    case 'copy':
      copyConfig(config)
      break
    case 'export':
      exportConfig(config)
      break
    case 'delete':
      deleteConfigConfirm(config)
      break
  }
}

// 复制配置
const copyConfig = (config) => {
  Object.assign(formData, {
    id: null,
    config_key: `${config.config_key}_copy`,
    config_value: config.config_value,
    config_type: config.config_type,
    group: config.group,
    description: config.description,
    is_system: false,
    status: config.status
  })
  modalVisible.value = true
}

// 导出配置
const exportConfig = (config) => {
  const data = {
    config_key: config.config_key,
    config_value: config.config_value,
    config_type: config.config_type,
    group: config.group,
    description: config.description
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${config.config_key}.json`
  a.click()
  URL.revokeObjectURL(url)
  
  message.success('配置导出成功')
}

// 删除配置确认
const deleteConfigConfirm = (config) => {
  Modal.confirm({
    title: '删除配置',
    content: `确定要删除配置"${config.config_key}"吗？删除后不可恢复。`,
    onOk: async () => {
      try {
        await deleteSystemConfig(config.id)
        message.success('删除成功')
        loadConfigs()
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

// 刷新配置
const refreshConfigs = () => {
  loadConfigs()
  message.success('配置已刷新')
}

// 处理模态框确认
const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    modalLoading.value = true
    
    // 验证JSON格式
    if (formData.config_type === 'json') {
      try {
        JSON.parse(formData.config_value)
      } catch {
        message.error('JSON格式不正确')
        return
      }
    }
    
    if (formData.id) {
      await updateSystemConfig(formData.id, formData)
      message.success('更新成功')
    } else {
      await createSystemConfig(formData)
      message.success('创建成功')
    }
    
    modalVisible.value = false
    loadConfigs()
  } catch (error) {
    if (error.errorFields) {
      message.error('请检查表单输入')
    } else {
      message.error('操作失败')
    }
  } finally {
    modalLoading.value = false
  }
}

// 处理模态框取消
const handleModalCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: null,
    config_key: '',
    config_value: '',
    config_type: 'string',
    group: 'system',
    description: '',
    is_system: false,
    status: 1
  })
  formRef.value?.resetFields()
}

// 组件挂载时加载数据
onMounted(() => {
  loadConfigs()
})
</script>

<style scoped>
.system-config {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h3 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.config-value {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.json-preview {
  font-family: 'Courier New', monospace;
  color: #666;
  cursor: pointer;
}
</style>
