package handler

import (
	"net/http"
	"quality_control/backend/internal/models"
	"quality_control/backend/internal/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

type SystemHandler struct {
	systemService *service.SystemService
}

func NewSystemHandler(systemService *service.SystemService) *SystemHandler {
	return &SystemHandler{
		systemService: systemService,
	}
}

// GetUnreadNotificationCount 获取未读通知数量
// @Summary 获取未读通知数量
// @Description 获取当前用户的未读通知数量
// @Tags 系统管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.Response{data=int64}
// @Failure 401 {object} models.Response
// @Router /system/notifications/unread-count [get]
func (h *SystemHandler) GetUnreadNotificationCount(c *gin.Context) {
	userID, err := GetCurrentUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.Response{
			Code:    401,
			Message: err.Error(),
		})
		return
	}

	count, err := h.systemService.GetUnreadNotificationCount(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "获取未读通知数量失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    count,
	})
}

// GetSystemStats 获取系统统计信息
// @Summary 获取系统统计信息
// @Description 获取系统各模块的统计信息
// @Tags 系统管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.Response{data=map[string]interface{}}
// @Failure 500 {object} models.Response
// @Router /system/stats [get]
func (h *SystemHandler) GetSystemStats(c *gin.Context) {
	stats, err := h.systemService.GetSystemStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "获取系统统计信息失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    stats,
	})
}

// GetNotifications 获取通知列表
// @Summary 获取通知列表
// @Description 获取当前用户的通知列表
// @Tags 系统管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param type query string false "通知类型"
// @Param is_read query bool false "是否已读"
// @Param priority query string false "优先级"
// @Success 200 {object} models.Response{data=service.UserListResponse}
// @Failure 401 {object} models.Response
// @Router /system/notifications [get]
func (h *SystemHandler) GetNotifications(c *gin.Context) {
	userID, err := GetCurrentUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.Response{
			Code:    401,
			Message: err.Error(),
		})
		return
	}

	var req service.NotificationListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	req.SetDefaults()

	resp, err := h.systemService.GetNotifications(userID, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "获取通知列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    resp,
	})
}

// MarkNotificationAsRead 标记通知为已读
// @Summary 标记通知为已读
// @Description 标记指定通知为已读
// @Tags 系统管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "通知ID"
// @Success 200 {object} models.Response
// @Failure 401 {object} models.Response
// @Router /system/notifications/{id}/read [put]
func (h *SystemHandler) MarkNotificationAsRead(c *gin.Context) {
	userID, err := GetCurrentUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.Response{
			Code:    401,
			Message: err.Error(),
		})
		return
	}

	idStr := c.Param("id")
	notificationID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "通知ID格式错误",
		})
		return
	}

	if err := h.systemService.MarkNotificationAsRead(userID, uint(notificationID)); err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "标记已读失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "标记成功",
	})
}

// MarkAllNotificationsAsRead 标记所有通知为已读
// @Summary 标记所有通知为已读
// @Description 标记当前用户所有通知为已读
// @Tags 系统管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.Response
// @Failure 401 {object} models.Response
// @Router /system/notifications/read-all [put]
func (h *SystemHandler) MarkAllNotificationsAsRead(c *gin.Context) {
	userID, err := GetCurrentUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.Response{
			Code:    401,
			Message: err.Error(),
		})
		return
	}

	if err := h.systemService.MarkAllNotificationsAsRead(userID); err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "标记已读失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "标记成功",
	})
}

// GetAuditLogs 获取审计日志列表
// @Summary 获取审计日志列表
// @Description 获取审计日志列表，支持分页和筛选
// @Tags 系统管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param user_id query int false "用户ID"
// @Param action query string false "操作"
// @Param resource query string false "资源"
// @Param start_date query string false "开始日期"
// @Param end_date query string false "结束日期"
// @Success 200 {object} models.Response{data=service.UserListResponse}
// @Failure 500 {object} models.Response
// @Router /system/audit-logs [get]
func (h *SystemHandler) GetAuditLogs(c *gin.Context) {
	var req service.AuditLogListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	req.SetDefaults()

	resp, err := h.systemService.GetAuditLogs(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "获取审计日志失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    resp,
	})
}

// GetDashboardData 获取仪表板数据
// @Summary 获取仪表板数据
// @Description 获取仪表板统计数据
// @Tags 系统管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.Response{data=map[string]interface{}}
// @Failure 500 {object} models.Response
// @Router /system/dashboard [get]
func (h *SystemHandler) GetDashboardData(c *gin.Context) {
	data, err := h.systemService.GetDashboardData()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "获取仪表板数据失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    data,
	})
}
