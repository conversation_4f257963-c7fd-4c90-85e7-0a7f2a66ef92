<template>
  <div class="role-manage">
    <a-card :bordered="false">
      <template #title>
        <div class="page-header">
          <h3>角色管理</h3>
          <div class="header-actions">
            <a-button type="primary" @click="showCreateModal">
              <template #icon><PlusOutlined /></template>
              新增角色
            </a-button>
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="角色名称">
            <a-input v-model:value="searchForm.name" placeholder="请输入角色名称" />
          </a-form-item>
          <a-form-item label="角色编码">
            <a-input v-model:value="searchForm.code" placeholder="请输入角色编码" />
          </a-form-item>
          <a-form-item label="状态">
            <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
              <a-select-option :value="1">启用</a-select-option>
              <a-select-option :value="0">禁用</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon><SearchOutlined /></template>
                搜索
              </a-button>
              <a-button @click="resetSearch">
                <template #icon><ReloadOutlined /></template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 角色表格 -->
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="tableLoading"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === 1 ? 'green' : 'red'">
              {{ record.status === 1 ? '启用' : '禁用' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a @click="editRole(record)">编辑</a>
              <a @click="assignPermissions(record)">分配权限</a>
              <a @click="toggleStatus(record)">
                {{ record.status === 1 ? '禁用' : '启用' }}
              </a>
              <a-dropdown>
                <a>更多 <DownOutlined /></a>
                <template #overlay>
                  <a-menu @click="({ key }) => handleMoreAction(key, record)">
                    <a-menu-item key="copy">复制角色</a-menu-item>
                    <a-menu-item key="view-users">查看用户</a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" style="color: #ff4d4f">删除</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑角色模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :confirm-loading="modalLoading"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-form-item label="角色名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入角色名称" />
        </a-form-item>
        
        <a-form-item label="角色编码" name="code">
          <a-input v-model:value="formData.code" placeholder="请输入角色编码" />
        </a-form-item>
        
        <a-form-item label="角色描述" name="description">
          <a-textarea v-model:value="formData.description" placeholder="请输入角色描述" :rows="3" />
        </a-form-item>
        
        <a-form-item label="排序" name="sort">
          <a-input-number v-model:value="formData.sort" :min="0" style="width: 100%" />
        </a-form-item>
        
        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="formData.status">
            <a-radio :value="1">启用</a-radio>
            <a-radio :value="0">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 权限分配模态框 -->
    <a-modal
      v-model:open="permissionModalVisible"
      title="分配权限"
      :confirm-loading="permissionModalLoading"
      width="800px"
      @ok="handlePermissionModalOk"
      @cancel="permissionModalVisible = false"
    >
      <div v-if="currentRole">
        <p>为角色 <strong>{{ currentRole.name }}</strong> 分配权限：</p>
        <a-tree
          v-model:checkedKeys="selectedPermissions"
          :tree-data="permissionTreeData"
          :field-names="{ children: 'children', title: 'name', key: 'id' }"
          checkable
          :default-expand-all="true"
        />
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import {
  getRoles,
  createRole,
  updateRole,
  deleteRole,
  getPermissionTree,
  getRolePermissions,
  assignRolePermissions
} from '@/api/rbac'

// 响应式数据
const tableData = ref([])
const tableLoading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const permissionModalVisible = ref(false)
const permissionModalLoading = ref(false)
const formRef = ref()
const permissionTreeData = ref([])
const currentRole = ref(null)
const selectedPermissions = ref([])

// 搜索表单
const searchForm = reactive({
  name: '',
  code: '',
  status: null
})

// 表单数据
const formData = reactive({
  id: null,
  name: '',
  code: '',
  description: '',
  sort: 0,
  status: 1
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 计算属性
const modalTitle = computed(() => formData.id ? '编辑角色' : '新增角色')

// 表格列配置
const columns = [
  { title: '角色名称', dataIndex: 'name', key: 'name' },
  { title: '角色编码', dataIndex: 'code', key: 'code' },
  { title: '角色描述', dataIndex: 'description', key: 'description', ellipsis: true },
  { title: '排序', dataIndex: 'sort', key: 'sort', width: 80 },
  { title: '状态', key: 'status', width: 80 },
  { title: '创建时间', dataIndex: 'created_at', key: 'created_at', width: 180 },
  { title: '操作', key: 'action', width: 200, fixed: 'right' }
]

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入角色编码', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 加载角色列表
const loadRoles = async () => {
  tableLoading.value = true
  try {
    const params = {
      page: pagination.current,
      page_size: pagination.pageSize,
      ...searchForm
    }
    
    const response = await getRoles(params)
    tableData.value = response.data.list || []
    pagination.total = response.data.total || 0
  } catch (error) {
    message.error('加载角色列表失败')
  } finally {
    tableLoading.value = false
  }
}

// 加载权限树
const loadPermissionTree = async () => {
  try {
    const response = await getPermissionTree()
    permissionTreeData.value = response.data || []
  } catch (error) {
    message.error('加载权限树失败')
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadRoles()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    code: '',
    status: null
  })
  pagination.current = 1
  loadRoles()
}

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadRoles()
}

// 显示创建模态框
const showCreateModal = () => {
  resetForm()
  modalVisible.value = true
}

// 编辑角色
const editRole = (role) => {
  Object.assign(formData, {
    id: role.id,
    name: role.name,
    code: role.code,
    description: role.description,
    sort: role.sort,
    status: role.status
  })
  modalVisible.value = true
}

// 分配权限
const assignPermissions = async (role) => {
  currentRole.value = role
  try {
    const response = await getRolePermissions(role.id)
    selectedPermissions.value = response.data.map(p => p.id)
    permissionModalVisible.value = true
  } catch (error) {
    message.error('获取角色权限失败')
  }
}

// 切换状态
const toggleStatus = async (role) => {
  const newStatus = role.status === 1 ? 0 : 1
  const action = newStatus === 1 ? '启用' : '禁用'
  
  Modal.confirm({
    title: `${action}角色`,
    content: `确定要${action}角色"${role.name}"吗？`,
    onOk: async () => {
      try {
        await updateRole(role.id, { status: newStatus })
        message.success(`${action}成功`)
        loadRoles()
      } catch (error) {
        message.error(`${action}失败`)
      }
    }
  })
}

// 更多操作
const handleMoreAction = async (key, role) => {
  switch (key) {
    case 'copy':
      copyRole(role)
      break
    case 'view-users':
      viewRoleUsers(role)
      break
    case 'delete':
      deleteRoleConfirm(role)
      break
  }
}

// 复制角色
const copyRole = (role) => {
  Object.assign(formData, {
    id: null,
    name: `${role.name}_副本`,
    code: `${role.code}_copy`,
    description: role.description,
    sort: role.sort,
    status: role.status
  })
  modalVisible.value = true
}

// 查看角色用户
const viewRoleUsers = (role) => {
  message.info('查看角色用户功能开发中...')
}

// 删除角色确认
const deleteRoleConfirm = (role) => {
  Modal.confirm({
    title: '删除角色',
    content: `确定要删除角色"${role.name}"吗？删除后不可恢复。`,
    onOk: async () => {
      try {
        await deleteRole(role.id)
        message.success('删除成功')
        loadRoles()
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

// 处理模态框确认
const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    modalLoading.value = true
    
    if (formData.id) {
      await updateRole(formData.id, formData)
      message.success('更新成功')
    } else {
      await createRole(formData)
      message.success('创建成功')
    }
    
    modalVisible.value = false
    loadRoles()
  } catch (error) {
    if (error.errorFields) {
      message.error('请检查表单输入')
    } else {
      message.error('操作失败')
    }
  } finally {
    modalLoading.value = false
  }
}

// 处理模态框取消
const handleModalCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 处理权限分配确认
const handlePermissionModalOk = async () => {
  try {
    permissionModalLoading.value = true
    await assignRolePermissions(currentRole.value.id, selectedPermissions.value)
    message.success('权限分配成功')
    permissionModalVisible.value = false
  } catch (error) {
    message.error('权限分配失败')
  } finally {
    permissionModalLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: null,
    name: '',
    code: '',
    description: '',
    sort: 0,
    status: 1
  })
  formRef.value?.resetFields()
}

// 组件挂载时加载数据
onMounted(() => {
  loadRoles()
  loadPermissionTree()
})
</script>

<style scoped>
.role-manage {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h3 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}
</style>
