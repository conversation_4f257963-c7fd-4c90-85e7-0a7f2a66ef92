<template>
  <div class="adjustment-detail">
    <a-card title="预算调整详情" :bordered="false">
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="调整编号">
          {{ adjustmentData.adjustment_no }}
        </a-descriptions-item>
        <a-descriptions-item label="调整类型">
          {{ getAdjustmentTypeName(adjustmentData.type) }}
        </a-descriptions-item>
        <a-descriptions-item label="申请人">
          {{ adjustmentData.applicant_name }}
        </a-descriptions-item>
        <a-descriptions-item label="申请部门">
          {{ adjustmentData.department_name }}
        </a-descriptions-item>
        <a-descriptions-item label="申请时间">
          {{ formatDate(adjustmentData.created_at) }}
        </a-descriptions-item>
        <a-descriptions-item label="调整状态">
          <a-tag :color="getStatusColor(adjustmentData.status)">
            {{ getStatusName(adjustmentData.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="调整原因" :span="2">
          {{ adjustmentData.reason }}
        </a-descriptions-item>
      </a-descriptions>

      <!-- 调整明细 -->
      <a-divider>调整明细</a-divider>
      <a-table
        :columns="detailColumns"
        :data-source="adjustmentData.details || []"
        :pagination="false"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'amount'">
            <span :class="{ 'text-red': record.amount < 0, 'text-green': record.amount > 0 }">
              {{ formatAmount(record.amount) }}
            </span>
          </template>
        </template>
      </a-table>

      <!-- 审批记录 -->
      <a-divider>审批记录</a-divider>
      <a-timeline>
        <a-timeline-item
          v-for="(record, index) in adjustmentData.approval_records || []"
          :key="index"
          :color="getApprovalColor(record.result)"
        >
          <div class="approval-record">
            <div class="record-header">
              <span class="approver">{{ record.approver_name }}</span>
              <span class="time">{{ formatDate(record.created_at) }}</span>
            </div>
            <div class="record-content">
              <a-tag :color="getApprovalColor(record.result)">
                {{ getApprovalResult(record.result) }}
              </a-tag>
              <span v-if="record.comment" class="comment">{{ record.comment }}</span>
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>
    </a-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import dayjs from 'dayjs'

// Props
const props = defineProps({
  adjustmentData: {
    type: Object,
    default: () => ({})
  }
})

// 表格列定义
const detailColumns = [
  {
    title: '预算科目',
    dataIndex: 'budget_item_name',
    key: 'budget_item_name'
  },
  {
    title: '原预算金额',
    dataIndex: 'original_amount',
    key: 'original_amount',
    align: 'right',
    customRender: ({ text }) => formatAmount(text)
  },
  {
    title: '调整金额',
    dataIndex: 'amount',
    key: 'amount',
    align: 'right'
  },
  {
    title: '调整后金额',
    dataIndex: 'new_amount',
    key: 'new_amount',
    align: 'right',
    customRender: ({ text }) => formatAmount(text)
  },
  {
    title: '调整说明',
    dataIndex: 'description',
    key: 'description'
  }
]

// 格式化金额
const formatAmount = (amount) => {
  if (amount === null || amount === undefined) return '-'
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(amount)
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

// 获取调整类型名称
const getAdjustmentTypeName = (type) => {
  const typeMap = {
    'increase': '预算增加',
    'decrease': '预算减少',
    'transfer': '预算调剂'
  }
  return typeMap[type] || type
}

// 获取状态名称
const getStatusName = (status) => {
  const statusMap = {
    'draft': '草稿',
    'pending': '待审批',
    'approved': '已通过',
    'rejected': '已驳回',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    'draft': 'default',
    'pending': 'processing',
    'approved': 'success',
    'rejected': 'error',
    'cancelled': 'default'
  }
  return colorMap[status] || 'default'
}

// 获取审批结果
const getApprovalResult = (result) => {
  const resultMap = {
    'approved': '通过',
    'rejected': '驳回',
    'pending': '待审批'
  }
  return resultMap[result] || result
}

// 获取审批颜色
const getApprovalColor = (result) => {
  const colorMap = {
    'approved': 'green',
    'rejected': 'red',
    'pending': 'blue'
  }
  return colorMap[result] || 'blue'
}
</script>

<style scoped>
.adjustment-detail {
  padding: 16px;
}

.text-red {
  color: #ff4d4f;
}

.text-green {
  color: #52c41a;
}

.approval-record {
  margin-bottom: 8px;
}

.record-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.approver {
  font-weight: 500;
}

.time {
  color: #999;
  font-size: 12px;
}

.record-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.comment {
  color: #666;
}
</style>
