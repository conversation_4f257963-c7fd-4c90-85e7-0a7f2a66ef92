# 依赖模块
node_modules/
frontend/node_modules/

# 构建输出
dist/
build/
frontend/dist/

# 日志文件
*.log
logs/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE文件
.vscode/
.idea/
*.swp
*.swo

# 操作系统文件
.DS_Store
Thumbs.db

# Go相关
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work

# 临时文件
tmp/
temp/

# 数据库文件
*.db
*.sqlite

# 配置文件（如果包含敏感信息）
config/local.yaml
config/production.yaml