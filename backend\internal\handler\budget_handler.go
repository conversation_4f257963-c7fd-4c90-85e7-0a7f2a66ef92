package handler

import (
	"net/http"
	"quality_control/backend/internal/models"
	"quality_control/backend/internal/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

type BudgetHandler struct {
	budgetService *service.BudgetService
}

func NewBudgetHandler(budgetService *service.BudgetService) *BudgetHandler {
	return &BudgetHandler{
		budgetService: budgetService,
	}
}

// GetBudgetSchemes 获取预算方案列表
// @Summary 获取预算方案列表
// @Description 获取预算方案列表，支持分页和筛选
// @Tags 预算管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param name query string false "方案名称"
// @Param year query int false "年度"
// @Param status query string false "状态"
// @Success 200 {object} models.Response{data=service.UserListResponse}
// @Failure 400 {object} models.Response
// @Router /budget/schemes [get]
func (h *BudgetHandler) GetBudgetSchemes(c *gin.Context) {
	var req service.BudgetSchemeListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	req.SetDefaults()

	resp, err := h.budgetService.GetBudgetSchemes(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "获取预算方案列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    resp,
	})
}

// GetBudgetScheme 获取预算方案详情
// @Summary 获取预算方案详情
// @Description 根据ID获取预算方案详情
// @Tags 预算管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "方案ID"
// @Success 200 {object} models.Response{data=models.BudgetScheme}
// @Failure 400 {object} models.Response
// @Router /budget/schemes/{id} [get]
func (h *BudgetHandler) GetBudgetScheme(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "方案ID格式错误",
		})
		return
	}

	scheme, err := h.budgetService.GetBudgetScheme(uint(id))
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    scheme,
	})
}

// CreateBudgetScheme 创建预算方案
// @Summary 创建预算方案
// @Description 创建新的预算方案
// @Tags 预算管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body service.CreateBudgetSchemeRequest true "创建预算方案请求"
// @Success 200 {object} models.Response{data=models.BudgetScheme}
// @Failure 400 {object} models.Response
// @Router /budget/schemes [post]
func (h *BudgetHandler) CreateBudgetScheme(c *gin.Context) {
	var req service.CreateBudgetSchemeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	userID, err := GetCurrentUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.Response{
			Code:    401,
			Message: err.Error(),
		})
		return
	}

	scheme, err := h.budgetService.CreateBudgetScheme(&req, userID)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "创建成功",
		Data:    scheme,
	})
}

// UpdateBudgetScheme 更新预算方案
// @Summary 更新预算方案
// @Description 更新预算方案信息
// @Tags 预算管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "方案ID"
// @Param request body service.UpdateBudgetSchemeRequest true "更新预算方案请求"
// @Success 200 {object} models.Response{data=models.BudgetScheme}
// @Failure 400 {object} models.Response
// @Router /budget/schemes/{id} [put]
func (h *BudgetHandler) UpdateBudgetScheme(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "方案ID格式错误",
		})
		return
	}

	var req service.UpdateBudgetSchemeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	scheme, err := h.budgetService.UpdateBudgetScheme(uint(id), &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "更新成功",
		Data:    scheme,
	})
}

// DeleteBudgetScheme 删除预算方案
// @Summary 删除预算方案
// @Description 删除预算方案
// @Tags 预算管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "方案ID"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Router /budget/schemes/{id} [delete]
func (h *BudgetHandler) DeleteBudgetScheme(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "方案ID格式错误",
		})
		return
	}

	if err := h.budgetService.DeleteBudgetScheme(uint(id)); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "删除成功",
	})
}

// SubmitBudgetScheme 提交预算方案
// @Summary 提交预算方案
// @Description 提交预算方案进行审批
// @Tags 预算管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "方案ID"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Router /budget/schemes/{id}/submit [post]
func (h *BudgetHandler) SubmitBudgetScheme(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "方案ID格式错误",
		})
		return
	}

	if err := h.budgetService.SubmitBudgetScheme(uint(id)); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "提交成功",
	})
}

// ApprovalRequest 审批请求
type ApprovalRequest struct {
	Approved bool   `json:"approved" binding:"required"`
	Comment  string `json:"comment"`
}

// ApproveBudgetScheme 审批预算方案
// @Summary 审批预算方案
// @Description 审批预算方案
// @Tags 预算管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "方案ID"
// @Param request body ApprovalRequest true "审批请求"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Router /budget/schemes/{id}/approve [post]
func (h *BudgetHandler) ApproveBudgetScheme(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "方案ID格式错误",
		})
		return
	}

	var req ApprovalRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	if err := h.budgetService.ApproveBudgetScheme(uint(id), req.Approved, req.Comment); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	message := "审批通过"
	if !req.Approved {
		message = "审批驳回"
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: message,
	})
}

// GetBudgetItemTree 获取预算科目树
// @Summary 获取预算科目树
// @Description 获取预算科目树形结构
// @Tags 预算管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.Response{data=[]service.BudgetItemTreeNode}
// @Failure 500 {object} models.Response
// @Router /budget/items/tree [get]
func (h *BudgetHandler) GetBudgetItemTree(c *gin.Context) {
	tree, err := h.budgetService.GetBudgetItemTree()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "获取预算科目树失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    tree,
	})
}

// CreateBudgetItem 创建预算科目
// @Summary 创建预算科目
// @Description 创建新的预算科目
// @Tags 预算管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body service.CreateBudgetItemRequest true "创建预算科目请求"
// @Success 200 {object} models.Response{data=models.BudgetItem}
// @Failure 400 {object} models.Response
// @Router /budget/items [post]
func (h *BudgetHandler) CreateBudgetItem(c *gin.Context) {
	var req service.CreateBudgetItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	item, err := h.budgetService.CreateBudgetItem(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "创建成功",
		Data:    item,
	})
}

// GetBudgetAnalysis 获取预算分析数据
// @Summary 获取预算分析数据
// @Description 获取预算分析数据，支持分页和筛选
// @Tags 预算管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param year query int false "年度"
// @Param department_ids query []int false "部门ID列表"
// @Param month_range query []int false "月份范围"
// @Param budget_type query string false "预算类型"
// @Success 200 {object} models.Response{data=service.UserListResponse}
// @Failure 400 {object} models.Response
// @Router /budget/analysis [get]
func (h *BudgetHandler) GetBudgetAnalysis(c *gin.Context) {
	var req service.BudgetAnalysisRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	req.SetDefaults()

	resp, err := h.budgetService.GetBudgetAnalysis(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "获取预算分析数据失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    resp,
	})
}
