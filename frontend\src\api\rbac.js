import request from './request'

// 角色管理
export const getRoles = (params) => {
  return request.get('/roles', { params })
}

export const getRole = (id) => {
  return request.get(`/roles/${id}`)
}

export const createRole = (data) => {
  return request.post('/roles', data)
}

export const updateRole = (id, data) => {
  return request.put(`/roles/${id}`, data)
}

export const deleteRole = (id) => {
  return request.delete(`/roles/${id}`)
}

// 权限管理
export const getPermissions = (params) => {
  return request.get('/permissions', { params })
}

export const getPermissionTree = () => {
  return request.get('/permissions/tree')
}

export const createPermission = (data) => {
  return request.post('/permissions', data)
}

export const updatePermission = (id, data) => {
  return request.put(`/permissions/${id}`, data)
}

export const deletePermission = (id) => {
  return request.delete(`/permissions/${id}`)
}

// 角色权限关联
export const getRolePermissions = (roleId) => {
  return request.get(`/roles/${roleId}/permissions`)
}

export const assignRolePermissions = (roleId, permissionIds) => {
  return request.post(`/roles/${roleId}/permissions`, { permission_ids: permissionIds })
}

// 用户权限检查
export const checkUserPermission = (permission) => {
  return request.get('/auth/check-permission', { params: { permission } })
}

// 获取用户菜单
export const getUserMenus = () => {
  return request.get('/auth/menus')
}

// 岗位管理
export const getPositions = (params) => {
  return request.get('/positions', { params })
}

export const getPosition = (id) => {
  return request.get(`/positions/${id}`)
}

export const createPosition = (data) => {
  return request.post('/positions', data)
}

export const updatePosition = (id, data) => {
  return request.put(`/positions/${id}`, data)
}

export const deletePosition = (id) => {
  return request.delete(`/positions/${id}`)
}

// 用户岗位关联
export const getUserPositions = (userId) => {
  return request.get(`/users/${userId}/positions`)
}

export const assignUserPositions = (userId, positionIds) => {
  return request.post(`/users/${userId}/positions`, { position_ids: positionIds })
}
