-- 医院运营管理系统完整数据库脚本
-- 创建时间: 2025-08-19
-- 包含：表结构、外键约束、索引、初始数据

-- ============================================================================
-- 1. 删除已存在的表（按依赖关系逆序）
-- ============================================================================
DROP TABLE IF EXISTS notification_recipients CASCADE;
DROP TABLE IF EXISTS audit_logs CASCADE;
DROP TABLE IF EXISTS workflow_tasks CASCADE;
DROP TABLE IF EXISTS workflow_instances CASCADE;
DROP TABLE IF EXISTS invoices CASCADE;
DROP TABLE IF EXISTS payments CASCADE;
DROP TABLE IF EXISTS application_budget_links CASCADE;
DROP TABLE IF EXISTS expenditure_attachments CASCADE;
DROP TABLE IF EXISTS expenditure_details CASCADE;
DROP TABLE IF EXISTS expenditure_applications CASCADE;
DROP TABLE IF EXISTS budget_adjustments CASCADE;
DROP TABLE IF EXISTS budget_executions CASCADE;
DROP TABLE IF EXISTS budget_data CASCADE;
DROP TABLE IF EXISTS user_positions CASCADE;
DROP TABLE IF EXISTS role_permissions CASCADE;
DROP TABLE IF EXISTS user_roles CASCADE;
DROP TABLE IF EXISTS notifications CASCADE;
DROP TABLE IF EXISTS file_uploads CASCADE;
DROP TABLE IF EXISTS system_stats CASCADE;
DROP TABLE IF EXISTS operation_logs CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS budget_schemes CASCADE;
DROP TABLE IF EXISTS workflow_definitions CASCADE;
DROP TABLE IF EXISTS expense_standards CASCADE;
DROP TABLE IF EXISTS system_configs CASCADE;
DROP TABLE IF EXISTS budget_items CASCADE;
DROP TABLE IF EXISTS cost_centers CASCADE;
DROP TABLE IF EXISTS positions CASCADE;
DROP TABLE IF EXISTS permissions CASCADE;
DROP TABLE IF EXISTS roles CASCADE;
DROP TABLE IF EXISTS organizations CASCADE;

-- ============================================================================
-- 2. 创建基础表
-- ============================================================================

-- 组织架构表
CREATE TABLE organizations (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    type VARCHAR(20) NOT NULL, -- hospital, department, section
    parent_id BIGINT,
    level INTEGER DEFAULT 1,
    sort INTEGER DEFAULT 0,
    description VARCHAR(255),
    status VARCHAR(20) DEFAULT 'active',
    manager_id BIGINT,
    phone VARCHAR(20),
    email VARCHAR(100),
    address VARCHAR(255)
);

-- 角色表
CREATE TABLE roles (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    name VARCHAR(50) UNIQUE NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    description VARCHAR(255),
    status VARCHAR(20) DEFAULT 'active'
);

-- 权限表
CREATE TABLE permissions (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(100) UNIQUE NOT NULL,
    type VARCHAR(20) NOT NULL, -- menu, button, api
    parent_id BIGINT,
    path VARCHAR(255),
    method VARCHAR(10),
    icon VARCHAR(50),
    sort INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active'
);

-- 岗位表
CREATE TABLE positions (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    organization_id BIGINT NOT NULL,
    level INTEGER DEFAULT 1,
    description VARCHAR(255),
    status VARCHAR(20) DEFAULT 'active'
);

-- 成本中心表
CREATE TABLE cost_centers (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    organization_id BIGINT NOT NULL,
    type VARCHAR(20) NOT NULL, -- revenue, cost, profit
    description VARCHAR(255),
    status VARCHAR(20) DEFAULT 'active'
);

-- 预算科目表
CREATE TABLE budget_items (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    parent_id BIGINT,
    level INTEGER DEFAULT 1,
    type VARCHAR(20) NOT NULL, -- income, expense
    category VARCHAR(50),
    unit VARCHAR(20),
    sort INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active',
    description VARCHAR(255),
    is_controlled BOOLEAN DEFAULT true,
    control_type INTEGER DEFAULT 1 -- 1:刚性控制 2:柔性控制
);

-- 系统配置表
CREATE TABLE system_configs (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    config_type VARCHAR(20) DEFAULT 'string', -- string, number, boolean, json
    category VARCHAR(50),
    description VARCHAR(255),
    is_system BOOLEAN DEFAULT false,
    status VARCHAR(20) DEFAULT 'active'
);

-- 费用标准表
CREATE TABLE expense_standards (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL,
    level VARCHAR(20),
    amount DECIMAL(18,2) NOT NULL,
    unit VARCHAR(20),
    description VARCHAR(255),
    effective_date DATE,
    expire_date DATE,
    status VARCHAR(20) DEFAULT 'active'
);

-- 工作流定义表
CREATE TABLE workflow_definitions (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    category VARCHAR(50) NOT NULL,
    version INTEGER DEFAULT 1,
    definition JSONB NOT NULL,
    description VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    status VARCHAR(20) DEFAULT 'active'
);

-- 用户表
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20),
    real_name VARCHAR(50) NOT NULL,
    avatar VARCHAR(255),
    status VARCHAR(20) DEFAULT 'active',
    last_login_at TIMESTAMPTZ,
    last_login_ip VARCHAR(45),
    organization_id BIGINT NOT NULL
);

-- 预算方案表
CREATE TABLE budget_schemes (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    name VARCHAR(100) NOT NULL,
    year INTEGER NOT NULL,
    organization_id BIGINT NOT NULL,
    status VARCHAR(20) DEFAULT 'draft', -- draft, submitted, approved, rejected
    description VARCHAR(255),
    creator_id BIGINT NOT NULL,
    approver_id BIGINT,
    approved_at TIMESTAMPTZ,
    submitted_at TIMESTAMPTZ
);

-- 通知表
CREATE TABLE notifications (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    title VARCHAR(200) NOT NULL,
    content TEXT,
    type VARCHAR(20) NOT NULL, -- system, workflow, reminder
    priority INTEGER DEFAULT 1, -- 1:低 2:中 3:高
    status VARCHAR(20) DEFAULT 'active',
    publish_time TIMESTAMPTZ,
    expire_time TIMESTAMPTZ,
    sender_id BIGINT
);

-- 文件上传表
CREATE TABLE file_uploads (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    file_name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    file_type VARCHAR(50),
    file_path VARCHAR(500) NOT NULL,
    file_url VARCHAR(500),
    business_type VARCHAR(50),
    business_id VARCHAR(64),
    uploader_id BIGINT NOT NULL
);

-- 系统统计表
CREATE TABLE system_stats (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    stat_date DATE NOT NULL,
    stat_type VARCHAR(50) NOT NULL,
    stat_value JSONB NOT NULL,
    organization_id BIGINT
);

-- 操作日志表
CREATE TABLE operation_logs (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    user_id BIGINT,
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(100),
    resource_id VARCHAR(64),
    details JSONB,
    ip_address VARCHAR(45),
    user_agent VARCHAR(500)
);

-- 用户角色关联表
CREATE TABLE user_roles (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    UNIQUE(user_id, role_id)
);

-- 角色权限关联表
CREATE TABLE role_permissions (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    role_id BIGINT NOT NULL,
    permission_id BIGINT NOT NULL,
    UNIQUE(role_id, permission_id)
);

-- 用户岗位关联表
CREATE TABLE user_positions (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    user_id BIGINT NOT NULL,
    position_id BIGINT NOT NULL,
    UNIQUE(user_id, position_id)
);

-- 预算数据表
CREATE TABLE budget_data (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    scheme_id BIGINT NOT NULL,
    budget_item_id BIGINT NOT NULL,
    organization_id BIGINT NOT NULL,
    cost_center_id BIGINT,
    amount DECIMAL(18,2) NOT NULL DEFAULT 0,
    quantity DECIMAL(18,2),
    unit_price DECIMAL(18,2),
    remark VARCHAR(500)
);

-- 预算执行表
CREATE TABLE budget_executions (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    budget_data_id BIGINT NOT NULL,
    execution_date DATE NOT NULL,
    amount DECIMAL(18,2) NOT NULL,
    balance DECIMAL(18,2) NOT NULL,
    business_type VARCHAR(50),
    business_id VARCHAR(64),
    remark VARCHAR(500)
);

-- 预算调整表
CREATE TABLE budget_adjustments (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    adjustment_no VARCHAR(64) UNIQUE NOT NULL,
    budget_data_id BIGINT NOT NULL,
    adjustment_type VARCHAR(20) NOT NULL, -- increase, decrease, transfer
    amount DECIMAL(18,2) NOT NULL,
    reason VARCHAR(500),
    status VARCHAR(20) DEFAULT 'pending',
    applicant_id BIGINT NOT NULL,
    approver_id BIGINT,
    applied_at TIMESTAMPTZ DEFAULT NOW(),
    approved_at TIMESTAMPTZ
);

-- 支出申请表
CREATE TABLE expenditure_applications (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    application_no VARCHAR(64) UNIQUE NOT NULL,
    title VARCHAR(200) NOT NULL,
    type VARCHAR(20) NOT NULL, -- advance, reimbursement
    total_amount DECIMAL(18,2) NOT NULL DEFAULT 0,
    status VARCHAR(20) DEFAULT 'draft',
    urgency VARCHAR(20) DEFAULT 'normal',
    description TEXT,
    applicant_id BIGINT NOT NULL,
    department_id BIGINT NOT NULL,
    related_application_id BIGINT,
    submitted_at TIMESTAMPTZ,
    approved_at TIMESTAMPTZ,
    rejected_at TIMESTAMPTZ,
    paid_at TIMESTAMPTZ
);

-- 支出明细表
CREATE TABLE expenditure_details (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    application_id BIGINT NOT NULL,
    budget_item_id BIGINT NOT NULL,
    description VARCHAR(255) NOT NULL,
    amount DECIMAL(18,2) NOT NULL,
    quantity DECIMAL(18,2),
    unit_price DECIMAL(18,2),
    expense_date DATE,
    vendor VARCHAR(100),
    remark VARCHAR(500)
);

-- 支出附件表
CREATE TABLE expenditure_attachments (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    application_id BIGINT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    file_type VARCHAR(50),
    file_path VARCHAR(500) NOT NULL,
    file_url VARCHAR(500)
);

-- 申请预算关联表
CREATE TABLE application_budget_links (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    application_id BIGINT NOT NULL,
    budget_data_id BIGINT NOT NULL,
    amount DECIMAL(18,2) NOT NULL,
    UNIQUE(application_id, budget_data_id)
);

-- 支付记录表
CREATE TABLE payments (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    payment_no VARCHAR(64) UNIQUE NOT NULL,
    application_id BIGINT NOT NULL,
    amount DECIMAL(18,2) NOT NULL,
    payment_method VARCHAR(50),
    payment_date TIMESTAMPTZ,
    status VARCHAR(20) DEFAULT 'unpaid',
    remark VARCHAR(500),
    payer_id BIGINT
);

-- 发票表
CREATE TABLE invoices (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    invoice_code VARCHAR(50) NOT NULL,
    invoice_no VARCHAR(50) NOT NULL,
    invoice_date DATE NOT NULL,
    amount DECIMAL(18,2) NOT NULL,
    tax_amount DECIMAL(18,2),
    vendor_name VARCHAR(200),
    vendor_tax_no VARCHAR(50),
    check_code VARCHAR(50),
    is_used BOOLEAN DEFAULT false,
    used_at TIMESTAMPTZ,
    application_id BIGINT,
    UNIQUE(invoice_code, invoice_no)
);

-- 工作流实例表
CREATE TABLE workflow_instances (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    instance_id VARCHAR(64) UNIQUE NOT NULL,
    definition_id BIGINT NOT NULL,
    business_type VARCHAR(50) NOT NULL,
    business_id VARCHAR(64) NOT NULL,
    status VARCHAR(20) DEFAULT 'running',
    current_node VARCHAR(100),
    initiator_id BIGINT NOT NULL,
    started_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ
);

-- 工作流任务表
CREATE TABLE workflow_tasks (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    task_id VARCHAR(64) UNIQUE NOT NULL,
    instance_id VARCHAR(64) NOT NULL,
    node_id VARCHAR(100) NOT NULL,
    task_name VARCHAR(200) NOT NULL,
    task_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    assignee_id BIGINT,
    assigned_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    comment TEXT,
    form_data JSONB
);

-- 通知接收记录表
CREATE TABLE notification_recipients (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    notification_id BIGINT NOT NULL,
    recipient_id BIGINT NOT NULL,
    is_read BOOLEAN DEFAULT false,
    read_time TIMESTAMPTZ,
    UNIQUE(notification_id, recipient_id)
);

-- 审计日志表
CREATE TABLE audit_logs (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    user_id BIGINT,
    action VARCHAR(255) NOT NULL,
    target_id VARCHAR(64),
    target_type VARCHAR(64),
    old_value JSONB,
    new_value JSONB,
    ip_address VARCHAR(64),
    user_agent VARCHAR(500)
);

-- ============================================================================
-- 3. 添加外键约束
-- ============================================================================

-- 组织架构外键
ALTER TABLE organizations ADD CONSTRAINT fk_organizations_parent
    FOREIGN KEY (parent_id) REFERENCES organizations(id);

-- 权限外键
ALTER TABLE permissions ADD CONSTRAINT fk_permissions_parent
    FOREIGN KEY (parent_id) REFERENCES permissions(id);

-- 岗位外键
ALTER TABLE positions ADD CONSTRAINT fk_positions_organization
    FOREIGN KEY (organization_id) REFERENCES organizations(id);

-- 成本中心外键
ALTER TABLE cost_centers ADD CONSTRAINT fk_cost_centers_organization
    FOREIGN KEY (organization_id) REFERENCES organizations(id);

-- 预算科目外键
ALTER TABLE budget_items ADD CONSTRAINT fk_budget_items_parent
    FOREIGN KEY (parent_id) REFERENCES budget_items(id);

-- 用户外键
ALTER TABLE users ADD CONSTRAINT fk_users_organization
    FOREIGN KEY (organization_id) REFERENCES organizations(id);

-- 组织管理员外键（延后添加）
ALTER TABLE organizations ADD CONSTRAINT fk_organizations_manager
    FOREIGN KEY (manager_id) REFERENCES users(id);

-- 预算方案外键
ALTER TABLE budget_schemes ADD CONSTRAINT fk_budget_schemes_organization
    FOREIGN KEY (organization_id) REFERENCES organizations(id);
ALTER TABLE budget_schemes ADD CONSTRAINT fk_budget_schemes_creator
    FOREIGN KEY (creator_id) REFERENCES users(id);
ALTER TABLE budget_schemes ADD CONSTRAINT fk_budget_schemes_approver
    FOREIGN KEY (approver_id) REFERENCES users(id);

-- 通知外键
ALTER TABLE notifications ADD CONSTRAINT fk_notifications_sender
    FOREIGN KEY (sender_id) REFERENCES users(id);

-- 文件上传外键
ALTER TABLE file_uploads ADD CONSTRAINT fk_file_uploads_uploader
    FOREIGN KEY (uploader_id) REFERENCES users(id);

-- 系统统计外键
ALTER TABLE system_stats ADD CONSTRAINT fk_system_stats_organization
    FOREIGN KEY (organization_id) REFERENCES organizations(id);

-- 操作日志外键
ALTER TABLE operation_logs ADD CONSTRAINT fk_operation_logs_user
    FOREIGN KEY (user_id) REFERENCES users(id);

-- 用户角色关联外键
ALTER TABLE user_roles ADD CONSTRAINT fk_user_roles_user
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE user_roles ADD CONSTRAINT fk_user_roles_role
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE;

-- 角色权限关联外键
ALTER TABLE role_permissions ADD CONSTRAINT fk_role_permissions_role
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE;
ALTER TABLE role_permissions ADD CONSTRAINT fk_role_permissions_permission
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE;

-- 用户岗位关联外键
ALTER TABLE user_positions ADD CONSTRAINT fk_user_positions_user
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE user_positions ADD CONSTRAINT fk_user_positions_position
    FOREIGN KEY (position_id) REFERENCES positions(id) ON DELETE CASCADE;

-- 预算数据外键
ALTER TABLE budget_data ADD CONSTRAINT fk_budget_data_scheme
    FOREIGN KEY (scheme_id) REFERENCES budget_schemes(id) ON DELETE CASCADE;
ALTER TABLE budget_data ADD CONSTRAINT fk_budget_data_budget_item
    FOREIGN KEY (budget_item_id) REFERENCES budget_items(id);
ALTER TABLE budget_data ADD CONSTRAINT fk_budget_data_organization
    FOREIGN KEY (organization_id) REFERENCES organizations(id);
ALTER TABLE budget_data ADD CONSTRAINT fk_budget_data_cost_center
    FOREIGN KEY (cost_center_id) REFERENCES cost_centers(id);

-- 预算执行外键
ALTER TABLE budget_executions ADD CONSTRAINT fk_budget_executions_budget_data
    FOREIGN KEY (budget_data_id) REFERENCES budget_data(id);

-- 预算调整外键
ALTER TABLE budget_adjustments ADD CONSTRAINT fk_budget_adjustments_budget_data
    FOREIGN KEY (budget_data_id) REFERENCES budget_data(id);
ALTER TABLE budget_adjustments ADD CONSTRAINT fk_budget_adjustments_applicant
    FOREIGN KEY (applicant_id) REFERENCES users(id);
ALTER TABLE budget_adjustments ADD CONSTRAINT fk_budget_adjustments_approver
    FOREIGN KEY (approver_id) REFERENCES users(id);

-- 支出申请外键
ALTER TABLE expenditure_applications ADD CONSTRAINT fk_expenditure_applications_applicant
    FOREIGN KEY (applicant_id) REFERENCES users(id);
ALTER TABLE expenditure_applications ADD CONSTRAINT fk_expenditure_applications_department
    FOREIGN KEY (department_id) REFERENCES organizations(id);
ALTER TABLE expenditure_applications ADD CONSTRAINT fk_expenditure_applications_related
    FOREIGN KEY (related_application_id) REFERENCES expenditure_applications(id);

-- 支出明细外键
ALTER TABLE expenditure_details ADD CONSTRAINT fk_expenditure_details_application
    FOREIGN KEY (application_id) REFERENCES expenditure_applications(id) ON DELETE CASCADE;
ALTER TABLE expenditure_details ADD CONSTRAINT fk_expenditure_details_budget_item
    FOREIGN KEY (budget_item_id) REFERENCES budget_items(id);

-- 支出附件外键
ALTER TABLE expenditure_attachments ADD CONSTRAINT fk_expenditure_attachments_application
    FOREIGN KEY (application_id) REFERENCES expenditure_applications(id) ON DELETE CASCADE;

-- 申请预算关联外键
ALTER TABLE application_budget_links ADD CONSTRAINT fk_application_budget_links_application
    FOREIGN KEY (application_id) REFERENCES expenditure_applications(id) ON DELETE CASCADE;
ALTER TABLE application_budget_links ADD CONSTRAINT fk_application_budget_links_budget_data
    FOREIGN KEY (budget_data_id) REFERENCES budget_data(id);

-- 支付记录外键
ALTER TABLE payments ADD CONSTRAINT fk_payments_application
    FOREIGN KEY (application_id) REFERENCES expenditure_applications(id);
ALTER TABLE payments ADD CONSTRAINT fk_payments_payer
    FOREIGN KEY (payer_id) REFERENCES users(id);

-- 发票外键
ALTER TABLE invoices ADD CONSTRAINT fk_invoices_application
    FOREIGN KEY (application_id) REFERENCES expenditure_applications(id);

-- 工作流实例外键
ALTER TABLE workflow_instances ADD CONSTRAINT fk_workflow_instances_definition
    FOREIGN KEY (definition_id) REFERENCES workflow_definitions(id);
ALTER TABLE workflow_instances ADD CONSTRAINT fk_workflow_instances_initiator
    FOREIGN KEY (initiator_id) REFERENCES users(id);

-- 工作流任务外键
ALTER TABLE workflow_tasks ADD CONSTRAINT fk_workflow_tasks_instance
    FOREIGN KEY (instance_id) REFERENCES workflow_instances(instance_id);
ALTER TABLE workflow_tasks ADD CONSTRAINT fk_workflow_tasks_assignee
    FOREIGN KEY (assignee_id) REFERENCES users(id);

-- 通知接收记录外键
ALTER TABLE notification_recipients ADD CONSTRAINT fk_notification_recipients_notification
    FOREIGN KEY (notification_id) REFERENCES notifications(id) ON DELETE CASCADE;
ALTER TABLE notification_recipients ADD CONSTRAINT fk_notification_recipients_recipient
    FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE;

-- 审计日志外键
ALTER TABLE audit_logs ADD CONSTRAINT fk_audit_logs_user
    FOREIGN KEY (user_id) REFERENCES users(id);

-- ============================================================================
-- 4. 创建索引
-- ============================================================================

-- 基础表索引
CREATE INDEX idx_organizations_deleted_at ON organizations(deleted_at);
CREATE INDEX idx_organizations_parent_id ON organizations(parent_id);
CREATE INDEX idx_organizations_code ON organizations(code);
CREATE INDEX idx_organizations_type ON organizations(type);

CREATE INDEX idx_users_deleted_at ON users(deleted_at);
CREATE INDEX idx_users_organization_id ON users(organization_id);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);

CREATE INDEX idx_permissions_deleted_at ON permissions(deleted_at);
CREATE INDEX idx_permissions_parent_id ON permissions(parent_id);
CREATE INDEX idx_permissions_code ON permissions(code);
CREATE INDEX idx_permissions_type ON permissions(type);

CREATE INDEX idx_budget_items_deleted_at ON budget_items(deleted_at);
CREATE INDEX idx_budget_items_parent_id ON budget_items(parent_id);
CREATE INDEX idx_budget_items_code ON budget_items(code);
CREATE INDEX idx_budget_items_type ON budget_items(type);

CREATE INDEX idx_notifications_deleted_at ON notifications(deleted_at);
CREATE INDEX idx_notifications_sender_id ON notifications(sender_id);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_status ON notifications(status);

-- 关联表索引
CREATE INDEX idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX idx_user_roles_role_id ON user_roles(role_id);
CREATE INDEX idx_role_permissions_role_id ON role_permissions(role_id);
CREATE INDEX idx_role_permissions_permission_id ON role_permissions(permission_id);

-- 业务表索引
CREATE INDEX idx_budget_data_scheme_item_org ON budget_data(scheme_id, budget_item_id, organization_id);
CREATE INDEX idx_expenditure_app_status_applicant ON expenditure_applications(status, applicant_id);
CREATE INDEX idx_expenditure_app_department ON expenditure_applications(department_id);
CREATE INDEX idx_workflow_task_status_assignee ON workflow_tasks(status, assignee_id);
CREATE INDEX idx_notification_recipients_recipient_read ON notification_recipients(recipient_id, is_read);

-- ============================================================================
-- 5. 插入初始数据
-- ============================================================================

-- 插入组织架构数据
INSERT INTO organizations (name, code, type, level, sort, description, status) VALUES
('云南省第二人民医院', 'HOSPITAL_ROOT', 'hospital', 1, 1, '医院根节点', 'active');

INSERT INTO organizations (name, code, type, parent_id, level, sort, status) VALUES
('内科', 'DEPT_INTERNAL', 'department', 1, 2, 1, 'active'),
('外科', 'DEPT_SURGERY', 'department', 1, 2, 2, 'active'),
('财务科', 'DEPT_FINANCE', 'department', 1, 2, 3, 'active'),
('人事科', 'DEPT_HR', 'department', 1, 2, 4, 'active'),
('信息科', 'DEPT_IT', 'department', 1, 2, 5, 'active'),
('药剂科', 'DEPT_PHARMACY', 'department', 1, 2, 6, 'active'),
('检验科', 'DEPT_LAB', 'department', 1, 2, 7, 'active'),
('放射科', 'DEPT_RADIOLOGY', 'department', 1, 2, 8, 'active');

-- 插入角色数据
INSERT INTO roles (name, code, description, status) VALUES
('系统管理员', 'ADMIN', '系统管理员，拥有所有权限', 'active'),
('财务管理员', 'FINANCE_ADMIN', '财务管理员', 'active'),
('科室主任', 'DEPT_MANAGER', '科室主任', 'active'),
('财务人员', 'FINANCE_USER', '财务人员', 'active'),
('普通用户', 'USER', '普通用户', 'active');

-- 插入权限数据
INSERT INTO permissions (name, code, type, path, icon, sort, status) VALUES
-- 系统管理
('系统管理', 'system', 'menu', '/system', 'SettingOutlined', 1, 'active'),
('用户管理', 'system:user', 'menu', '/system/users', 'TeamOutlined', 11, 'active'),
('角色管理', 'system:role', 'menu', '/system/roles', 'CrownOutlined', 12, 'active'),
('权限管理', 'system:permission', 'menu', '/system/permissions', 'KeyOutlined', 13, 'active'),
('组织管理', 'system:org', 'menu', '/system/organizations', 'ApartmentOutlined', 14, 'active'),

-- 预算管理
('预算管理', 'budget', 'menu', '/budget', 'FundOutlined', 2, 'active'),
('预算编制', 'budget:plan', 'menu', '/budget/plan', 'EditOutlined', 21, 'active'),
('预算分析', 'budget:analysis', 'menu', '/budget/analysis', 'BarChartOutlined', 22, 'active'),
('预算监控', 'budget:monitor', 'menu', '/budget/monitor', 'EyeOutlined', 23, 'active'),

-- 支出控制
('支出控制', 'expenditure', 'menu', '/expenditure', 'AccountBookOutlined', 3, 'active'),
('报销申请', 'expenditure:apply', 'menu', '/expenditure/apply', 'PlusCircleOutlined', 31, 'active'),
('报销管理', 'expenditure:manage', 'menu', '/expenditure/manage', 'UnorderedListOutlined', 32, 'active'),
('报销审批', 'expenditure:approve', 'menu', '/expenditure/approve', 'CheckCircleOutlined', 33, 'active'),

-- 合同管理
('合同管理', 'contract', 'menu', '/contract', 'FileTextOutlined', 4, 'active'),
('合同列表', 'contract:list', 'menu', '/contract/list', 'FolderOutlined', 41, 'active'),

-- 财务管理
('财务管理', 'finance', 'menu', '/finance', 'CalculatorOutlined', 5, 'active'),
('凭证管理', 'finance:voucher', 'menu', '/finance/voucher', 'FileAddOutlined', 51, 'active'),
('财务报表', 'finance:report', 'menu', '/finance/report', 'FileTextOutlined', 52, 'active');

-- 设置权限层级关系
UPDATE permissions SET parent_id = (SELECT id FROM permissions WHERE code = 'system')
WHERE code IN ('system:user', 'system:role', 'system:permission', 'system:org');

UPDATE permissions SET parent_id = (SELECT id FROM permissions WHERE code = 'budget')
WHERE code IN ('budget:plan', 'budget:analysis', 'budget:monitor');

UPDATE permissions SET parent_id = (SELECT id FROM permissions WHERE code = 'expenditure')
WHERE code IN ('expenditure:apply', 'expenditure:manage', 'expenditure:approve');

UPDATE permissions SET parent_id = (SELECT id FROM permissions WHERE code = 'contract')
WHERE code IN ('contract:list');

UPDATE permissions SET parent_id = (SELECT id FROM permissions WHERE code = 'finance')
WHERE code IN ('finance:voucher', 'finance:report');

-- 插入岗位数据
INSERT INTO positions (name, code, organization_id, level, description, status) VALUES
('科室主任', 'DEPT_DIRECTOR_INTERNAL', 2, 1, '内科主任', 'active'),
('科室主任', 'DEPT_DIRECTOR_SURGERY', 3, 1, '外科主任', 'active'),
('财务科长', 'FINANCE_DIRECTOR', 4, 1, '财务科长', 'active'),
('财务会计', 'FINANCE_ACCOUNTANT', 4, 2, '财务会计', 'active'),
('出纳', 'FINANCE_CASHIER', 4, 2, '出纳', 'active'),
('人事科长', 'HR_DIRECTOR', 5, 1, '人事科长', 'active'),
('信息科长', 'IT_DIRECTOR', 6, 1, '信息科长', 'active');

-- 插入成本中心数据
INSERT INTO cost_centers (name, code, organization_id, type, description, status) VALUES
('内科成本中心', 'CC_INTERNAL', 2, 'cost', '内科成本中心', 'active'),
('外科成本中心', 'CC_SURGERY', 3, 'cost', '外科成本中心', 'active'),
('财务成本中心', 'CC_FINANCE', 4, 'cost', '财务成本中心', 'active'),
('人事成本中心', 'CC_HR', 5, 'cost', '人事成本中心', 'active'),
('信息成本中心', 'CC_IT', 6, 'cost', '信息成本中心', 'active');

-- 插入预算科目数据
INSERT INTO budget_items (name, code, type, level, sort, status, is_controlled) VALUES
('收入', 'INCOME', 'income', 1, 1, 'active', false),
('支出', 'EXPENSE', 'expense', 1, 2, 'active', true);

-- 插入二级科目
INSERT INTO budget_items (name, code, parent_id, type, level, sort, status, is_controlled) VALUES
('医疗收入', 'MEDICAL_INCOME', 1, 'income', 2, 1, 'active', false),
('药品收入', 'DRUG_INCOME', 1, 'income', 2, 2, 'active', false),
('人员经费', 'PERSONNEL_EXPENSE', 2, 'expense', 2, 1, 'active', true),
('公用经费', 'PUBLIC_EXPENSE', 2, 'expense', 2, 2, 'active', true),
('设备购置', 'EQUIPMENT_EXPENSE', 2, 'expense', 2, 3, 'active', true),
('药品费用', 'DRUG_EXPENSE', 2, 'expense', 2, 4, 'active', true);

-- 插入三级科目
INSERT INTO budget_items (name, code, parent_id, type, level, sort, status, is_controlled) VALUES
('门诊收入', 'OUTPATIENT_INCOME', 3, 'income', 3, 1, 'active', false),
('住院收入', 'INPATIENT_INCOME', 3, 'income', 3, 2, 'active', false),
('基本工资', 'BASIC_SALARY', 5, 'expense', 3, 1, 'active', true),
('绩效工资', 'PERFORMANCE_SALARY', 5, 'expense', 3, 2, 'active', true),
('社会保险', 'SOCIAL_INSURANCE', 5, 'expense', 3, 3, 'active', true),
('办公费', 'OFFICE_EXPENSE', 6, 'expense', 3, 1, 'active', true),
('水电费', 'UTILITY_EXPENSE', 6, 'expense', 3, 2, 'active', true),
('维修费', 'MAINTENANCE_EXPENSE', 6, 'expense', 3, 3, 'active', true),
('医疗设备', 'MEDICAL_EQUIPMENT', 7, 'expense', 3, 1, 'active', true),
('办公设备', 'OFFICE_EQUIPMENT', 7, 'expense', 3, 2, 'active', true);

-- 插入系统配置数据
INSERT INTO system_configs (config_key, config_value, config_type, category, description, is_system, status) VALUES
('system.name', '医院运营管理系统', 'string', 'system', '系统名称', true, 'active'),
('system.version', '1.0.0', 'string', 'system', '系统版本', true, 'active'),
('system.logo', '/assets/logo.png', 'string', 'system', '系统Logo', false, 'active'),
('budget.control_type', '1', 'number', 'budget', '预算控制类型：1刚性，2柔性', false, 'active'),
('budget.warning_threshold', '0.8', 'number', 'budget', '预算预警阈值', false, 'active'),
('expense.auto_approval_limit', '1000', 'number', 'expense', '自动审批金额上限', false, 'active'),
('expense.require_invoice', 'true', 'boolean', 'expense', '是否必须上传发票', false, 'active'),
('workflow.timeout_hours', '72', 'number', 'workflow', '工作流超时小时数', false, 'active');

-- 插入费用标准数据
INSERT INTO expense_standards (name, category, level, amount, unit, description, effective_date, status) VALUES
('差旅费标准-省内', 'travel', '一般人员', 200.00, '天', '省内差旅费标准', '2025-01-01', 'active'),
('差旅费标准-省内', 'travel', '处级干部', 300.00, '天', '省内差旅费标准', '2025-01-01', 'active'),
('差旅费标准-省外', 'travel', '一般人员', 350.00, '天', '省外差旅费标准', '2025-01-01', 'active'),
('差旅费标准-省外', 'travel', '处级干部', 450.00, '天', '省外差旅费标准', '2025-01-01', 'active'),
('培训费标准', 'training', '一般人员', 500.00, '天', '培训费标准', '2025-01-01', 'active'),
('会议费标准', 'meeting', '一般会议', 150.00, '人天', '会议费标准', '2025-01-01', 'active'),
('招待费标准', 'entertainment', '一般接待', 100.00, '人次', '招待费标准', '2025-01-01', 'active');

-- 插入用户数据（密码为 password 的bcrypt哈希）
INSERT INTO users (username, password, email, real_name, status, organization_id) VALUES
('admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', '系统管理员', 'active', 1),
('finance_admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', '财务管理员', 'active', 4),
('dept_manager1', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', '内科主任', 'active', 2),
('dept_manager2', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', '外科主任', 'active', 3),
('finance_user', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', '财务会计', 'active', 4);

-- 分配用户角色
INSERT INTO user_roles (user_id, role_id) VALUES
(1, 1), -- admin -> 系统管理员
(2, 2), -- finance_admin -> 财务管理员
(3, 3), -- dept_manager1 -> 科室主任
(4, 3), -- dept_manager2 -> 科室主任
(5, 4); -- finance_user -> 财务人员

-- 分配角色权限
-- 系统管理员拥有所有权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT 1, id FROM permissions;

-- 财务管理员权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT 2, id FROM permissions WHERE code IN (
    'budget', 'budget:plan', 'budget:analysis', 'budget:monitor',
    'expenditure', 'expenditure:apply', 'expenditure:manage', 'expenditure:approve',
    'finance', 'finance:voucher', 'finance:report'
);

-- 科室主任权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT 3, id FROM permissions WHERE code IN (
    'budget', 'budget:plan', 'budget:analysis',
    'expenditure', 'expenditure:apply', 'expenditure:manage'
);

-- 财务人员权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT 4, id FROM permissions WHERE code IN (
    'budget', 'budget:analysis',
    'expenditure', 'expenditure:manage', 'expenditure:approve',
    'finance', 'finance:voucher', 'finance:report'
);

-- 普通用户权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT 5, id FROM permissions WHERE code IN (
    'expenditure', 'expenditure:apply'
);

-- 分配用户岗位
INSERT INTO user_positions (user_id, position_id) VALUES
(3, 1), -- 内科主任
(4, 2), -- 外科主任
(2, 3), -- 财务科长
(5, 4); -- 财务会计

-- 创建示例预算方案
INSERT INTO budget_schemes (name, year, organization_id, status, description, creator_id) VALUES
('2025年度预算方案', 2025, 1, 'approved', '2025年度医院整体预算方案', 1),
('内科2025年预算', 2025, 2, 'approved', '内科2025年度预算', 3),
('外科2025年预算', 2025, 3, 'approved', '外科2025年度预算', 4);

-- 插入示例预算数据
INSERT INTO budget_data (scheme_id, budget_item_id, organization_id, amount, remark) VALUES
-- 医院整体预算
(1, 5, 1, 5000000.00, '人员经费总预算'),
(1, 6, 1, 2000000.00, '公用经费总预算'),
(1, 7, 1, 1000000.00, '设备购置总预算'),
-- 内科预算
(2, 5, 2, 1000000.00, '内科人员经费'),
(2, 6, 2, 300000.00, '内科公用经费'),
(2, 7, 2, 200000.00, '内科设备购置'),
-- 外科预算
(3, 5, 3, 1200000.00, '外科人员经费'),
(3, 6, 3, 400000.00, '外科公用经费'),
(3, 7, 3, 300000.00, '外科设备购置');

-- 插入示例通知
INSERT INTO notifications (title, content, type, priority, status, sender_id, publish_time) VALUES
('系统上线通知', '医院运营管理系统正式上线，请各科室及时登录使用。', 'system', 2, 'active', 1, NOW()),
('预算编制提醒', '2025年度预算编制工作即将开始，请各科室做好准备。', 'reminder', 2, 'active', 2, NOW()),
('系统维护通知', '系统将于本周六晚进行维护，维护期间暂停服务。', 'system', 3, 'active', 1, NOW());

-- 插入通知接收记录
INSERT INTO notification_recipients (notification_id, recipient_id, is_read) VALUES
(1, 2, false), (1, 3, false), (1, 4, false), (1, 5, false),
(2, 2, false), (2, 3, false), (2, 4, false),
(3, 1, true), (3, 2, false), (3, 3, false), (3, 4, false), (3, 5, false);

-- 插入工作流定义数据
INSERT INTO workflow_definitions (name, code, category, version, definition, description, is_active, status) VALUES
('预算申请审批流程', 'BUDGET_APPROVAL', 'budget', 1, '{"nodes":[{"id":"start","type":"start","name":"开始"},{"id":"apply","type":"task","name":"申请提交"},{"id":"dept_review","type":"task","name":"科室审核"},{"id":"finance_review","type":"task","name":"财务审核"},{"id":"end","type":"end","name":"结束"}],"edges":[{"source":"start","target":"apply"},{"source":"apply","target":"dept_review"},{"source":"dept_review","target":"finance_review"},{"source":"finance_review","target":"end"}]}', '预算申请审批工作流', true, 'active'),
('支出申请审批流程', 'EXPENSE_APPROVAL', 'expense', 1, '{"nodes":[{"id":"start","type":"start","name":"开始"},{"id":"apply","type":"task","name":"申请提交"},{"id":"dept_review","type":"task","name":"科室审核"},{"id":"finance_review","type":"task","name":"财务审核"},{"id":"director_review","type":"task","name":"院长审核"},{"id":"end","type":"end","name":"结束"}],"edges":[{"source":"start","target":"apply"},{"source":"apply","target":"dept_review"},{"source":"dept_review","target":"finance_review"},{"source":"finance_review","target":"director_review"},{"source":"director_review","target":"end"}]}', '支出申请审批工作流', true, 'active'),
('合同审批流程', 'CONTRACT_APPROVAL', 'contract', 1, '{"nodes":[{"id":"start","type":"start","name":"开始"},{"id":"apply","type":"task","name":"合同提交"},{"id":"legal_review","type":"task","name":"法务审核"},{"id":"finance_review","type":"task","name":"财务审核"},{"id":"director_review","type":"task","name":"院长审核"},{"id":"end","type":"end","name":"结束"}],"edges":[{"source":"start","target":"apply"},{"source":"apply","target":"legal_review"},{"source":"legal_review","target":"finance_review"},{"source":"finance_review","target":"director_review"},{"source":"director_review","target":"end"}]}', '合同审批工作流', true, 'active');

-- 插入工作流实例数据
INSERT INTO workflow_instances (instance_id, definition_id, business_type, business_id, status, current_node, initiator_id, started_at) VALUES
('WF_INST_001', 1, 'budget_scheme', '1', 'running', 'dept_review', 3, NOW() - INTERVAL '2 days'),
('WF_INST_002', 2, 'expenditure_application', '1', 'running', 'finance_review', 4, NOW() - INTERVAL '1 day'),
('WF_INST_003', 1, 'budget_scheme', '2', 'completed', 'end', 3, NOW() - INTERVAL '5 days'),
('WF_INST_004', 2, 'expenditure_application', '2', 'running', 'dept_review', 5, NOW() - INTERVAL '3 hours'),
('WF_INST_005', 3, 'contract', '1', 'running', 'legal_review', 2, NOW() - INTERVAL '6 hours');

-- 插入工作流任务数据
INSERT INTO workflow_tasks (task_id, instance_id, node_id, task_name, task_type, status, assignee_id, assigned_at, comment, form_data) VALUES
('TASK_001', 'WF_INST_001', 'dept_review', '科室审核预算申请', 'approval', 'pending', 3, NOW() - INTERVAL '2 days', '', '{"amount": 100000, "description": "内科设备采购预算"}'),
('TASK_002', 'WF_INST_002', 'finance_review', '财务审核支出申请', 'approval', 'pending', 2, NOW() - INTERVAL '1 day', '', '{"amount": 50000, "description": "办公用品采购"}'),
('TASK_003', 'WF_INST_003', 'end', '预算申请已完成', 'completed', 'completed', 2, NOW() - INTERVAL '5 days', '审批通过', '{"amount": 80000, "description": "外科医疗设备"}'),
('TASK_004', 'WF_INST_004', 'dept_review', '科室审核支出申请', 'approval', 'pending', 4, NOW() - INTERVAL '3 hours', '', '{"amount": 20000, "description": "培训费用"}'),
('TASK_005', 'WF_INST_005', 'legal_review', '法务审核合同', 'approval', 'pending', 1, NOW() - INTERVAL '6 hours', '', '{"contract_amount": 500000, "vendor": "医疗设备供应商"}'),
('TASK_006', 'WF_INST_001', 'apply', '预算申请提交', 'submit', 'completed', 3, NOW() - INTERVAL '2 days', '已提交申请', '{"amount": 100000, "description": "内科设备采购预算"}'),
('TASK_007', 'WF_INST_002', 'apply', '支出申请提交', 'submit', 'completed', 4, NOW() - INTERVAL '1 day', '已提交申请', '{"amount": 50000, "description": "办公用品采购"}'),
('TASK_008', 'WF_INST_002', 'dept_review', '科室审核支出申请', 'approval', 'completed', 4, NOW() - INTERVAL '1 day', '科室审核通过', '{"amount": 50000, "description": "办公用品采购"}');

-- ============================================================================
-- 完成
-- ============================================================================
-- 数据库初始化完成！
-- 默认管理员账号：admin / password
-- 系统已包含基础的组织架构、角色权限、预算科目等数据
