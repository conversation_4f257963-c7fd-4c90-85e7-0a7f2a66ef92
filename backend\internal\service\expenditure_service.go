package service

import (
	"errors"
	"fmt"
	"quality_control/backend/internal/models"
	"time"

	"gorm.io/gorm"
)

type ExpenditureService struct {
	db *gorm.DB
}

func NewExpenditureService(db *gorm.DB) *ExpenditureService {
	return &ExpenditureService{db: db}
}

// CreateExpenditureApplicationRequest 创建支出申请请求
type CreateExpenditureApplicationRequest struct {
	Title       string                    `json:"title" binding:"required"`
	Type        models.ApplicationType    `json:"type" binding:"required"`
	TotalAmount float64                   `json:"total_amount" binding:"required,gt=0"`
	Details     []ExpenditureDetailRequest `json:"details" binding:"required,dive"`
}

// ExpenditureDetailRequest 支出明细请求
type ExpenditureDetailRequest struct {
	ExpenseType string    `json:"expense_type" binding:"required"`
	ExpenseDate time.Time `json:"expense_date" binding:"required"`
	InvoiceNo   string    `json:"invoice_no"`
	InvoiceCode string    `json:"invoice_code"`
	Amount      float64   `json:"amount" binding:"required,gt=0"`
	TaxAmount   float64   `json:"tax_amount"`
	Description string    `json:"description"`
}

// UpdateExpenditureApplicationRequest 更新支出申请请求
type UpdateExpenditureApplicationRequest struct {
	Title       string                    `json:"title"`
	TotalAmount float64                   `json:"total_amount"`
	Details     []ExpenditureDetailRequest `json:"details"`
}

// ExpenditureApplicationListRequest 支出申请列表请求
type ExpenditureApplicationListRequest struct {
	models.PageRequest
	Title         string                 `form:"title"`
	Type          models.ApplicationType `form:"type"`
	Status        models.ApprovalStatus  `form:"status"`
	PaymentStatus models.PaymentStatus   `form:"payment_status"`
	ApplicantID   uint                   `form:"applicant_id"`
	DepartmentID  uint                   `form:"department_id"`
	StartDate     string                 `form:"start_date"`
	EndDate       string                 `form:"end_date"`
}

// CreateExpenditureApplication 创建支出申请
func (s *ExpenditureService) CreateExpenditureApplication(req *CreateExpenditureApplicationRequest, applicantID, departmentID uint) (*models.ExpenditureApplication, error) {
	// 生成申请编号
	applicationNo := s.generateApplicationNo()

	// 验证明细金额总和
	var detailsTotal float64
	for _, detail := range req.Details {
		detailsTotal += detail.Amount
	}
	if detailsTotal != req.TotalAmount {
		return nil, errors.New("明细金额总和与申请总金额不符")
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建申请
	application := &models.ExpenditureApplication{
		ApplicationNo: applicationNo,
		Title:         req.Title,
		Type:          req.Type,
		TotalAmount:   req.TotalAmount,
		Status:        models.ApprovalStatusDraft,
		PaymentStatus: models.PaymentStatusUnpaid,
		ApplicantID:   applicantID,
		DepartmentID:  departmentID,
	}

	if err := tx.Create(application).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// 创建明细
	for _, detailReq := range req.Details {
		detail := &models.ExpenditureDetail{
			ApplicationID: application.ID,
			ExpenseType:   detailReq.ExpenseType,
			ExpenseDate:   detailReq.ExpenseDate,
			InvoiceNo:     detailReq.InvoiceNo,
			InvoiceCode:   detailReq.InvoiceCode,
			Amount:        detailReq.Amount,
			TaxAmount:     detailReq.TaxAmount,
			Description:   detailReq.Description,
		}

		if err := tx.Create(detail).Error; err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	return s.GetExpenditureApplication(application.ID)
}

// generateApplicationNo 生成申请编号
func (s *ExpenditureService) generateApplicationNo() string {
	now := time.Now()
	prefix := fmt.Sprintf("EXP%s", now.Format("20060102"))
	
	// 查询当天的最大序号
	var count int64
	s.db.Model(&models.ExpenditureApplication{}).
		Where("application_no LIKE ?", prefix+"%").
		Count(&count)
	
	return fmt.Sprintf("%s%04d", prefix, count+1)
}

// GetExpenditureApplications 获取支出申请列表
func (s *ExpenditureService) GetExpenditureApplications(req *ExpenditureApplicationListRequest) (*UserListResponse, error) {
	req.SetDefaults()
	
	query := s.db.Model(&models.ExpenditureApplication{})
	
	if req.Title != "" {
		query = query.Where("title LIKE ?", "%"+req.Title+"%")
	}
	if req.Type > 0 {
		query = query.Where("type = ?", req.Type)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.PaymentStatus != "" {
		query = query.Where("payment_status = ?", req.PaymentStatus)
	}
	if req.ApplicantID > 0 {
		query = query.Where("applicant_id = ?", req.ApplicantID)
	}
	if req.DepartmentID > 0 {
		query = query.Where("department_id = ?", req.DepartmentID)
	}
	if req.StartDate != "" {
		query = query.Where("created_at >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		query = query.Where("created_at <= ?", req.EndDate)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	var applications []models.ExpenditureApplication
	if err := query.Preload("Applicant").Preload("Department").
		Offset(req.GetOffset()).Limit(req.PageSize).
		Order("created_at DESC").
		Find(&applications).Error; err != nil {
		return nil, err
	}

	// 转换为通用响应格式
	var users []models.User
	return &UserListResponse{
		List: users,
		PageInfo: models.PageInfo{
			Page:     req.Page,
			PageSize: req.PageSize,
			Total:    total,
		},
	}, nil
}

// GetExpenditureApplication 获取支出申请详情
func (s *ExpenditureService) GetExpenditureApplication(id uint) (*models.ExpenditureApplication, error) {
	var application models.ExpenditureApplication
	if err := s.db.Preload("Applicant").Preload("Department").
		Preload("Details").Preload("Attachments").Preload("BudgetLinks").
		Where("id = ?", id).First(&application).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("支出申请不存在")
		}
		return nil, err
	}

	return &application, nil
}

// UpdateExpenditureApplication 更新支出申请
func (s *ExpenditureService) UpdateExpenditureApplication(id uint, req *UpdateExpenditureApplicationRequest) (*models.ExpenditureApplication, error) {
	var application models.ExpenditureApplication
	if err := s.db.Where("id = ?", id).First(&application).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("支出申请不存在")
		}
		return nil, err
	}

	// 只有草稿状态才能修改
	if application.Status != models.ApprovalStatusDraft {
		return nil, errors.New("只有草稿状态的申请才能修改")
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新申请信息
	updates := map[string]interface{}{}
	if req.Title != "" {
		updates["title"] = req.Title
	}
	if req.TotalAmount > 0 {
		updates["total_amount"] = req.TotalAmount
	}

	if len(updates) > 0 {
		if err := tx.Model(&application).Updates(updates).Error; err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	// 更新明细
	if req.Details != nil {
		// 删除原有明细
		if err := tx.Where("application_id = ?", id).Delete(&models.ExpenditureDetail{}).Error; err != nil {
			tx.Rollback()
			return nil, err
		}

		// 创建新明细
		for _, detailReq := range req.Details {
			detail := &models.ExpenditureDetail{
				ApplicationID: id,
				ExpenseType:   detailReq.ExpenseType,
				ExpenseDate:   detailReq.ExpenseDate,
				InvoiceNo:     detailReq.InvoiceNo,
				InvoiceCode:   detailReq.InvoiceCode,
				Amount:        detailReq.Amount,
				TaxAmount:     detailReq.TaxAmount,
				Description:   detailReq.Description,
			}

			if err := tx.Create(detail).Error; err != nil {
				tx.Rollback()
				return nil, err
			}
		}
	}

	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	return s.GetExpenditureApplication(id)
}

// DeleteExpenditureApplication 删除支出申请
func (s *ExpenditureService) DeleteExpenditureApplication(id uint) error {
	var application models.ExpenditureApplication
	if err := s.db.Where("id = ?", id).First(&application).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("支出申请不存在")
		}
		return err
	}

	// 只有草稿状态才能删除
	if application.Status != models.ApprovalStatusDraft {
		return errors.New("只有草稿状态的申请才能删除")
	}

	return s.db.Delete(&application).Error
}

// SubmitExpenditureApplication 提交支出申请
func (s *ExpenditureService) SubmitExpenditureApplication(id uint) error {
	var application models.ExpenditureApplication
	if err := s.db.Where("id = ?", id).First(&application).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("支出申请不存在")
		}
		return err
	}

	if application.Status != models.ApprovalStatusDraft {
		return errors.New("只有草稿状态的申请才能提交")
	}

	// 检查是否有明细
	var count int64
	if err := s.db.Model(&models.ExpenditureDetail{}).Where("application_id = ?", id).Count(&count).Error; err != nil {
		return err
	}
	if count == 0 {
		return errors.New("请先添加支出明细")
	}

	return s.db.Model(&application).Update("status", models.ApprovalStatusPending).Error
}

// ApproveExpenditureApplication 审批支出申请
func (s *ExpenditureService) ApproveExpenditureApplication(id uint, approved bool, comment string) error {
	var application models.ExpenditureApplication
	if err := s.db.Where("id = ?", id).First(&application).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("支出申请不存在")
		}
		return err
	}

	if application.Status != models.ApprovalStatusPending {
		return errors.New("只有待审批状态的申请才能审批")
	}

	var newStatus models.ApprovalStatus
	if approved {
		newStatus = models.ApprovalStatusApproved
	} else {
		newStatus = models.ApprovalStatusRejected
	}

	return s.db.Model(&application).Update("status", newStatus).Error
}

// CreatePaymentRequest 创建支付请求
type CreatePaymentRequest struct {
	ApplicationID uint    `json:"application_id" binding:"required"`
	Amount        float64 `json:"amount" binding:"required,gt=0"`
	PaymentMethod string  `json:"payment_method" binding:"required"`
	Remark        string  `json:"remark"`
}

// CreatePayment 创建支付记录
func (s *ExpenditureService) CreatePayment(req *CreatePaymentRequest, payerID uint) (*models.Payment, error) {
	// 检查申请是否存在且已审批
	var application models.ExpenditureApplication
	if err := s.db.Where("id = ? AND status = ?", req.ApplicationID, models.ApprovalStatusApproved).
		First(&application).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("申请不存在或未审批通过")
		}
		return nil, err
	}

	// 检查支付金额是否超过申请金额
	var paidAmount float64
	s.db.Model(&models.Payment{}).
		Where("application_id = ? AND status = ?", req.ApplicationID, models.PaymentStatusPaid).
		Select("COALESCE(SUM(amount), 0)").Scan(&paidAmount)

	if paidAmount+req.Amount > application.TotalAmount {
		return nil, errors.New("支付金额超过申请金额")
	}

	// 生成支付编号
	paymentNo := s.generatePaymentNo()
	now := time.Now()

	payment := &models.Payment{
		PaymentNo:     paymentNo,
		ApplicationID: req.ApplicationID,
		Amount:        req.Amount,
		PaymentMethod: req.PaymentMethod,
		PaymentDate:   &now,
		Status:        models.PaymentStatusPaid,
		Remark:        req.Remark,
		PayerID:       &payerID,
	}

	if err := s.db.Create(payment).Error; err != nil {
		return nil, err
	}

	// 更新申请的支付状态
	newPaidAmount := paidAmount + req.Amount
	var paymentStatus models.PaymentStatus
	if newPaidAmount >= application.TotalAmount {
		paymentStatus = models.PaymentStatusPaid
	} else {
		paymentStatus = models.PaymentStatusPartly
	}

	s.db.Model(&application).Update("payment_status", paymentStatus)

	return payment, nil
}

// generatePaymentNo 生成支付编号
func (s *ExpenditureService) generatePaymentNo() string {
	now := time.Now()
	prefix := fmt.Sprintf("PAY%s", now.Format("20060102"))
	
	var count int64
	s.db.Model(&models.Payment{}).
		Where("payment_no LIKE ?", prefix+"%").
		Count(&count)
	
	return fmt.Sprintf("%s%04d", prefix, count+1)
}
