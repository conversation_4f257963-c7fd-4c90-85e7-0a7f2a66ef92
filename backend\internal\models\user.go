package models

import (
	"time"
)

// User 用户模型
type User struct {
	BaseModel
	Username     string    `gorm:"uniqueIndex;size:50;not null" json:"username"`
	Password     string    `gorm:"size:255;not null" json:"-"`
	Email        string    `gorm:"uniqueIndex;size:100" json:"email"`
	Phone        string    `gorm:"size:20" json:"phone"`
	RealName     string    `gorm:"size:50;not null" json:"real_name"`
	Avatar       string    `gorm:"size:255" json:"avatar"`
	Status       Status    `gorm:"size:20;default:'active'" json:"status"`
	LastLoginAt  *time.Time `json:"last_login_at"`
	LastLoginIP  string    `gorm:"size:45" json:"last_login_ip"`
	
	// 关联字段
	OrganizationID uint         `gorm:"index" json:"organization_id"`
	Organization   Organization `gorm:"foreignKey:OrganizationID" json:"organization,omitempty"`

	// 多对多关联
	Roles     []Role     `gorm:"many2many:user_roles;" json:"roles,omitempty"`
	Positions []Position `gorm:"many2many:user_positions;" json:"positions,omitempty"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// Role 角色模型
type Role struct {
	BaseModel
	Name        string `gorm:"uniqueIndex;size:50;not null" json:"name"`
	Code        string `gorm:"uniqueIndex;size:50;not null" json:"code"`
	Description string `gorm:"size:255" json:"description"`
	Status      Status `gorm:"size:20;default:'active'" json:"status"`
	
	// 多对多关联
	Users       []User       `gorm:"-" json:"users,omitempty"`
	Permissions []Permission `gorm:"-" json:"permissions,omitempty"`
}

// TableName 指定表名
func (Role) TableName() string {
	return "roles"
}

// Permission 权限模型
type Permission struct {
	BaseModel
	Name        string `gorm:"size:100;not null" json:"name"`
	Code        string `gorm:"uniqueIndex;size:100;not null" json:"code"`
	Type        string `gorm:"size:20;not null" json:"type"` // menu, button, api
	ParentID    *uint  `gorm:"index" json:"parent_id"`
	Path        string `gorm:"size:255" json:"path"`
	Method      string `gorm:"size:10" json:"method"`
	Icon        string `gorm:"size:50" json:"icon"`
	Sort        int    `gorm:"default:0" json:"sort"`
	Status      Status `gorm:"size:20;default:'active'" json:"status"`
	
	// 自关联
	Parent   *Permission  `gorm:"-" json:"parent,omitempty"`
	Children []Permission `gorm:"-" json:"children,omitempty"`

	// 多对多关联
	Roles []Role `gorm:"-" json:"roles,omitempty"`
}

// TableName 指定表名
func (Permission) TableName() string {
	return "permissions"
}

// Position 岗位模型
type Position struct {
	BaseModel
	Name           string `gorm:"size:50;not null" json:"name"`
	Code           string `gorm:"uniqueIndex;size:50;not null" json:"code"`
	Description    string `gorm:"size:255" json:"description"`
	Level          int    `gorm:"default:1" json:"level"`
	Status         Status `gorm:"size:20;default:'active'" json:"status"`
	OrganizationID uint   `gorm:"index" json:"organization_id"`

	// 关联字段
	Organization Organization `gorm:"-" json:"organization,omitempty"`
	Users        []User       `gorm:"-" json:"users,omitempty"`
}

// TableName 指定表名
func (Position) TableName() string {
	return "positions"
}

// UserRole 用户角色关联表
type UserRole struct {
	UserID uint `gorm:"primaryKey" json:"user_id"`
	RoleID uint `gorm:"primaryKey" json:"role_id"`
	
	User User `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Role Role `gorm:"foreignKey:RoleID" json:"role,omitempty"`
}

// TableName 指定表名
func (UserRole) TableName() string {
	return "user_roles"
}

// RolePermission 角色权限关联表
type RolePermission struct {
	RoleID       uint `gorm:"primaryKey" json:"role_id"`
	PermissionID uint `gorm:"primaryKey" json:"permission_id"`
	
	Role       Role       `gorm:"foreignKey:RoleID" json:"role,omitempty"`
	Permission Permission `gorm:"foreignKey:PermissionID" json:"permission,omitempty"`
}

// TableName 指定表名
func (RolePermission) TableName() string {
	return "role_permissions"
}

// UserPosition 用户岗位关联表
type UserPosition struct {
	UserID     uint `gorm:"primaryKey" json:"user_id"`
	PositionID uint `gorm:"primaryKey" json:"position_id"`
	
	User     User     `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Position Position `gorm:"foreignKey:PositionID" json:"position,omitempty"`
}

// TableName 指定表名
func (UserPosition) TableName() string {
	return "user_positions"
}
