<template>
  <div class="permission-manage">
    <a-card :bordered="false">
      <template #title>
        <div class="page-header">
          <h3>权限管理</h3>
          <div class="header-actions">
            <a-button type="primary" @click="showCreateModal">
              <template #icon><PlusOutlined /></template>
              新增权限
            </a-button>
            <a-button @click="expandAll">
              <template #icon><ExpandOutlined /></template>
              展开全部
            </a-button>
            <a-button @click="collapseAll">
              <template #icon><CompressOutlined /></template>
              收起全部
            </a-button>
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="权限名称">
            <a-input v-model:value="searchForm.name" placeholder="请输入权限名称" />
          </a-form-item>
          <a-form-item label="权限编码">
            <a-input v-model:value="searchForm.code" placeholder="请输入权限编码" />
          </a-form-item>
          <a-form-item label="权限类型">
            <a-select v-model:value="searchForm.type" placeholder="请选择类型" allow-clear>
              <a-select-option :value="1">菜单</a-select-option>
              <a-select-option :value="2">按钮</a-select-option>
              <a-select-option :value="3">API</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="状态">
            <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
              <a-select-option :value="1">启用</a-select-option>
              <a-select-option :value="0">禁用</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon><SearchOutlined /></template>
                搜索
              </a-button>
              <a-button @click="resetSearch">
                <template #icon><ReloadOutlined /></template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 权限表格 -->
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="false"
        :loading="tableLoading"
        row-key="id"
        :default-expand-all-rows="expandedAll"
        :expanded-row-keys="expandedRowKeys"
        @expand="handleExpand"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeName(record.type) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'icon'">
            <component v-if="record.icon" :is="record.icon" />
            <span v-else>-</span>
          </template>
          <template v-else-if="column.key === 'status'">
            <a-tag :color="record.status === 1 ? 'green' : 'red'">
              {{ record.status === 1 ? '启用' : '禁用' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a @click="editPermission(record)">编辑</a>
              <a @click="addChild(record)">添加子权限</a>
              <a @click="toggleStatus(record)">
                {{ record.status === 1 ? '禁用' : '启用' }}
              </a>
              <a-dropdown>
                <a>更多 <DownOutlined /></a>
                <template #overlay>
                  <a-menu @click="({ key }) => handleMoreAction(key, record)">
                    <a-menu-item key="copy">复制权限</a-menu-item>
                    <a-menu-item key="move">移动权限</a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" style="color: #ff4d4f">删除</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑权限模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :confirm-loading="modalLoading"
      width="800px"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="权限名称" name="name">
              <a-input v-model:value="formData.name" placeholder="请输入权限名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="权限编码" name="code">
              <a-input v-model:value="formData.code" placeholder="请输入权限编码" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="权限类型" name="type">
              <a-select v-model:value="formData.type" placeholder="请选择权限类型">
                <a-select-option :value="1">菜单</a-select-option>
                <a-select-option :value="2">按钮</a-select-option>
                <a-select-option :value="3">API</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="父级权限" name="parent_id">
              <a-tree-select
                v-model:value="formData.parent_id"
                :tree-data="permissionTreeData"
                :field-names="{ children: 'children', label: 'name', value: 'id' }"
                placeholder="请选择父级权限"
                allow-clear
                tree-default-expand-all
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="路径/URL" name="path">
              <a-input v-model:value="formData.path" placeholder="请输入路径或URL" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="HTTP方法" name="method">
              <a-select v-model:value="formData.method" placeholder="请选择HTTP方法" allow-clear>
                <a-select-option value="GET">GET</a-select-option>
                <a-select-option value="POST">POST</a-select-option>
                <a-select-option value="PUT">PUT</a-select-option>
                <a-select-option value="DELETE">DELETE</a-select-option>
                <a-select-option value="PATCH">PATCH</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="图标" name="icon">
              <a-input v-model:value="formData.icon" placeholder="请输入图标名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="前端组件" name="component">
              <a-input v-model:value="formData.component" placeholder="请输入前端组件路径" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="排序" name="sort">
              <a-input-number v-model:value="formData.sort" :min="0" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="状态" name="status">
              <a-radio-group v-model:value="formData.status">
                <a-radio :value="1">启用</a-radio>
                <a-radio :value="0">禁用</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="权限描述" name="description">
          <a-textarea v-model:value="formData.description" placeholder="请输入权限描述" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  ExpandOutlined,
  CompressOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import {
  getPermissions,
  getPermissionTree,
  createPermission,
  updatePermission,
  deletePermission
} from '@/api/rbac'

// 响应式数据
const tableData = ref([])
const tableLoading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const formRef = ref()
const permissionTreeData = ref([])
const expandedAll = ref(false)
const expandedRowKeys = ref([])

// 搜索表单
const searchForm = reactive({
  name: '',
  code: '',
  type: null,
  status: null
})

// 表单数据
const formData = reactive({
  id: null,
  name: '',
  code: '',
  type: 1,
  parent_id: null,
  path: '',
  method: '',
  icon: '',
  component: '',
  sort: 0,
  status: 1,
  description: ''
})

// 计算属性
const modalTitle = computed(() => formData.id ? '编辑权限' : '新增权限')

// 表格列配置
const columns = [
  { title: '权限名称', dataIndex: 'name', key: 'name', width: 200 },
  { title: '权限编码', dataIndex: 'code', key: 'code', width: 150 },
  { title: '类型', key: 'type', width: 80 },
  { title: '图标', key: 'icon', width: 60 },
  { title: '路径/URL', dataIndex: 'path', key: 'path', ellipsis: true },
  { title: 'HTTP方法', dataIndex: 'method', key: 'method', width: 100 },
  { title: '排序', dataIndex: 'sort', key: 'sort', width: 80 },
  { title: '状态', key: 'status', width: 80 },
  { title: '操作', key: 'action', width: 200, fixed: 'right' }
]

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入权限名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入权限编码', trigger: 'blur' }],
  type: [{ required: true, message: '请选择权限类型', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 获取类型名称
const getTypeName = (type) => {
  const names = { 1: '菜单', 2: '按钮', 3: 'API' }
  return names[type] || '未知'
}

// 获取类型颜色
const getTypeColor = (type) => {
  const colors = { 1: 'blue', 2: 'green', 3: 'orange' }
  return colors[type] || 'default'
}

// 加载权限列表
const loadPermissions = async () => {
  tableLoading.value = true
  try {
    const response = await getPermissions(searchForm)
    tableData.value = response.data || []
  } catch (error) {
    message.error('加载权限列表失败')
  } finally {
    tableLoading.value = false
  }
}

// 加载权限树
const loadPermissionTree = async () => {
  try {
    const response = await getPermissionTree()
    permissionTreeData.value = response.data || []
  } catch (error) {
    message.error('加载权限树失败')
  }
}

// 搜索
const handleSearch = () => {
  loadPermissions()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    code: '',
    type: null,
    status: null
  })
  loadPermissions()
}

// 展开全部
const expandAll = () => {
  expandedAll.value = true
  expandedRowKeys.value = getAllKeys(tableData.value)
}

// 收起全部
const collapseAll = () => {
  expandedAll.value = false
  expandedRowKeys.value = []
}

// 获取所有键
const getAllKeys = (data) => {
  const keys = []
  const traverse = (items) => {
    items.forEach(item => {
      keys.push(item.id)
      if (item.children && item.children.length > 0) {
        traverse(item.children)
      }
    })
  }
  traverse(data)
  return keys
}

// 处理展开
const handleExpand = (expanded, record) => {
  if (expanded) {
    expandedRowKeys.value.push(record.id)
  } else {
    const index = expandedRowKeys.value.indexOf(record.id)
    if (index > -1) {
      expandedRowKeys.value.splice(index, 1)
    }
  }
}

// 显示创建模态框
const showCreateModal = () => {
  resetForm()
  modalVisible.value = true
}

// 编辑权限
const editPermission = (permission) => {
  Object.assign(formData, {
    id: permission.id,
    name: permission.name,
    code: permission.code,
    type: permission.type,
    parent_id: permission.parent_id,
    path: permission.path,
    method: permission.method,
    icon: permission.icon,
    component: permission.component,
    sort: permission.sort,
    status: permission.status,
    description: permission.description
  })
  modalVisible.value = true
}

// 添加子权限
const addChild = (permission) => {
  resetForm()
  formData.parent_id = permission.id
  modalVisible.value = true
}

// 切换状态
const toggleStatus = async (permission) => {
  const newStatus = permission.status === 1 ? 0 : 1
  const action = newStatus === 1 ? '启用' : '禁用'
  
  Modal.confirm({
    title: `${action}权限`,
    content: `确定要${action}权限"${permission.name}"吗？`,
    onOk: async () => {
      try {
        await updatePermission(permission.id, { status: newStatus })
        message.success(`${action}成功`)
        loadPermissions()
      } catch (error) {
        message.error(`${action}失败`)
      }
    }
  })
}

// 更多操作
const handleMoreAction = async (key, permission) => {
  switch (key) {
    case 'copy':
      copyPermission(permission)
      break
    case 'move':
      movePermission(permission)
      break
    case 'delete':
      deletePermissionConfirm(permission)
      break
  }
}

// 复制权限
const copyPermission = (permission) => {
  Object.assign(formData, {
    id: null,
    name: `${permission.name}_副本`,
    code: `${permission.code}_copy`,
    type: permission.type,
    parent_id: permission.parent_id,
    path: permission.path,
    method: permission.method,
    icon: permission.icon,
    component: permission.component,
    sort: permission.sort,
    status: permission.status,
    description: permission.description
  })
  modalVisible.value = true
}

// 移动权限
const movePermission = (permission) => {
  message.info('移动权限功能开发中...')
}

// 删除权限确认
const deletePermissionConfirm = (permission) => {
  Modal.confirm({
    title: '删除权限',
    content: `确定要删除权限"${permission.name}"吗？删除后不可恢复。`,
    onOk: async () => {
      try {
        await deletePermission(permission.id)
        message.success('删除成功')
        loadPermissions()
        loadPermissionTree()
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

// 处理模态框确认
const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    modalLoading.value = true
    
    if (formData.id) {
      await updatePermission(formData.id, formData)
      message.success('更新成功')
    } else {
      await createPermission(formData)
      message.success('创建成功')
    }
    
    modalVisible.value = false
    loadPermissions()
    loadPermissionTree()
  } catch (error) {
    if (error.errorFields) {
      message.error('请检查表单输入')
    } else {
      message.error('操作失败')
    }
  } finally {
    modalLoading.value = false
  }
}

// 处理模态框取消
const handleModalCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: null,
    name: '',
    code: '',
    type: 1,
    parent_id: null,
    path: '',
    method: '',
    icon: '',
    component: '',
    sort: 0,
    status: 1,
    description: ''
  })
  formRef.value?.resetFields()
}

// 组件挂载时加载数据
onMounted(() => {
  loadPermissions()
  loadPermissionTree()
})
</script>

<style scoped>
.permission-manage {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h3 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}
</style>
