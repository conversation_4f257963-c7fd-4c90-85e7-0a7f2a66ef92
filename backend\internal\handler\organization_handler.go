package handler

import (
	"net/http"
	"quality_control/backend/internal/models"
	"quality_control/backend/internal/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

type OrganizationHandler struct {
	organizationService *service.OrganizationService
}

func NewOrganizationHandler(organizationService *service.OrganizationService) *OrganizationHandler {
	return &OrganizationHandler{
		organizationService: organizationService,
	}
}

// GetOrganizationTree 获取组织架构树
// @Summary 获取组织架构树
// @Description 获取完整的组织架构树形结构
// @Tags 组织管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.Response{data=[]service.OrganizationTreeNode}
// @Failure 500 {object} models.Response
// @Router /organizations/tree [get]
func (h *OrganizationHandler) GetOrganizationTree(c *gin.Context) {
	tree, err := h.organizationService.GetOrganizationTree()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "获取组织架构树失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    tree,
	})
}

// GetOrganizations 获取组织列表
// @Summary 获取组织列表
// @Description 获取组织列表，支持分页和筛选
// @Tags 组织管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param name query string false "组织名称"
// @Param type query string false "组织类型"
// @Success 200 {object} models.Response{data=service.UserListResponse}
// @Failure 500 {object} models.Response
// @Router /organizations [get]
func (h *OrganizationHandler) GetOrganizations(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	name := c.Query("name")
	orgType := c.Query("type")

	resp, err := h.organizationService.GetOrganizations(page, pageSize, name, orgType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "获取组织列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    resp,
	})
}

// GetOrganization 获取单个组织
// @Summary 获取组织详情
// @Description 根据ID获取组织详情
// @Tags 组织管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "组织ID"
// @Success 200 {object} models.Response{data=models.Organization}
// @Failure 400 {object} models.Response
// @Router /organizations/{id} [get]
func (h *OrganizationHandler) GetOrganization(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "组织ID格式错误",
		})
		return
	}

	organization, err := h.organizationService.GetOrganization(uint(id))
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    organization,
	})
}

// CreateOrganization 创建组织
// @Summary 创建组织
// @Description 创建新组织
// @Tags 组织管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body service.CreateOrganizationRequest true "创建组织请求"
// @Success 200 {object} models.Response{data=models.Organization}
// @Failure 400 {object} models.Response
// @Router /organizations [post]
func (h *OrganizationHandler) CreateOrganization(c *gin.Context) {
	var req service.CreateOrganizationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	organization, err := h.organizationService.CreateOrganization(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "创建成功",
		Data:    organization,
	})
}

// UpdateOrganization 更新组织
// @Summary 更新组织
// @Description 更新组织信息
// @Tags 组织管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "组织ID"
// @Param request body service.UpdateOrganizationRequest true "更新组织请求"
// @Success 200 {object} models.Response{data=models.Organization}
// @Failure 400 {object} models.Response
// @Router /organizations/{id} [put]
func (h *OrganizationHandler) UpdateOrganization(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "组织ID格式错误",
		})
		return
	}

	var req service.UpdateOrganizationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	organization, err := h.organizationService.UpdateOrganization(uint(id), &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "更新成功",
		Data:    organization,
	})
}

// DeleteOrganization 删除组织
// @Summary 删除组织
// @Description 删除组织
// @Tags 组织管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "组织ID"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Router /organizations/{id} [delete]
func (h *OrganizationHandler) DeleteOrganization(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "组织ID格式错误",
		})
		return
	}

	if err := h.organizationService.DeleteOrganization(uint(id)); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "删除成功",
	})
}

// MoveOrganizationRequest 移动组织请求
type MoveOrganizationRequest struct {
	ParentID *uint `json:"parent_id"`
}

// MoveOrganization 移动组织
// @Summary 移动组织
// @Description 移动组织到新的父组织下
// @Tags 组织管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "组织ID"
// @Param request body MoveOrganizationRequest true "移动组织请求"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Router /organizations/{id}/move [put]
func (h *OrganizationHandler) MoveOrganization(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "组织ID格式错误",
		})
		return
	}

	var req MoveOrganizationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	if err := h.organizationService.MoveOrganization(uint(id), req.ParentID); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "移动成功",
	})
}
