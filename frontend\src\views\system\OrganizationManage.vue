<template>
  <div class="organization-manage">
    <a-card :bordered="false">
      <template #title>
        <div class="page-header">
          <h3>组织架构管理</h3>
          <div class="header-actions">
            <a-button type="primary" @click="showCreateModal">
              <template #icon><PlusOutlined /></template>
              新增组织
            </a-button>
            <a-button @click="refreshTree">
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>
          </div>
        </div>
      </template>

      <a-row :gutter="16">
        <!-- 左侧组织树 -->
        <a-col :span="8">
          <a-card title="组织架构" size="small" :bordered="false">
            <a-tree
              v-model:selectedKeys="selectedKeys"
              v-model:expandedKeys="expandedKeys"
              :tree-data="treeData"
              :field-names="{ children: 'children', title: 'name', key: 'id' }"
              show-line
              @select="onSelectNode"
              @right-click="onRightClick"
            >
              <template #title="{ name, type, status }">
                <span :class="{ 'disabled-node': status === 0 }">
                  <component :is="getOrgIcon(type)" style="margin-right: 4px" />
                  {{ name }}
                </span>
              </template>
            </a-tree>
          </a-card>
        </a-col>

        <!-- 右侧详情 -->
        <a-col :span="16">
          <a-card title="组织详情" size="small" :bordered="false">
            <div v-if="!selectedOrg" class="empty-state">
              <a-empty description="请选择左侧组织节点查看详情" />
            </div>
            
            <div v-else>
              <!-- 组织基本信息 -->
              <a-descriptions :column="2" bordered size="small">
                <a-descriptions-item label="组织名称">{{ selectedOrg.name }}</a-descriptions-item>
                <a-descriptions-item label="组织编码">{{ selectedOrg.code }}</a-descriptions-item>
                <a-descriptions-item label="组织类型">
                  <a-tag :color="getOrgTypeColor(selectedOrg.type)">
                    {{ getOrgTypeName(selectedOrg.type) }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="状态">
                  <a-tag :color="selectedOrg.status === 1 ? 'green' : 'red'">
                    {{ selectedOrg.status === 1 ? '启用' : '禁用' }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="层级">{{ selectedOrg.level }}</a-descriptions-item>
                <a-descriptions-item label="排序">{{ selectedOrg.sort }}</a-descriptions-item>
                <a-descriptions-item label="描述" :span="2">
                  {{ selectedOrg.description || '暂无描述' }}
                </a-descriptions-item>
              </a-descriptions>

              <!-- 操作按钮 -->
              <div class="action-buttons">
                <a-button type="primary" @click="editOrganization">
                  <template #icon><EditOutlined /></template>
                  编辑
                </a-button>
                <a-button @click="addChildOrganization">
                  <template #icon><PlusOutlined /></template>
                  添加子组织
                </a-button>
                <a-button danger @click="deleteOrganization">
                  <template #icon><DeleteOutlined /></template>
                  删除
                </a-button>
              </div>

              <!-- 子组织列表 -->
              <a-divider>子组织</a-divider>
              <a-table
                :columns="childColumns"
                :data-source="selectedOrg.children || []"
                :pagination="false"
                size="small"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'type'">
                    <a-tag :color="getOrgTypeColor(record.type)">
                      {{ getOrgTypeName(record.type) }}
                    </a-tag>
                  </template>
                  <template v-else-if="column.key === 'status'">
                    <a-tag :color="record.status === 1 ? 'green' : 'red'">
                      {{ record.status === 1 ? '启用' : '禁用' }}
                    </a-tag>
                  </template>
                  <template v-else-if="column.key === 'action'">
                    <a-space>
                      <a @click="editChild(record)">编辑</a>
                      <a @click="deleteChild(record)" style="color: #ff4d4f">删除</a>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </a-card>

    <!-- 新增/编辑组织模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :confirm-loading="modalLoading"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-form-item label="组织名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入组织名称" />
        </a-form-item>
        
        <a-form-item label="组织编码" name="code">
          <a-input v-model:value="formData.code" placeholder="请输入组织编码" />
        </a-form-item>
        
        <a-form-item label="上级组织" name="parent_id">
          <a-tree-select
            v-model:value="formData.parent_id"
            :tree-data="treeSelectData"
            :field-names="{ children: 'children', label: 'name', value: 'id' }"
            placeholder="请选择上级组织"
            allow-clear
            tree-default-expand-all
          />
        </a-form-item>
        
        <a-form-item label="组织类型" name="type">
          <a-select v-model:value="formData.type" placeholder="请选择组织类型">
            <a-select-option :value="1">院级</a-select-option>
            <a-select-option :value="2">科室</a-select-option>
            <a-select-option :value="3">组</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="排序" name="sort">
          <a-input-number v-model:value="formData.sort" :min="0" style="width: 100%" />
        </a-form-item>
        
        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="formData.status">
            <a-radio :value="1">启用</a-radio>
            <a-radio :value="0">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item label="描述" name="description">
          <a-textarea v-model:value="formData.description" placeholder="请输入组织描述" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 右键菜单 -->
    <a-dropdown
      v-model:open="contextMenuVisible"
      :trigger="['contextmenu']"
      :style="{ position: 'absolute', left: contextMenuX + 'px', top: contextMenuY + 'px' }"
    >
      <div></div>
      <template #overlay>
        <a-menu @click="handleContextMenu">
          <a-menu-item key="add">
            <PlusOutlined />
            添加子组织
          </a-menu-item>
          <a-menu-item key="edit">
            <EditOutlined />
            编辑组织
          </a-menu-item>
          <a-menu-divider />
          <a-menu-item key="delete" style="color: #ff4d4f">
            <DeleteOutlined />
            删除组织
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  BankOutlined,
  TeamOutlined,
  UserOutlined
} from '@ant-design/icons-vue'
import {
  getOrganizationTree,
  createOrganization,
  updateOrganization,
  deleteOrganization as deleteOrganizationAPI
} from '@/api/organization'

// 响应式数据
const treeData = ref([])
const selectedKeys = ref([])
const expandedKeys = ref([])
const selectedOrg = ref(null)
const modalVisible = ref(false)
const modalLoading = ref(false)
const formRef = ref()
const contextMenuVisible = ref(false)
const contextMenuX = ref(0)
const contextMenuY = ref(0)
const rightClickNode = ref(null)

// 表单数据
const formData = reactive({
  id: null,
  name: '',
  code: '',
  parent_id: null,
  type: 1,
  sort: 0,
  status: 1,
  description: ''
})

// 计算属性
const modalTitle = computed(() => formData.id ? '编辑组织' : '新增组织')
const treeSelectData = computed(() => {
  // 过滤掉当前编辑的节点，避免循环引用
  return filterTreeData(treeData.value, formData.id)
})

// 子组织表格列配置
const childColumns = [
  { title: '组织名称', dataIndex: 'name', key: 'name' },
  { title: '组织编码', dataIndex: 'code', key: 'code' },
  { title: '类型', dataIndex: 'type', key: 'type' },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '排序', dataIndex: 'sort', key: 'sort' },
  { title: '操作', key: 'action', width: 120 }
]

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入组织名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入组织编码', trigger: 'blur' }],
  type: [{ required: true, message: '请选择组织类型', trigger: 'change' }]
}

// 获取组织图标
const getOrgIcon = (type) => {
  const icons = {
    1: BankOutlined,
    2: TeamOutlined,
    3: UserOutlined
  }
  return icons[type] || BankOutlined
}

// 获取组织类型名称
const getOrgTypeName = (type) => {
  const names = { 1: '院级', 2: '科室', 3: '组' }
  return names[type] || '未知'
}

// 获取组织类型颜色
const getOrgTypeColor = (type) => {
  const colors = { 1: 'red', 2: 'blue', 3: 'green' }
  return colors[type] || 'default'
}

// 过滤树数据，避免循环引用
const filterTreeData = (data, excludeId) => {
  return data.filter(item => item.id !== excludeId).map(item => ({
    ...item,
    children: item.children ? filterTreeData(item.children, excludeId) : []
  }))
}

// 加载组织树
const loadOrganizationTree = async () => {
  try {
    const response = await getOrganizationTree()
    treeData.value = response.data || []
    // 默认展开所有节点
    expandedKeys.value = getAllKeys(treeData.value)
  } catch (error) {
    message.error('加载组织架构失败')
  }
}

// 获取所有节点的key
const getAllKeys = (data) => {
  const keys = []
  const traverse = (nodes) => {
    nodes.forEach(node => {
      keys.push(node.id)
      if (node.children) {
        traverse(node.children)
      }
    })
  }
  traverse(data)
  return keys
}

// 选择节点
const onSelectNode = (keys, { node }) => {
  if (keys.length > 0) {
    selectedOrg.value = node
  } else {
    selectedOrg.value = null
  }
}

// 右键点击
const onRightClick = ({ event, node }) => {
  event.preventDefault()
  rightClickNode.value = node
  contextMenuX.value = event.clientX
  contextMenuY.value = event.clientY
  contextMenuVisible.value = true
}

// 处理右键菜单
const handleContextMenu = ({ key }) => {
  contextMenuVisible.value = false
  
  switch (key) {
    case 'add':
      addChildOrganization(rightClickNode.value)
      break
    case 'edit':
      editOrganization(rightClickNode.value)
      break
    case 'delete':
      deleteOrganization(rightClickNode.value)
      break
  }
}

// 显示创建模态框
const showCreateModal = () => {
  resetForm()
  modalVisible.value = true
}

// 编辑组织
const editOrganization = (org = selectedOrg.value) => {
  if (!org) {
    message.warning('请先选择要编辑的组织')
    return
  }
  
  Object.assign(formData, {
    id: org.id,
    name: org.name,
    code: org.code,
    parent_id: org.parent_id,
    type: org.type,
    sort: org.sort,
    status: org.status,
    description: org.description
  })
  
  modalVisible.value = true
}

// 添加子组织
const addChildOrganization = (parentOrg = selectedOrg.value) => {
  resetForm()
  if (parentOrg) {
    formData.parent_id = parentOrg.id
    formData.type = Math.min(parentOrg.type + 1, 3) // 子组织类型递增，最大为3
  }
  modalVisible.value = true
}

// 删除组织
const deleteOrganization = (org = selectedOrg.value) => {
  if (!org) {
    message.warning('请先选择要删除的组织')
    return
  }
  
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除组织"${org.name}"吗？删除后不可恢复。`,
    onOk: async () => {
      try {
        await deleteOrganizationAPI(org.id)
        message.success('删除成功')
        await loadOrganizationTree()
        selectedOrg.value = null
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

// 处理模态框确认
const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    modalLoading.value = true
    
    if (formData.id) {
      await updateOrganization(formData.id, formData)
      message.success('更新成功')
    } else {
      await createOrganization(formData)
      message.success('创建成功')
    }
    
    modalVisible.value = false
    await loadOrganizationTree()
  } catch (error) {
    if (error.errorFields) {
      message.error('请检查表单输入')
    } else {
      message.error('操作失败')
    }
  } finally {
    modalLoading.value = false
  }
}

// 处理模态框取消
const handleModalCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: null,
    name: '',
    code: '',
    parent_id: null,
    type: 1,
    sort: 0,
    status: 1,
    description: ''
  })
  formRef.value?.resetFields()
}

// 刷新树
const refreshTree = () => {
  loadOrganizationTree()
}

// 编辑子组织
const editChild = (child) => {
  editOrganization(child)
}

// 删除子组织
const deleteChild = (child) => {
  deleteOrganization(child)
}

// 组件挂载时加载数据
onMounted(() => {
  loadOrganizationTree()
})
</script>

<style scoped>
.organization-manage {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h3 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.disabled-node {
  color: #ccc;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.action-buttons {
  margin: 16px 0;
  display: flex;
  gap: 8px;
}

:deep(.ant-tree-title) {
  display: flex;
  align-items: center;
}
</style>
