<template>
  <div class="notification-manage">
    <a-card :bordered="false">
      <template #title>
        <div class="page-header">
          <h3>通知管理</h3>
          <div class="header-actions">
            <a-button type="primary" @click="showCreateModal">
              <template #icon><PlusOutlined /></template>
              发送通知
            </a-button>
            <a-button @click="markAllAsRead">
              <template #icon><CheckOutlined /></template>
              全部已读
            </a-button>
          </div>
        </div>
      </template>

      <!-- 通知类型标签 -->
      <a-tabs v-model:activeKey="activeType" @change="handleTypeChange">
        <a-tab-pane key="all" tab="全部通知" />
        <a-tab-pane key="1" tab="系统通知" />
        <a-tab-pane key="2" tab="业务通知" />
        <a-tab-pane key="3" tab="审批通知" />
      </a-tabs>

      <!-- 搜索表单 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="通知标题">
            <a-input v-model:value="searchForm.title" placeholder="请输入通知标题" />
          </a-form-item>
          <a-form-item label="发送人">
            <a-input v-model:value="searchForm.sender" placeholder="请输入发送人" />
          </a-form-item>
          <a-form-item label="接收人">
            <a-input v-model:value="searchForm.receiver" placeholder="请输入接收人" />
          </a-form-item>
          <a-form-item label="通知级别">
            <a-select v-model:value="searchForm.level" placeholder="请选择级别" allow-clear>
              <a-select-option :value="1">普通</a-select-option>
              <a-select-option :value="2">重要</a-select-option>
              <a-select-option :value="3">紧急</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="阅读状态">
            <a-select v-model:value="searchForm.is_read" placeholder="请选择状态" allow-clear>
              <a-select-option :value="false">未读</a-select-option>
              <a-select-option :value="true">已读</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon><SearchOutlined /></template>
                搜索
              </a-button>
              <a-button @click="resetSearch">
                <template #icon><ReloadOutlined /></template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 通知表格 -->
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="tableLoading"
        row-key="id"
        @change="handleTableChange"
        :row-class-name="getRowClassName"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'title'">
            <div class="notification-title">
              <a @click="viewNotification(record)" :class="{ 'unread': !record.is_read }">
                {{ record.title }}
              </a>
              <a-tag v-if="!record.is_read" color="red" size="small" style="margin-left: 8px">未读</a-tag>
            </div>
          </template>
          <template v-else-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeName(record.type) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'level'">
            <a-tag :color="getLevelColor(record.level)">
              {{ getLevelName(record.level) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'sender'">
            <div class="user-info">
              <a-avatar :size="24" :icon="h(UserOutlined)" />
              <span style="margin-left: 8px">{{ record.sender?.real_name || '系统' }}</span>
            </div>
          </template>
          <template v-else-if="column.key === 'receiver'">
            <div class="user-info">
              <a-avatar :size="24" :icon="h(UserOutlined)" />
              <span style="margin-left: 8px">{{ record.receiver?.real_name || '-' }}</span>
            </div>
          </template>
          <template v-else-if="column.key === 'is_read'">
            <a-tag :color="record.is_read ? 'green' : 'red'">
              {{ record.is_read ? '已读' : '未读' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'created_at'">
            {{ formatDate(record.created_at) }}
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a @click="viewNotification(record)">查看</a>
              <a @click="markAsRead(record)" v-if="!record.is_read">标记已读</a>
              <a-dropdown>
                <a>更多 <DownOutlined /></a>
                <template #overlay>
                  <a-menu @click="({ key }) => handleMoreAction(key, record)">
                    <a-menu-item key="resend">重新发送</a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" style="color: #ff4d4f">删除</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 发送通知模态框 -->
    <a-modal
      v-model:open="modalVisible"
      title="发送通知"
      :confirm-loading="modalLoading"
      width="600px"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-form-item label="通知标题" name="title">
          <a-input v-model:value="formData.title" placeholder="请输入通知标题" />
        </a-form-item>
        
        <a-form-item label="通知类型" name="type">
          <a-select v-model:value="formData.type" placeholder="请选择通知类型">
            <a-select-option :value="1">系统通知</a-select-option>
            <a-select-option :value="2">业务通知</a-select-option>
            <a-select-option :value="3">审批通知</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="通知级别" name="level">
          <a-select v-model:value="formData.level" placeholder="请选择通知级别">
            <a-select-option :value="1">普通</a-select-option>
            <a-select-option :value="2">重要</a-select-option>
            <a-select-option :value="3">紧急</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="接收人" name="receiver_id">
          <a-select 
            v-model:value="formData.receiver_id" 
            placeholder="请选择接收人"
            show-search
            :filter-option="filterUser"
          >
            <a-select-option v-for="user in userList" :key="user.id" :value="user.id">
              {{ user.real_name }} ({{ user.username }})
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="通知内容" name="content">
          <a-textarea v-model:value="formData.content" placeholder="请输入通知内容" :rows="5" />
        </a-form-item>
        
        <a-form-item label="业务关联" name="business_info">
          <a-row :gutter="8">
            <a-col :span="12">
              <a-input v-model:value="formData.business_type" placeholder="业务类型" />
            </a-col>
            <a-col :span="12">
              <a-input v-model:value="formData.business_id" placeholder="业务ID" />
            </a-col>
          </a-row>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 通知详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="通知详情"
      :footer="null"
      width="600px"
    >
      <div v-if="currentNotification">
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="通知标题">
            {{ currentNotification.title }}
          </a-descriptions-item>
          <a-descriptions-item label="通知类型">
            <a-tag :color="getTypeColor(currentNotification.type)">
              {{ getTypeName(currentNotification.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="通知级别">
            <a-tag :color="getLevelColor(currentNotification.level)">
              {{ getLevelName(currentNotification.level) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="发送人">
            {{ currentNotification.sender?.real_name || '系统' }}
          </a-descriptions-item>
          <a-descriptions-item label="接收人">
            {{ currentNotification.receiver?.real_name || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="发送时间">
            {{ formatDate(currentNotification.created_at) }}
          </a-descriptions-item>
          <a-descriptions-item label="阅读时间">
            {{ currentNotification.read_at ? formatDate(currentNotification.read_at) : '未读' }}
          </a-descriptions-item>
          <a-descriptions-item label="通知内容">
            <div class="notification-content">{{ currentNotification.content }}</div>
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h } from 'vue'
import { message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  PlusOutlined,
  CheckOutlined,
  SearchOutlined,
  ReloadOutlined,
  DownOutlined,
  UserOutlined
} from '@ant-design/icons-vue'
import {
  getNotifications,
  createNotification,
  deleteNotification,
  markNotificationAsRead,
  markAllNotificationsAsRead
} from '@/api/system'
import { getUsers } from '@/api/user'

// 响应式数据
const tableData = ref([])
const tableLoading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const detailModalVisible = ref(false)
const formRef = ref()
const activeType = ref('all')
const userList = ref([])
const currentNotification = ref(null)

// 搜索表单
const searchForm = reactive({
  title: '',
  sender: '',
  receiver: '',
  level: null,
  is_read: null,
  type: ''
})

// 表单数据
const formData = reactive({
  title: '',
  content: '',
  type: 1,
  level: 1,
  receiver_id: null,
  business_type: '',
  business_id: ''
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  { title: '通知标题', key: 'title', ellipsis: true },
  { title: '类型', key: 'type', width: 100 },
  { title: '级别', key: 'level', width: 80 },
  { title: '发送人', key: 'sender', width: 120 },
  { title: '接收人', key: 'receiver', width: 120 },
  { title: '状态', key: 'is_read', width: 80 },
  { title: '发送时间', key: 'created_at', width: 180 },
  { title: '操作', key: 'action', width: 150, fixed: 'right' }
]

// 表单验证规则
const formRules = {
  title: [{ required: true, message: '请输入通知标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入通知内容', trigger: 'blur' }],
  type: [{ required: true, message: '请选择通知类型', trigger: 'change' }],
  level: [{ required: true, message: '请选择通知级别', trigger: 'change' }],
  receiver_id: [{ required: true, message: '请选择接收人', trigger: 'change' }]
}

// 获取类型名称
const getTypeName = (type) => {
  const names = { 1: '系统通知', 2: '业务通知', 3: '审批通知' }
  return names[type] || '未知'
}

// 获取类型颜色
const getTypeColor = (type) => {
  const colors = { 1: 'blue', 2: 'green', 3: 'orange' }
  return colors[type] || 'default'
}

// 获取级别名称
const getLevelName = (level) => {
  const names = { 1: '普通', 2: '重要', 3: '紧急' }
  return names[level] || '未知'
}

// 获取级别颜色
const getLevelColor = (level) => {
  const colors = { 1: 'default', 2: 'orange', 3: 'red' }
  return colors[level] || 'default'
}

// 格式化日期
const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

// 获取行样式
const getRowClassName = (record) => {
  return record.is_read ? '' : 'unread-row'
}

// 用户过滤
const filterUser = (input, option) => {
  return option.children.toLowerCase().includes(input.toLowerCase())
}

// 加载通知列表
const loadNotifications = async () => {
  tableLoading.value = true
  try {
    const params = {
      page: pagination.current,
      page_size: pagination.pageSize,
      ...searchForm
    }
    
    if (activeType.value !== 'all') {
      params.type = activeType.value
    }
    
    const response = await getNotifications(params)
    tableData.value = response.data.list || []
    pagination.total = response.data.total || 0
  } catch (error) {
    message.error('加载通知列表失败')
  } finally {
    tableLoading.value = false
  }
}

// 加载用户列表
const loadUsers = async () => {
  try {
    const response = await getUsers({ page_size: 1000 })
    userList.value = response.data.list || []
  } catch (error) {
    message.error('加载用户列表失败')
  }
}

// 处理类型变化
const handleTypeChange = (type) => {
  activeType.value = type
  searchForm.type = type === 'all' ? '' : type
  pagination.current = 1
  loadNotifications()
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadNotifications()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    title: '',
    sender: '',
    receiver: '',
    level: null,
    is_read: null,
    type: activeType.value === 'all' ? '' : activeType.value
  })
  pagination.current = 1
  loadNotifications()
}

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadNotifications()
}

// 显示创建模态框
const showCreateModal = () => {
  resetForm()
  modalVisible.value = true
}

// 查看通知
const viewNotification = async (notification) => {
  currentNotification.value = notification
  detailModalVisible.value = true
  
  // 如果是未读通知，标记为已读
  if (!notification.is_read) {
    await markAsRead(notification)
  }
}

// 标记为已读
const markAsRead = async (notification) => {
  try {
    await markNotificationAsRead(notification.id)
    message.success('标记已读成功')
    loadNotifications()
  } catch (error) {
    message.error('标记已读失败')
  }
}

// 全部标记为已读
const markAllAsRead = async () => {
  Modal.confirm({
    title: '标记全部已读',
    content: '确定要将所有通知标记为已读吗？',
    onOk: async () => {
      try {
        await markAllNotificationsAsRead()
        message.success('全部标记已读成功')
        loadNotifications()
      } catch (error) {
        message.error('操作失败')
      }
    }
  })
}

// 更多操作
const handleMoreAction = async (key, notification) => {
  switch (key) {
    case 'resend':
      resendNotification(notification)
      break
    case 'delete':
      deleteNotificationConfirm(notification)
      break
  }
}

// 重新发送通知
const resendNotification = (notification) => {
  Object.assign(formData, {
    title: notification.title,
    content: notification.content,
    type: notification.type,
    level: notification.level,
    receiver_id: notification.receiver_id,
    business_type: notification.business_type,
    business_id: notification.business_id
  })
  modalVisible.value = true
}

// 删除通知确认
const deleteNotificationConfirm = (notification) => {
  Modal.confirm({
    title: '删除通知',
    content: `确定要删除通知"${notification.title}"吗？删除后不可恢复。`,
    onOk: async () => {
      try {
        await deleteNotification(notification.id)
        message.success('删除成功')
        loadNotifications()
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

// 处理模态框确认
const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    modalLoading.value = true
    
    await createNotification(formData)
    message.success('通知发送成功')
    
    modalVisible.value = false
    loadNotifications()
  } catch (error) {
    if (error.errorFields) {
      message.error('请检查表单输入')
    } else {
      message.error('发送失败')
    }
  } finally {
    modalLoading.value = false
  }
}

// 处理模态框取消
const handleModalCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    title: '',
    content: '',
    type: 1,
    level: 1,
    receiver_id: null,
    business_type: '',
    business_id: ''
  })
  formRef.value?.resetFields()
}

// 组件挂载时加载数据
onMounted(() => {
  loadNotifications()
  loadUsers()
})
</script>

<style scoped>
.notification-manage {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h3 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.notification-title a.unread {
  font-weight: bold;
  color: #1890ff;
}

.user-info {
  display: flex;
  align-items: center;
}

.notification-content {
  white-space: pre-wrap;
  word-break: break-all;
}

:deep(.unread-row) {
  background-color: #f6ffed;
}
</style>
