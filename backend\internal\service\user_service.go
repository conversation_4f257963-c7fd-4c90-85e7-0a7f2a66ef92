package service

import (
	"errors"
	"quality_control/backend/internal/models"
	"quality_control/backend/pkg/logger"

	"gorm.io/gorm"
)

type UserService struct {
	db *gorm.DB
}

func NewUserService(db *gorm.DB) *UserService {
	return &UserService{db: db}
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username       string `json:"username" binding:"required"`
	Password       string `json:"password" binding:"required,min=6"`
	Email          string `json:"email" binding:"email"`
	Phone          string `json:"phone"`
	RealName       string `json:"real_name" binding:"required"`
	OrganizationID uint   `json:"organization_id" binding:"required"`
	RoleIDs        []uint `json:"role_ids"`
	PositionIDs    []uint `json:"position_ids"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Email          string `json:"email" binding:"email"`
	Phone          string `json:"phone"`
	RealName       string `json:"real_name"`
	OrganizationID uint   `json:"organization_id"`
	Status         string `json:"status"`
	RoleIDs        []uint `json:"role_ids"`
	PositionIDs    []uint `json:"position_ids"`
}

// UserListRequest 用户列表请求
type UserListRequest struct {
	models.PageRequest
	Username       string `form:"username"`
	RealName       string `form:"real_name"`
	OrganizationID uint   `form:"organization_id"`
	Status         string `form:"status"`
}

// UserListResponse 用户列表响应
type UserListResponse struct {
	List     []models.User     `json:"list"`
	PageInfo models.PageInfo   `json:"page_info"`
}

// GetUsers 获取用户列表
func (s *UserService) GetUsers(req *UserListRequest) (*UserListResponse, error) {
	req.SetDefaults()
	
	query := s.db.Model(&models.User{})
	
	// 添加查询条件
	if req.Username != "" {
		query = query.Where("username LIKE ?", "%"+req.Username+"%")
	}
	if req.RealName != "" {
		query = query.Where("real_name LIKE ?", "%"+req.RealName+"%")
	}
	if req.OrganizationID > 0 {
		query = query.Where("organization_id = ?", req.OrganizationID)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 获取列表数据
	var users []models.User
	if err := query.Preload("Roles").Preload("Positions").
		Offset(req.GetOffset()).Limit(req.PageSize).
		Find(&users).Error; err != nil {
		return nil, err
	}

	// 清除密码字段
	for i := range users {
		users[i].Password = ""
	}

	return &UserListResponse{
		List: users,
		PageInfo: models.PageInfo{
			Page:     req.Page,
			PageSize: req.PageSize,
			Total:    total,
		},
	}, nil
}

// GetUser 获取单个用户
func (s *UserService) GetUser(id uint) (*models.User, error) {
	var user models.User
	if err := s.db.Preload("Roles").Preload("Positions").
		Where("id = ?", id).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, err
	}

	user.Password = ""
	return &user, nil
}

// CreateUser 创建用户
func (s *UserService) CreateUser(req *CreateUserRequest) (*models.User, error) {
	// 检查用户名是否已存在
	var count int64
	if err := s.db.Model(&models.User{}).Where("username = ?", req.Username).Count(&count).Error; err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.New("用户名已存在")
	}

	// 检查邮箱是否已存在
	if req.Email != "" {
		if err := s.db.Model(&models.User{}).Where("email = ?", req.Email).Count(&count).Error; err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, errors.New("邮箱已存在")
		}
	}

	// 加密密码
	authService := NewAuthService(s.db, "")
	hashedPassword, err := authService.HashPassword(req.Password)
	if err != nil {
		return nil, err
	}

	// 创建用户
	user := &models.User{
		Username:       req.Username,
		Password:       hashedPassword,
		Email:          req.Email,
		Phone:          req.Phone,
		RealName:       req.RealName,
		OrganizationID: req.OrganizationID,
		Status:         models.StatusActive,
	}

	if err := s.db.Create(user).Error; err != nil {
		return nil, err
	}

	// 分配角色
	if len(req.RoleIDs) > 0 {
		if err := s.AssignRoles(user.ID, req.RoleIDs); err != nil {
			logger.Error("分配角色失败:", err)
		}
	}

	// 分配岗位
	if len(req.PositionIDs) > 0 {
		if err := s.AssignPositions(user.ID, req.PositionIDs); err != nil {
			logger.Error("分配岗位失败:", err)
		}
	}

	user.Password = ""
	return user, nil
}

// UpdateUser 更新用户
func (s *UserService) UpdateUser(id uint, req *UpdateUserRequest) (*models.User, error) {
	var user models.User
	if err := s.db.Where("id = ?", id).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, err
	}

	// 检查邮箱是否已被其他用户使用
	if req.Email != "" && req.Email != user.Email {
		var count int64
		if err := s.db.Model(&models.User{}).Where("email = ? AND id != ?", req.Email, id).Count(&count).Error; err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, errors.New("邮箱已被其他用户使用")
		}
	}

	// 更新用户信息
	updates := map[string]interface{}{}
	if req.Email != "" {
		updates["email"] = req.Email
	}
	if req.Phone != "" {
		updates["phone"] = req.Phone
	}
	if req.RealName != "" {
		updates["real_name"] = req.RealName
	}
	if req.OrganizationID > 0 {
		updates["organization_id"] = req.OrganizationID
	}
	if req.Status != "" {
		updates["status"] = req.Status
	}

	if len(updates) > 0 {
		if err := s.db.Model(&user).Updates(updates).Error; err != nil {
			return nil, err
		}
	}

	// 更新角色
	if req.RoleIDs != nil {
		if err := s.AssignRoles(id, req.RoleIDs); err != nil {
			return nil, err
		}
	}

	// 更新岗位
	if req.PositionIDs != nil {
		if err := s.AssignPositions(id, req.PositionIDs); err != nil {
			return nil, err
		}
	}

	// 重新获取用户信息
	return s.GetUser(id)
}

// DeleteUser 删除用户
func (s *UserService) DeleteUser(id uint) error {
	return s.db.Delete(&models.User{}, id).Error
}

// AssignRoles 分配角色
func (s *UserService) AssignRoles(userID uint, roleIDs []uint) error {
	// 删除现有角色
	if err := s.db.Where("user_id = ?", userID).Delete(&models.UserRole{}).Error; err != nil {
		return err
	}

	// 添加新角色
	for _, roleID := range roleIDs {
		userRole := &models.UserRole{
			UserID: userID,
			RoleID: roleID,
		}
		if err := s.db.Create(userRole).Error; err != nil {
			return err
		}
	}

	return nil
}

// AssignPositions 分配岗位
func (s *UserService) AssignPositions(userID uint, positionIDs []uint) error {
	// 删除现有岗位
	if err := s.db.Where("user_id = ?", userID).Delete(&models.UserPosition{}).Error; err != nil {
		return err
	}

	// 添加新岗位
	for _, positionID := range positionIDs {
		userPosition := &models.UserPosition{
			UserID:     userID,
			PositionID: positionID,
		}
		if err := s.db.Create(userPosition).Error; err != nil {
			return err
		}
	}

	return nil
}

// ResetPassword 重置密码
func (s *UserService) ResetPassword(id uint, newPassword string) error {
	authService := NewAuthService(s.db, "")
	hashedPassword, err := authService.HashPassword(newPassword)
	if err != nil {
		return err
	}

	return s.db.Model(&models.User{}).Where("id = ?", id).Update("password", hashedPassword).Error
}
