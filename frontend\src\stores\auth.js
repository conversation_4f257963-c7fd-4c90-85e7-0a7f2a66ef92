import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login as loginApi, logout as logoutApi, getProfile } from '@/api/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '')
  const user = ref(null)
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)
  const userRoles = computed(() => user.value?.roles || [])
  const isAdmin = computed(() => userRoles.value.includes('admin'))

  // 登录
  const login = async (credentials) => {
    loading.value = true
    try {
      const response = await loginApi(credentials)

      // 响应拦截器已经处理了格式：{ data: { token, user, expires_at }, message: "登录成功" }
      const { token: newToken, user: userInfo } = response.data

      // 存储令牌和用户信息
      token.value = newToken
      user.value = userInfo
      localStorage.setItem('token', newToken)

      return { success: true, data: response.data }
    } catch (error) {
      return {
        success: false,
        message: error.message || '登录失败'
      }
    } finally {
      loading.value = false
    }
  }

  // 退出登录
  const logout = async () => {
    try {
      await logoutApi()
    } catch (error) {
      console.error('退出登录失败:', error)
    } finally {
      // 清除本地状态
      token.value = ''
      user.value = null
      localStorage.removeItem('token')
    }
  }

  // 获取用户信息
  const fetchUserProfile = async () => {
    if (!token.value) return

    try {
      const response = await getProfile()
      // 响应拦截器已经处理了格式：{ data: user, message: "获取成功" }
      user.value = response.data
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，可能是令牌过期，清除登录状态
      if (error.response?.status === 401) {
        await logout()
      }
    }
  }

  // 检查权限
  const hasPermission = (permission) => {
    if (!user.value) return false
    if (isAdmin.value) return true
    return user.value.permissions?.includes(permission) || false
  }

  // 检查角色
  const hasRole = (role) => {
    if (!user.value) return false
    return userRoles.value.includes(role)
  }

  // 初始化时获取用户信息
  if (token.value && !user.value) {
    fetchUserProfile()
  }

  return {
    // 状态
    token,
    user,
    loading,

    // 计算属性
    isAuthenticated,
    userRoles,
    isAdmin,

    // 方法
    login,
    logout,
    fetchUserProfile,
    hasPermission,
    hasRole
  }
})