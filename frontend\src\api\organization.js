import request from './request'

// 获取组织架构树
export const getOrganizationTree = () => {
  return request.get('/organizations/tree')
}

// 获取组织列表
export const getOrganizations = (params) => {
  return request.get('/organizations', { params })
}

// 获取部门列表（别名）
export const getDepartments = (params) => {
  return request.get('/organizations', { params })
}

// 获取单个组织信息
export const getOrganization = (id) => {
  return request.get(`/organizations/${id}`)
}

// 创建组织
export const createOrganization = (data) => {
  return request.post('/organizations', data)
}

// 更新组织
export const updateOrganization = (id, data) => {
  return request.put(`/organizations/${id}`, data)
}

// 删除组织
export const deleteOrganization = (id) => {
  return request.delete(`/organizations/${id}`)
}

// 获取岗位列表
export const getPositions = (params) => {
  return request.get('/positions', { params })
}

// 创建岗位
export const createPosition = (data) => {
  return request.post('/positions', data)
}

// 更新岗位
export const updatePosition = (id, data) => {
  return request.put(`/positions/${id}`, data)
}

// 删除岗位
export const deletePosition = (id) => {
  return request.delete(`/positions/${id}`)
}

// 获取组织下的用户
export const getOrganizationUsers = (id, params) => {
  return request.get(`/organizations/${id}/users`, { params })
}

// 获取组织下的岗位
export const getOrganizationPositions = (id, params) => {
  return request.get(`/organizations/${id}/positions`, { params })
}

// 移动组织
export const moveOrganization = (id, parentId) => {
  return request.put(`/organizations/${id}/move`, { parent_id: parentId })
}

// 批量删除组织
export const batchDeleteOrganizations = (ids) => {
  return request.delete('/organizations/batch', { data: { ids } })
}

// 导入组织架构
export const importOrganizations = (file) => {
  const formData = new FormData()
  formData.append('file', file)

  return request.post('/organizations/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 导出组织架构
export const exportOrganizations = () => {
  return request.get('/organizations/export', {
    responseType: 'blob'
  })
}

// 获取组织统计信息
export const getOrganizationStats = (id) => {
  return request.get(`/organizations/${id}/stats`)
}

// 同步HIS系统组织架构
export const syncHISOrganizations = () => {
  return request.post('/organizations/sync-his')
}
