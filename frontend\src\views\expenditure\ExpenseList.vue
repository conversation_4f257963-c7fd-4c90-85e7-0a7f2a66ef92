<template>
  <div class="expense-list">
    <a-card :bordered="false">
      <template #title>
        <h3>报销管理</h3>
      </template>
      
      <template #extra>
        <a-space>
          <a-button type="primary" @click="goToApply">
            <template #icon><PlusOutlined /></template>
            新建申请
          </a-button>
          <a-button @click="batchExport">
            <template #icon><ExportOutlined /></template>
            批量导出
          </a-button>
        </a-space>
      </template>

      <!-- 筛选区 -->
      <div class="filter-section">
        <a-form layout="inline" :model="filterForm" @finish="handleSearch">
          <a-form-item label="申请编号">
            <a-input v-model:value="filterForm.application_no" placeholder="请输入申请编号" />
          </a-form-item>
          
          <a-form-item label="申请人">
            <a-input v-model:value="filterForm.applicant" placeholder="请输入申请人" />
          </a-form-item>
          
          <a-form-item label="申请部门">
            <a-tree-select
              v-model:value="filterForm.department_id"
              :tree-data="departmentTree"
              :field-names="{ children: 'children', label: 'name', value: 'id' }"
              placeholder="请选择部门"
              allow-clear
              style="width: 200px"
            />
          </a-form-item>
          
          <a-form-item label="报销类型">
            <a-select v-model:value="filterForm.expense_type" placeholder="请选择类型" allow-clear style="width: 150px">
              <a-select-option value="travel">差旅费</a-select-option>
              <a-select-option value="office">办公费</a-select-option>
              <a-select-option value="training">培训费</a-select-option>
              <a-select-option value="entertainment">招待费</a-select-option>
              <a-select-option value="other">其他</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="申请状态">
            <a-select v-model:value="filterForm.status" placeholder="请选择状态" allow-clear style="width: 150px">
              <a-select-option value="draft">草稿</a-select-option>
              <a-select-option value="pending">待审批</a-select-option>
              <a-select-option value="approved">已通过</a-select-option>
              <a-select-option value="rejected">已驳回</a-select-option>
              <a-select-option value="paid">已支付</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="申请时间">
            <a-range-picker
              v-model:value="filterForm.date_range"
              format="YYYY-MM-DD"
              placeholder="请选择时间范围"
            />
          </a-form-item>
          
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon><SearchOutlined /></template>
                查询
              </a-button>
              <a-button @click="resetFilter">
                <template #icon><ReloadOutlined /></template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 统计卡片 -->
      <div class="statistics-section">
        <a-row :gutter="16">
          <a-col :xs="12" :sm="6">
            <a-card size="small">
              <a-statistic
                title="本月申请"
                :value="statistics.month_applications"
                suffix="笔"
                :value-style="{ color: '#1890ff' }"
              />
            </a-card>
          </a-col>
          <a-col :xs="12" :sm="6">
            <a-card size="small">
              <a-statistic
                title="本月金额"
                :value="statistics.month_amount"
                :precision="2"
                suffix="元"
                :value-style="{ color: '#52c41a' }"
              />
            </a-card>
          </a-col>
          <a-col :xs="12" :sm="6">
            <a-card size="small">
              <a-statistic
                title="待审批"
                :value="statistics.pending_count"
                suffix="笔"
                :value-style="{ color: '#faad14' }"
              />
            </a-card>
          </a-col>
          <a-col :xs="12" :sm="6">
            <a-card size="small">
              <a-statistic
                title="待支付"
                :value="statistics.payment_pending"
                suffix="笔"
                :value-style="{ color: '#fa8c16' }"
              />
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 数据表格 -->
      <div class="table-section">
        <a-table
          :columns="columns"
          :data-source="dataSource"
          :pagination="pagination"
          :loading="loading"
          :row-selection="rowSelection"
          row-key="id"
          @change="handleTableChange"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'application_no'">
              <a @click="viewDetail(record)">{{ record.application_no }}</a>
            </template>
            
            <template v-else-if="column.key === 'expense_type'">
              <a-tag :color="getExpenseTypeColor(record.expense_type)">
                {{ getExpenseTypeName(record.expense_type) }}
              </a-tag>
            </template>
            
            <template v-else-if="column.key === 'total_amount'">
              <span class="amount-text">{{ formatMoney(record.total_amount) }}</span>
            </template>
            
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusName(record.status) }}
              </a-tag>
            </template>
            
            <template v-else-if="column.key === 'progress'">
              <a-progress
                :percent="record.approval_progress"
                :stroke-color="getProgressColor(record.approval_progress)"
                size="small"
              />
            </template>
            
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="viewDetail(record)">查看</a>
                <a @click="editApplication(record)" v-if="record.status === 'draft'">编辑</a>
                <a @click="withdrawApplication(record)" v-if="record.status === 'pending'">撤回</a>
                <a @click="copyApplication(record)">复制</a>
                <a-dropdown>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="printApplication(record)">打印</a-menu-item>
                      <a-menu-item @click="exportApplication(record)">导出</a-menu-item>
                      <a-menu-item @click="viewApprovalFlow(record)">审批流程</a-menu-item>
                      <a-menu-item @click="deleteApplication(record)" v-if="record.status === 'draft'">
                        <span style="color: #ff4d4f;">删除</span>
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a>
                    更多 <DownOutlined />
                  </a>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-card>

    <!-- 详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="报销申请详情"
      width="1200px"
      :footer="null"
    >
      <expense-detail
        v-if="detailModalVisible"
        :application-id="selectedApplicationId"
        @close="detailModalVisible = false"
      />
    </a-modal>

    <!-- 审批流程模态框 -->
    <a-modal
      v-model:open="flowModalVisible"
      title="审批流程"
      width="800px"
      :footer="null"
    >
      <approval-flow
        v-if="flowModalVisible"
        :application-id="selectedApplicationId"
      />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  PlusOutlined,
  ExportOutlined,
  SearchOutlined,
  ReloadOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import {
  getExpenseApplications,
  deleteExpenseApplication,
  withdrawExpenseApplication,
  exportExpenseReport,
  getExpenseStatistics
} from '@/api/expenditure'
import { getDepartments } from '@/api/organization'
import ExpenseDetail from './components/ExpenseDetail.vue'
import ApprovalFlow from './components/ApprovalFlow.vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const dataSource = ref([])
const departmentTree = ref([])
const detailModalVisible = ref(false)
const flowModalVisible = ref(false)
const selectedApplicationId = ref(null)
const selectedRowKeys = ref([])

// 筛选表单
const filterForm = reactive({
  application_no: '',
  applicant: '',
  department_id: null,
  expense_type: null,
  status: null,
  date_range: null
})

// 统计数据
const statistics = reactive({
  month_applications: 0,
  month_amount: 0,
  pending_count: 0,
  payment_pending: 0
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  }
}

// 表格列配置
const columns = [
  { title: '申请编号', key: 'application_no', width: 150 },
  { title: '申请人', dataIndex: 'applicant_name', key: 'applicant_name', width: 100 },
  { title: '申请部门', dataIndex: 'department_name', key: 'department_name', width: 120 },
  { title: '报销类型', key: 'expense_type', width: 100 },
  { title: '申请金额', key: 'total_amount', width: 120 },
  { title: '申请时间', dataIndex: 'apply_date', key: 'apply_date', width: 120 },
  { title: '状态', key: 'status', width: 100 },
  { title: '审批进度', key: 'progress', width: 120 },
  { title: '操作', key: 'action', width: 200, fixed: 'right' }
]

// 工具函数
const formatMoney = (amount) => {
  return (amount / 100).toFixed(2) + '元'
}

const getExpenseTypeColor = (type) => {
  const colors = {
    'travel': 'blue',
    'office': 'green',
    'training': 'orange',
    'entertainment': 'purple',
    'other': 'default'
  }
  return colors[type] || 'default'
}

const getExpenseTypeName = (type) => {
  const names = {
    'travel': '差旅费',
    'office': '办公费',
    'training': '培训费',
    'entertainment': '招待费',
    'other': '其他'
  }
  return names[type] || '未知'
}

const getStatusColor = (status) => {
  const colors = {
    'draft': 'default',
    'pending': 'orange',
    'approved': 'green',
    'rejected': 'red',
    'paid': 'blue'
  }
  return colors[status] || 'default'
}

const getStatusName = (status) => {
  const names = {
    'draft': '草稿',
    'pending': '待审批',
    'approved': '已通过',
    'rejected': '已驳回',
    'paid': '已支付'
  }
  return names[status] || '未知'
}

const getProgressColor = (progress) => {
  if (progress >= 100) return '#52c41a'
  if (progress >= 50) return '#1890ff'
  return '#faad14'
}

// 数据加载函数
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      ...filterForm,
      page: pagination.current,
      page_size: pagination.pageSize
    }
    
    if (filterForm.date_range && filterForm.date_range.length === 2) {
      params.start_date = filterForm.date_range[0].format('YYYY-MM-DD')
      params.end_date = filterForm.date_range[1].format('YYYY-MM-DD')
    }
    
    const response = await getExpenseApplications(params)
    dataSource.value = response.data.list || []
    pagination.total = response.data.total || 0
  } catch (error) {
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  try {
    const response = await getExpenseStatistics()
    Object.assign(statistics, response.data)
  } catch (error) {
    message.error('加载统计数据失败')
  }
}

const loadDepartments = async () => {
  try {
    const response = await getDepartments()
    departmentTree.value = response.data || []
  } catch (error) {
    message.error('加载部门数据失败')
  }
}

// 事件处理函数
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const resetFilter = () => {
  Object.assign(filterForm, {
    application_no: '',
    applicant: '',
    department_id: null,
    expense_type: null,
    status: null,
    date_range: null
  })
  pagination.current = 1
  loadData()
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

const goToApply = () => {
  router.push('/expenditure/apply')
}

const viewDetail = (record) => {
  selectedApplicationId.value = record.id
  detailModalVisible.value = true
}

const editApplication = (record) => {
  router.push(`/expenditure/edit/${record.id}`)
}

const withdrawApplication = (record) => {
  Modal.confirm({
    title: '确认撤回',
    content: '确定要撤回这个申请吗？撤回后可以重新编辑提交。',
    onOk: async () => {
      try {
        await withdrawExpenseApplication(record.id)
        message.success('撤回成功')
        loadData()
      } catch (error) {
        message.error('撤回失败')
      }
    }
  })
}

const copyApplication = (record) => {
  router.push(`/expenditure/copy/${record.id}`)
}

const deleteApplication = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这个申请吗？删除后无法恢复。',
    onOk: async () => {
      try {
        await deleteExpenseApplication(record.id)
        message.success('删除成功')
        loadData()
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

const printApplication = (record) => {
  message.info('打印功能开发中...')
}

const exportApplication = (record) => {
  message.info('导出功能开发中...')
}

const viewApprovalFlow = (record) => {
  selectedApplicationId.value = record.id
  flowModalVisible.value = true
}

const batchExport = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要导出的记录')
    return
  }
  
  try {
    const response = await exportExpenseReport('batch', {
      ids: selectedRowKeys.value
    })
    
    // 创建下载链接
    const blob = new Blob([response.data], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `expense_applications_${dayjs().format('YYYY-MM-DD')}.xlsx`
    a.click()
    URL.revokeObjectURL(url)
    
    message.success('导出成功')
  } catch (error) {
    message.error('导出失败')
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
  loadStatistics()
  loadDepartments()
})
</script>

<style scoped>
.expense-list {
  padding: 24px;
}

.filter-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.statistics-section {
  margin-bottom: 24px;
}

.table-section {
  margin-bottom: 24px;
}

.amount-text {
  font-weight: 500;
  color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .expense-list {
    padding: 16px;
  }
  
  .filter-section {
    padding: 12px;
  }
}
</style>
