<template>
  <div class="budget-analysis">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="page-title-section">
            <h1 class="page-title">预算分析</h1>
            <p class="page-subtitle">全面分析预算执行情况和趋势</p>
          </div>
        </div>
        <div class="header-actions">
          <a-select v-model:value="timeRange" style="width: 120px" @change="handleTimeRangeChange">
            <a-select-option value="month">本月</a-select-option>
            <a-select-option value="quarter">本季度</a-select-option>
            <a-select-option value="year">本年度</a-select-option>
          </a-select>
          <a-button @click="exportAnalysis">
            <template #icon><ExportOutlined /></template>
            导出报告
          </a-button>
          <a-button type="primary" @click="refreshData">
            <template #icon><ReloadOutlined /></template>
            刷新数据
          </a-button>
        </div>
      </div>
    </div>

    <!-- 筛选区 -->
    <div class="filter-section">
      <a-card class="filter-card" :bordered="false">
        <a-form layout="inline" :model="filterForm" @finish="handleSearch" class="filter-form">
          <a-form-item label="预算年度">
            <a-select v-model:value="filterForm.year" style="width: 120px">
              <a-select-option :value="2024">2024年</a-select-option>
              <a-select-option :value="2025">2025年</a-select-option>
              <a-select-option :value="2026">2026年</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="部门/科室">
            <a-tree-select
              v-model:value="filterForm.department_ids"
              :tree-data="departmentTree"
              :field-names="{ children: 'children', label: 'name', value: 'id' }"
              multiple
              allow-clear
              placeholder="请选择部门/科室"
              style="width: 300px"
            />
          </a-form-item>

          <a-form-item label="月份范围">
            <a-range-picker
              v-model:value="filterForm.month_range"
              picker="month"
              format="YYYY-MM"
              :placeholder="['开始月份', '结束月份']"
            />
          </a-form-item>

          <a-form-item label="预算类型">
            <a-select v-model:value="filterForm.budget_type" style="width: 150px" allow-clear>
              <a-select-option value="income">收入预算</a-select-option>
              <a-select-option value="expense">支出预算</a-select-option>
              <a-select-option value="investment">投资预算</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon><SearchOutlined /></template>
                查询
              </a-button>
              <a-button @click="resetFilter">
                <template #icon><ReloadOutlined /></template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 核心指标卡片 -->
    <div class="metrics-section">
      <a-row :gutter="[24, 16]">
        <a-col :xs="24" :sm="12" :lg="6">
          <div class="metric-card budget-total">
            <div class="metric-icon">
              <FundOutlined />
            </div>
            <div class="metric-content">
              <div class="metric-value">¥{{ formatNumber(indicators.budget_total * 10000) }}</div>
              <div class="metric-label">预算总额</div>
              <div class="metric-trend">
                <component :is="indicators.budget_trend >= 0 ? ArrowUpOutlined : ArrowDownOutlined"
                          :class="indicators.budget_trend >= 0 ? 'trend-up' : 'trend-down'" />
                <span>较上期 {{ indicators.budget_trend >= 0 ? '+' : '' }}{{ indicators.budget_trend }}%</span>
              </div>
            </div>
          </div>
        </a-col>

        <a-col :xs="24" :sm="12" :lg="6">
          <div class="metric-card budget-used">
            <div class="metric-icon">
              <DollarOutlined />
            </div>
            <div class="metric-content">
              <div class="metric-value">¥{{ formatNumber(indicators.execution_total * 10000) }}</div>
              <div class="metric-label">已使用</div>
              <div class="metric-trend">
                <component :is="indicators.execution_trend >= 0 ? ArrowUpOutlined : ArrowDownOutlined"
                          :class="indicators.execution_trend >= 0 ? 'trend-up' : 'trend-down'" />
                <span>执行率 {{ indicators.execution_rate }}%</span>
              </div>
            </div>
          </div>
        </a-col>

        <a-col :xs="24" :sm="12" :lg="6">
          <div class="metric-card budget-remaining">
            <div class="metric-icon">
              <WalletOutlined />
            </div>
            <div class="metric-content">
              <div class="metric-value">¥{{ formatNumber(indicators.remaining_budget * 10000) }}</div>
              <div class="metric-label">剩余预算</div>
              <div class="metric-trend">
                <ArrowDownOutlined class="trend-down" />
                <span>可用 {{ (100 - indicators.execution_rate).toFixed(1) }}%</span>
              </div>
            </div>
          </div>
        </a-col>

        <a-col :xs="24" :sm="12" :lg="6">
          <div class="metric-card budget-variance">
            <div class="metric-icon">
              <TrendingUpOutlined />
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ getBudgetVariance() > 0 ? '+' : '' }}{{ getBudgetVariance() }}%</div>
              <div class="metric-label">预算偏差</div>
              <div class="metric-trend">
                <component :is="getBudgetVariance() > 0 ? ArrowUpOutlined : ArrowDownOutlined"
                          :class="getBudgetVariance() > 0 ? 'trend-up' : 'trend-down'" />
                <span>{{ getBudgetVariance() > 0 ? '超支' : '节约' }}</span>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 图表分析 -->
    <div class="charts-section">
      <a-row :gutter="[24, 24]">
        <!-- 预算执行趋势 -->
        <a-col :xs="24" :lg="16">
          <a-card class="chart-card" :bordered="false">
            <template #title>
              <div class="card-title">
                <LineChartOutlined class="title-icon" />
                <span>预算执行趋势</span>
              </div>
            </template>
            <template #extra>
              <a-radio-group v-model:value="trendPeriod" size="small" @change="updateTrendChart">
                <a-radio-button value="daily">日</a-radio-button>
                <a-radio-button value="weekly">周</a-radio-button>
                <a-radio-button value="monthly">月</a-radio-button>
              </a-radio-group>
            </template>

            <div class="chart-container">
              <v-chart
                class="trend-chart"
                :option="trendChartOption"
                :loading="chartLoading"
                autoresize
              />
            </div>
          </a-card>
        </a-col>

        <!-- 预算分布 -->
        <a-col :xs="24" :lg="8">
          <a-card class="chart-card" :bordered="false">
            <template #title>
              <div class="card-title">
                <PieChartOutlined class="title-icon" />
                <span>预算分布</span>
              </div>
            </template>

            <div class="chart-container">
              <v-chart
                class="pie-chart"
                :option="departmentChartOption"
                :loading="chartLoading"
                autoresize
              />
            </div>
          </a-card>
        </a-col>

        <!-- 部门预算对比 -->
        <a-col :xs="24" :lg="12">
          <a-card class="chart-card" :bordered="false">
            <template #title>
              <div class="card-title">
                <BarChartOutlined class="title-icon" />
                <span>部门预算对比</span>
              </div>
            </template>
            <template #extra>
              <a-select v-model:value="departmentMetric" size="small" style="width: 100px" @change="updateDepartmentChart">
                <a-select-option value="budget">预算额</a-select-option>
                <a-select-option value="used">已使用</a-select-option>
                <a-select-option value="rate">执行率</a-select-option>
              </a-select>
            </template>

            <div class="chart-container">
              <v-chart
                class="bar-chart"
                :option="typeChartOption"
                :loading="chartLoading"
                autoresize
              />
            </div>
          </a-card>
        </a-col>

        <!-- 预算预警 -->
        <a-col :xs="24" :lg="12">
          <a-card class="chart-card" :bordered="false">
            <template #title>
              <div class="card-title">
                <ExclamationCircleOutlined class="title-icon" />
                <span>预算预警</span>
                <a-badge :count="warningList.length" class="title-badge" />
              </div>
            </template>

            <div class="warning-list">
              <a-list :data-source="warningList" size="small">
                <template #renderItem="{ item }">
                  <a-list-item class="warning-item">
                    <a-list-item-meta>
                      <template #avatar>
                        <a-avatar :style="{ backgroundColor: getWarningColor(item.level) }">
                          <component :is="getWarningIcon(item.level)" />
                        </a-avatar>
                      </template>
                      <template #title>
                        <div class="warning-title">{{ item.title }}</div>
                      </template>
                      <template #description>
                        <div class="warning-desc">
                          <p>{{ item.description }}</p>
                          <div class="warning-meta">
                            <a-tag :color="getWarningColor(item.level)" size="small">
                              {{ getWarningLevelText(item.level) }}
                            </a-tag>
                            <span class="warning-time">{{ formatTime(item.time) }}</span>
                          </div>
                        </div>
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 详细数据表格 -->
    <div class="table-section">
      <a-card class="table-card" :bordered="false">
        <template #title>
          <div class="card-title">
            <TableOutlined class="title-icon" />
            <span>预算明细</span>
          </div>
        </template>
        <template #extra>
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="搜索预算项目..."
            style="width: 200px"
            @search="handleSearch"
          />
        </template>

        <a-table
          :columns="detailColumns"
          :data-source="detailData"
          :loading="tableLoading"
          :pagination="pagination"
          row-key="id"
          @change="handleTableChange"
          class="budget-table"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div class="budget-name">
                <a @click="viewBudgetDetail(record)">{{ record.budget_item }}</a>
                <div class="budget-code">{{ record.budget_code }}</div>
              </div>
            </template>

            <template v-else-if="column.key === 'progress'">
              <div class="progress-cell">
                <a-progress
                  :percent="record.execution_rate"
                  size="small"
                  :stroke-color="getProgressColor(record.execution_rate)"
                />
                <span class="progress-text">{{ record.execution_rate }}%</span>
              </div>
            </template>

            <template v-else-if="column.key === 'status'">
              <a-tag :color="getBudgetStatusColor(record.status)">
                {{ getBudgetStatusText(record.status) }}
              </a-tag>
            </template>

            <template v-else-if="column.key === 'amount'">
              <div class="amount-cell">
                <div class="amount-budget">¥{{ formatMoney(record.budget_amount) }}</div>
                <div class="amount-used">已用: ¥{{ formatMoney(record.execution_amount) }}</div>
              </div>
            </template>

            <template v-else-if="column.key === 'variance'">
              <span :class="record.variance >= 0 ? 'variance-positive' : 'variance-negative'">
                {{ record.variance >= 0 ? '+' : '' }}{{ formatMoney(Math.abs(record.variance)) }}
              </span>
            </template>

            <template v-else-if="column.key === 'action'">
              <a-dropdown>
                <a-button type="text" size="small">
                  操作
                  <DownOutlined />
                </a-button>
                <template #overlay>
                  <a-menu @click="handleAction($event, record)">
                    <a-menu-item key="view">
                      <EyeOutlined />
                      查看详情
                    </a-menu-item>
                    <a-menu-item key="drill">
                      <FundProjectionScreenOutlined />
                      下钻分析
                    </a-menu-item>
                    <a-menu-item key="execution">
                      <UnorderedListOutlined />
                      执行明细
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>
  </div>

    <!-- 执行明细模态框 -->
    <a-modal
      v-model:open="executionModalVisible"
      title="预算执行明细"
      width="1000px"
      :footer="null"
    >
      <a-table
        :columns="executionColumns"
        :data-source="executionDetailData"
        :pagination="false"
        size="small"
        :scroll="{ y: 400 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'amount'">
            {{ formatMoney(record.amount) }}
          </template>
          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusName(record.status) }}
            </a-tag>
          </template>
        </template>
      </a-table>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, PieChart, BarChart } from 'echarts/charts'
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components'
import VChart from 'vue-echarts'
import dayjs from 'dayjs'
import {
  SearchOutlined,
  ReloadOutlined,
  ExportOutlined,
  FundOutlined,
  DollarOutlined,
  WalletOutlined,
  TrendingUpOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  LineChartOutlined,
  PieChartOutlined,
  BarChartOutlined,
  ExclamationCircleOutlined,
  TableOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  EyeOutlined,
  FundProjectionScreenOutlined,
  UnorderedListOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import {
  getBudgetAnalysisData,
  getBudgetTrendData,
  getBudgetExecutionDetail,
  exportBudgetAnalysis
} from '@/api/budget'

const router = useRouter()

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  PieChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

// 响应式数据
const timeRange = ref('month')
const trendPeriod = ref('monthly')
const departmentMetric = ref('budget')
const searchKeyword = ref('')
const departmentTree = ref([])
const detailData = ref([])
const executionDetailData = ref([])
const chartLoading = ref(false)
const tableLoading = ref(false)
const executionModalVisible = ref(false)

// 预警数据
const warningList = ref([
  {
    id: 1,
    title: '内科预算即将超支',
    description: '当前执行率已达95%，预计本月将超支8%',
    level: 'high',
    time: new Date(Date.now() - 30 * 60 * 1000)
  },
  {
    id: 2,
    title: '设备采购预算执行缓慢',
    description: '执行率仅为35%，建议加快采购进度',
    level: 'medium',
    time: new Date(Date.now() - 2 * 60 * 60 * 1000)
  },
  {
    id: 3,
    title: '培训费用预算充足',
    description: '执行率为60%，预算使用正常',
    level: 'low',
    time: new Date(Date.now() - 24 * 60 * 60 * 1000)
  }
])

// 筛选表单
const filterForm = reactive({
  year: new Date().getFullYear(),
  department_ids: [],
  month_range: null,
  budget_type: null
})

// 核心指标
const indicators = reactive({
  budget_total: 0,
  execution_total: 0,
  execution_rate: 0,
  remaining_budget: 0,
  budget_trend: 0,
  execution_trend: 0
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 表格列配置
const detailColumns = [
  {
    title: '预算项目',
    key: 'name',
    width: 200
  },
  {
    title: '负责部门',
    dataIndex: 'department',
    key: 'department',
    width: 120
  },
  {
    title: '预算金额',
    key: 'amount',
    width: 150,
    align: 'right'
  },
  {
    title: '执行进度',
    key: 'progress',
    width: 150
  },
  {
    title: '预算偏差',
    key: 'variance',
    width: 120,
    align: 'right'
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '更新时间',
    dataIndex: 'update_time',
    key: 'update_time',
    width: 120
  },
  {
    title: '操作',
    key: 'action',
    width: 100,
    fixed: 'right'
  }
]

const executionColumns = [
  { title: '执行日期', dataIndex: 'execution_date', key: 'execution_date' },
  { title: '执行项目', dataIndex: 'execution_item', key: 'execution_item' },
  { title: '执行金额', dataIndex: 'execution_amount', key: 'execution_amount', align: 'right' },
  { title: '执行人', dataIndex: 'executor', key: 'executor' },
  { title: '备注', dataIndex: 'remark', key: 'remark' }
]

// 图表配置
const trendChartOption = ref({
  title: {
    text: '预算与执行趋势对比',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    }
  },
  legend: {
    data: ['预算金额', '执行金额'],
    top: 30
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value}万'
    }
  },
  series: [
    {
      name: '预算金额',
      type: 'line',
      data: [],
      itemStyle: { color: '#1890ff' }
    },
    {
      name: '执行金额',
      type: 'line',
      data: [],
      itemStyle: { color: '#52c41a' }
    }
  ]
})

const departmentChartOption = ref({
  title: {
    text: '部门预算分布',
    left: 'center'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c}万 ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    top: 'middle'
  },
  series: [
    {
      name: '预算金额',
      type: 'pie',
      radius: ['40%', '70%'],
      data: []
    }
  ]
})

const typeChartOption = ref({
  title: {
    text: '预算类型分析',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: []
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value}万'
    }
  },
  series: [
    {
      name: '预算金额',
      type: 'bar',
      data: [],
      itemStyle: { color: '#1890ff' }
    }
  ]
})

// 表格列配置
const detailColumns = [
  { title: '部门/科室', dataIndex: 'department_name', key: 'department_name', width: 150 },
  { title: '预算项目', dataIndex: 'budget_item', key: 'budget_item', width: 200 },
  { title: '预算金额', key: 'budget_amount', width: 120 },
  { title: '执行金额', key: 'execution_amount', width: 120 },
  { title: '执行进度', key: 'execution_rate', width: 120 },
  { title: '差异金额', key: 'variance', width: 120 },
  { title: '操作', key: 'action', width: 120, fixed: 'right' }
]

const executionColumns = [
  { title: '单据编号', dataIndex: 'document_no', key: 'document_no' },
  { title: '单据类型', dataIndex: 'document_type', key: 'document_type' },
  { title: '金额', key: 'amount' },
  { title: '申请人', dataIndex: 'applicant', key: 'applicant' },
  { title: '申请时间', dataIndex: 'apply_time', key: 'apply_time' },
  { title: '状态', key: 'status' }
]

// 工具函数
const formatMoney = (amount) => {
  if (!amount) return '0'
  return (amount / 10000).toFixed(2) + '万'
}

const getProgressColor = (rate) => {
  if (rate >= 90) return '#ff4d4f'
  if (rate >= 80) return '#faad14'
  return '#52c41a'
}

const getStatusColor = (status) => {
  const colors = {
    'pending': 'orange',
    'approved': 'green',
    'rejected': 'red'
  }
  return colors[status] || 'default'
}

const getStatusName = (status) => {
  const names = {
    'pending': '待审批',
    'approved': '已通过',
    'rejected': '已驳回'
  }
  return names[status] || '未知'
}

const formatNumber = (num) => {
  return num.toLocaleString('zh-CN')
}

const formatTime = (time) => {
  const now = dayjs()
  const target = dayjs(time)
  const diff = now.diff(target, 'minute')

  if (diff < 60) return `${diff}分钟前`
  if (diff < 1440) return `${Math.floor(diff / 60)}小时前`
  if (diff < 10080) return `${Math.floor(diff / 1440)}天前`
  return target.format('MM-DD HH:mm')
}

const getBudgetVariance = () => {
  const planned = indicators.budget_total
  const actual = indicators.execution_total
  if (planned === 0) return 0
  return ((actual - planned) / planned * 100).toFixed(1)
}

const getBudgetStatusColor = (status) => {
  const colors = {
    'normal': 'green',
    'warning': 'orange',
    'danger': 'red',
    'completed': 'blue'
  }
  return colors[status] || 'default'
}

const getBudgetStatusText = (status) => {
  const texts = {
    'normal': '正常',
    'warning': '预警',
    'danger': '超支',
    'completed': '完成'
  }
  return texts[status] || '未知'
}

const getWarningColor = (level) => {
  const colors = {
    'high': '#ff4d4f',
    'medium': '#faad14',
    'low': '#52c41a'
  }
  return colors[level] || '#1890ff'
}

const getWarningIcon = (level) => {
  const icons = {
    'high': ExclamationCircleOutlined,
    'medium': WarningOutlined,
    'low': InfoCircleOutlined
  }
  return icons[level] || InfoCircleOutlined
}

const getWarningLevelText = (level) => {
  const texts = {
    'high': '高风险',
    'medium': '中风险',
    'low': '低风险'
  }
  return texts[level] || '未知'
}

// 事件处理函数
const handleTimeRangeChange = () => {
  loadAnalysisData()
}

const refreshData = () => {
  loadAnalysisData()
}

const updateTrendChart = () => {
  // 更新趋势图表
  loadAnalysisData()
}

const updateDepartmentChart = () => {
  // 更新部门图表
  loadAnalysisData()
}

const handleSearch = () => {
  loadAnalysisData()
}

const handleAction = ({ key }, record) => {
  switch (key) {
    case 'view':
      viewBudgetDetail(record)
      break
    case 'drill':
      drillDown(record)
      break
    case 'execution':
      viewExecutionDetail(record)
      break
  }
}

const viewBudgetDetail = (record) => {
  router.push(`/budget/detail/${record.id}`)
}

const drillDown = (record) => {
  // 下钻分析逻辑
  message.info('下钻分析功能开发中...')
}

// 数据加载函数
const loadAnalysisData = async () => {
  tableLoading.value = true
  chartLoading.value = true
  
  try {
    const params = {
      ...filterForm,
      page: pagination.current,
      page_size: pagination.pageSize
    }
    
    if (filterForm.month_range && filterForm.month_range.length === 2) {
      params.start_month = filterForm.month_range[0].format('YYYY-MM')
      params.end_month = filterForm.month_range[1].format('YYYY-MM')
    }
    
    const response = await getBudgetAnalysisData(params)
    const data = response.data
    
    // 更新指标
    Object.assign(indicators, data.indicators)
    
    // 更新表格数据
    detailData.value = data.detail_list || []
    pagination.total = data.total || 0
    
    // 更新图表数据
    updateChartData(data.chart_data)
    
  } catch (error) {
    message.error('加载分析数据失败')
  } finally {
    tableLoading.value = false
    chartLoading.value = false
  }
}

const updateChartData = (chartData) => {
  // 更新趋势图
  if (chartData.trend) {
    trendChartOption.value.series[0].data = chartData.trend.budget_data
    trendChartOption.value.series[1].data = chartData.trend.execution_data
  }
  
  // 更新部门分布图
  if (chartData.department) {
    departmentChartOption.value.series[0].data = chartData.department
  }
  
  // 更新类型分析图
  if (chartData.type) {
    typeChartOption.value.xAxis.data = chartData.type.map(item => item.name)
    typeChartOption.value.series[0].data = chartData.type.map(item => item.value)
  }
}

// 事件处理函数
const handleSearch = () => {
  pagination.current = 1
  loadAnalysisData()
}

const resetFilter = () => {
  Object.assign(filterForm, {
    year: new Date().getFullYear(),
    department_ids: [],
    month_range: null,
    budget_type: null
  })
  pagination.current = 1
  loadAnalysisData()
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadAnalysisData()
}

const viewExecutionDetail = async (record) => {
  try {
    const response = await getBudgetExecutionDetail(record.id)
    executionDetailData.value = response.data || []
    executionModalVisible.value = true
  } catch (error) {
    message.error('加载执行明细失败')
  }
}

const viewBudgetDetail = (record) => {
  message.info('查看预算详情功能开发中...')
}

const drillDown = (record) => {
  message.info('下钻分析功能开发中...')
}

const exportAnalysis = async () => {
  try {
    const params = {
      ...filterForm
    }
    
    if (filterForm.month_range && filterForm.month_range.length === 2) {
      params.start_month = filterForm.month_range[0].format('YYYY-MM')
      params.end_month = filterForm.month_range[1].format('YYYY-MM')
    }
    
    const response = await exportBudgetAnalysis(params)
    
    // 创建下载链接
    const blob = new Blob([response.data], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `budget_analysis_${dayjs().format('YYYY-MM-DD')}.xlsx`
    a.click()
    URL.revokeObjectURL(url)
    
    message.success('导出成功')
  } catch (error) {
    message.error('导出失败')
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadAnalysisData()
})
</script>

<style scoped>
/* 预算分析页面 - 企业级设计 */
.budget-analysis {
  background: var(--bg-secondary);
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-active) 100%);
  color: white;
  padding: var(--spacing-2xl) var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.page-title-section {
  flex: 1;
}

.page-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin: 0 0 var(--spacing-sm) 0;
  color: white;
}

.page-subtitle {
  font-size: var(--font-size-md);
  margin: 0;
  opacity: 0.9;
  font-weight: var(--font-weight-normal);
}

.header-actions {
  display: flex;
  gap: var(--spacing-md);
}

/* 筛选区域 */
.filter-section {
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.filter-card {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
}

.filter-form {
  width: 100%;
}

.filter-form :deep(.ant-form-item) {
  margin-bottom: 0;
}

/* 核心指标卡片 */
.metrics-section {
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.metric-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.metric-card.budget-total::before {
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-hover));
}

.metric-card.budget-used::before {
  background: linear-gradient(90deg, #00C853, #4CAF50);
}

.metric-card.budget-remaining::before {
  background: linear-gradient(90deg, #FF9800, #FFB74D);
}

.metric-card.budget-variance::before {
  background: linear-gradient(90deg, #F44336, #E57373);
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.metric-card.budget-total .metric-icon {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-color-hover));
}

.metric-card.budget-used .metric-icon {
  background: linear-gradient(135deg, #00C853, #4CAF50);
}

.metric-card.budget-remaining .metric-icon {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}

.metric-card.budget-variance .metric-icon {
  background: linear-gradient(135deg, #F44336, #E57373);
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-xs);
}

.metric-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-xs);
}

.trend-up {
  color: var(--success-color);
}

.trend-down {
  color: var(--error-color);
}

/* 图表区域 */
.charts-section {
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.chart-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
}

.chart-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.title-icon {
  color: var(--primary-color);
  font-size: 16px;
}

.title-badge {
  margin-left: auto;
}

.chart-container {
  padding: var(--spacing-md);
}

.trend-chart,
.pie-chart,
.bar-chart {
  height: 400px;
  width: 100%;
}

/* 预警列表 */
.warning-list {
  max-height: 400px;
  overflow-y: auto;
  padding: var(--spacing-md);
}

.warning-item {
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--border-light);
}

.warning-item:last-child {
  border-bottom: none;
}

.warning-title {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.warning-desc p {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.warning-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.warning-time {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

/* 表格区域 */
.table-section {
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.table-card {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
}

.budget-table :deep(.ant-table-thead > tr > th) {
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-light);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.budget-table :deep(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid var(--border-light);
}

.budget-table :deep(.ant-table-tbody > tr:hover > td) {
  background: var(--bg-hover);
}

.budget-name a {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
}

.budget-name a:hover {
  text-decoration: underline;
}

.budget-code {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  margin-top: 2px;
}

.progress-cell {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.progress-text {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  text-align: center;
}

.amount-cell {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.amount-budget {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.amount-used {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.variance-positive {
  color: var(--error-color);
  font-weight: var(--font-weight-medium);
}

.variance-negative {
  color: var(--success-color);
  font-weight: var(--font-weight-medium);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .charts-section,
  .table-section {
    padding: 0 var(--spacing-md);
  }

  .trend-chart,
  .pie-chart,
  .bar-chart {
    height: 300px;
  }

  .chart-container {
    padding: var(--spacing-sm);
  }
}

@media (max-width: 576px) {
  .charts-section,
  .table-section {
    padding: 0 var(--spacing-sm);
  }

  .trend-chart,
  .pie-chart,
  .bar-chart {
    height: 250px;
  }
}
</style>
