
---

### **第一部分：详细功能设计**

我们将遵循文档的章节结构，对每个核心模块进行功能拆解，描述其核心流程、控制点和用户界面要素。

#### **1. 基础平台与门户管理 (Platform & Portal)**

这是整个系统的基石，为所有业务模块提供支撑。

*   **1.1 组织与用户管理**
    *   **功能描述：** 统一管理医院的组织架构（院、科、组）、岗位和员工信息。支持与HIS/OA系统的人员信息同步，确保人员信息的唯一性和准确性。
    *   **主要流程：**
        1.  管理员通过界面维护组织架构树。
        2.  可手动新增/编辑/禁用员工，或配置定时任务从HIS/OA同步员工信息。
        3.  员工信息包括基本信息、岗位、职级、关联的成本中心等。
    *   **控制点：** 员工入职/离职/转岗时，权限应自动或半自动地进行变更，防止权限滥用。

*   **1.2 角色与权限中心 (RBAC)**
    *   **功能描述：** 实现“用户-角色-权限”的精细化管理。权限可控制到菜单、页面、按钮级别。
    *   **主要流程：**
        1.  **权限定义：** 系统管理员预定义所有系统的权限点（如“创建报销单”、“审批预算”）。
        2.  **角色创建：** 管理员创建角色（如“科室主任”、“财务审核员”、“采购经办”）。
        3.  **角色授权：** 为每个角色分配相应的权限点。
        4.  **用户分配角色：** 将一个或多个角色分配给用户。
    *   **控制点：** 严格执行不相容岗位分离原则，例如，财务审核员不能拥有报销单申请权限。

*   **1.3 动态工作流引擎**
    *   **功能描述：** 提供图形化界面，让业务管理员无需编码即可设计、修改和部署审批流程。
    *   **主要流程：**
        1.  管理员在流程设计器中通过拖拉拽的方式绘制流程图（开始、审批节点、条件分支、结束）。
        2.  为每个审批节点配置审批人规则（如“申请人直属领导”、“指定角色（财务科长）”、“成本中心负责人”、“按金额判断”）。
        3.  将设计好的流程与具体的业务单据（如“通用报销单”、“合同审批单”）进行绑定。
    *   **控制点：** 所有经济活动的审批必须通过工作流引擎驱动，确保流程的固化和合规，所有审批记录永久保存，可追溯。

*   **1.4 门户管理 (对应文档3.2.2)**
    *   **功能描述：** 提供个性化的工作台，作为用户登录后的首页。
    *   **界面元素描述：**
        *   **顶部导航栏：** 系统Logo、全局搜索框、消息中心、个人信息。
        *   **左侧菜单栏：** 根据用户权限动态生成的菜单树。
        *   **主内容区（可拖拽配置）：**
            *   **待办事项 (To-Do List):** 显示所有待审批的单据，点击可直接进入审批页面。
            *   **快捷入口:** “我要报销”、“我要借款”、“我的合同”等常用功能。
            *   **我的单据:** 快速访问我起草的、我已审批的单据。
            *   **通知公告:** 显示医院内部通知。
            *   **统计图表（管理驾驶舱）：** 面向领导角色，以图表形式展示关键指标，如预算执行进度、科室支出排名等。

#### **2. 全面预算管理系统 (对应文档第二章)**

*   **2.1 目标管理 & 2.2 事业计划**
    *   **功能描述：** 将医院战略目标层层分解，并与具体的业务计划（医疗、人员、设备）相结合，作为预算编制的源头和依据。
    *   **主要流程：**
        1.  院领导在【目标管理】页面录入年度战略目标（如：总收入增长10%，药品占比下降至25%）。
        2.  归口部门（如医务科、人事科）将目标分解为具体的业务计划指标（如：门诊人次目标、手术台次目标、新增床位、人员编制计划）。
        3.  系统根据业务计划和历史收费数据，自动测算并向临床科室下达【收入目标】和【支出控制目标】建议值。
        4.  临床科室在【事业计划上报】模块中，上报本科室的业务、人员、设备购置计划，并提交归口部门审批。
    *   **界面元素：** 目标录入表单、指标分解树状图、计划上报列表及审批界面。

*   **2.3 - 2.5 预算编制、编审与审批**
    *   **功能描述：** 实现自下而上（科室 -> 归口部门）的预算数据归集和自上而下（预算委员会 -> 归口部门）的审批与反馈。
    *   **主要流程：**
        1.  **科室编制：** 科室预算员登录系统，看到系统预置的预算事项（如人员经费、材料费），并基于事业计划和历史数据填报预算金额，提交审批。
        2.  **归口部门编审：** 归口部门（如人事科审核人员经费）看到所有下属科室提交的同类预算，可进行汇总、审核、调整或驳回。编审通过后提交至预算委员会。
        3.  **预算委员会审批：** 预算委员会成员看到全院的预算汇总数据，进行最终审批或驳回。
    *   **控制点：** 流程由工作流引擎驱动，每一步审批都有记录。系统提供版本控制，可追溯预算的修改历史。

*   **2.7 预算分析**
    *   **功能描述：** 提供多维度的、可交互的预算执行分析报表。
    *   **界面元素：**
        *   **数据驾驶舱：** 包含执行进度仪表盘、支出分类饼图、科室执行率排名条形图。
        *   **分析报表：** 提供表格形式的报表，支持按时间（月/季/年）、按部门、按预算项目进行筛选和钻取。点击具体数字可穿透查询到对应的支出单据明细。

#### **3. 支出控制管理系统 (对应文档第三章)**

*   **3.4 事前申请 & 3.5 通用报销**
    *   **功能描述：** 管理所有费用的申请与报销，强制进行预算控制。
    *   **主要流程（报销关联事前申请）：**
        1.  员工发起【事前申请】（如出差申请），填写预估费用并选择预算项目。提交后，系统**冻结**对应预算额度。
        2.  申请经审批通过。
        3.  员工出差回来，发起【通用报销】，选择关联已通过的事前申请单。
        4.  系统自动带入申请信息，员工填写实际发生金额，并上传发票（支持OCR识别）。
        5.  提交报销单审批。审批通过后，系统**解冻**原预算额度，并**扣减**实际报销金额。
    *   **控制点：**
        *   **预算控制：** 提交申请时，系统实时检查预算可用额度。超出部分根据规则进行阻断（刚性）或提示（柔性）。
        *   **标准控制：** 差旅费报销时，系统根据员工职级、出差地，自动校验住宿、交通费用是否超标，超标部分需特殊审批。
        *   **发票查重：** OCR识别后，根据发票代码+号码在发票台账中查重，防止重复报销。

*   **3.8 凭证中心**
    *   **功能描述：** 作为业财融合的关键节点，将前端业务单据转换为标准会计凭证。
    *   **主要流程：**
        1.  管理员预设【凭证生成规则】（如：“差旅费”报销，借：管理费用-差旅费，贷：银行存款）。
        2.  财务人员在凭证中心，筛选已支付完成的报销单。
        3.  点击“生成凭证”，系统根据规则自动生成待处理的会计凭证。
        4.  财务人员审核凭证无误后，点击“推送财务系统”，通过API接口将凭证数据发送至会计系统。
    *   **界面元素：** 规则配置界面、待生成凭证的单据列表、已生成凭证列表（含凭证预览）。

---

### **第二部分：架构与图表绘制**

#### **1. 系统分层架构图**

```mermaid
graph TD
    subgraph 用户端 (Client Tier)
        A[Web浏览器 - Vue3/AntD]
        B[移动端 - Uni-app (企业微信小程序/APP)]
    end

    subgraph 网关与负载均衡 (Gateway & LB)
        C[Nginx/SLB] --> D{API Gateway (Go)}
    end

    subgraph 后端微服务 (Service Tier - Go/Gin)
        D --> S1[基础平台服务]
        D --> S2[预算管理服务]
        D --> S3[支出控制服务]
        D --> S4[采购管理服务]
        D --> S5[合同管理服务]
        D --> S6[通知服务]
        D --> S7[集成服务]
    end

    subgraph 数据与支撑层 (Data & Support Tier)
        S1 --> DB[(PostgreSQL - 主库)]
        S2 --> DB
        S3 --> DB
        S4 --> DB
        S5 --> DB
      
        S1 --> Cache[(Redis)]
        S2 --> Cache
        S3 --> Cache
      
        S6 --> MQ([Message Queue - RabbitMQ])
        S7 --> MQ

        S3 --> OS([对象存储 - MinIO])
        S4 --> OS
        S5 --> OS
      
        S7 <--> Ext1[HIS系统]
        S7 <--> Ext2[财务会计系统]
        S7 <--> Ext3[OA系统]
    end

    A --> C
    B --> C
  
    style S1 fill:#cce5ff,stroke:#333,stroke-width:2px
    style S2 fill:#cce5ff,stroke:#333,stroke-width:2px
    style S3 fill:#cce5ff,stroke:#333,stroke-width:2px
    style S4 fill:#cce5ff,stroke:#333,stroke-width:2px
    style S5 fill:#cce5ff,stroke:#333,stroke-width:2px
    style S6 fill:#cce5ff,stroke:#333,stroke-width:2px
    style S7 fill:#cce5ff,stroke:#333,stroke-width:2px
```

#### **2. 核心业务流程图：采购到支付全流程**

```mermaid
sequenceDiagram
    participant U as 经办人
    participant FE as 前端应用
    participant GW as API网关
    participant PS as 采购服务
    participant BS as 预算服务
    participant ES as 支出服务
    participant DB as 数据库

    U->>FE: 1. 提交采购申请（含金额、明细）
    FE->>GW: 2. 发送API请求
    GW->>PS: 3. 路由到采购服务
    PS->>BS: 4. 请求校验预算
    BS-->>DB: 5. 查询预算可用额度
    DB-->>BS: 6. 返回额度
    BS-->>PS: 7. 预算充足，冻结额度并返回成功
    PS-->>DB: 8. 创建采购申请单，状态“待审批”
    DB-->>PS: 9. 返回成功
    PS->>GW: 10. 响应成功
    GW->>FE: 11. 响应成功
    FE->>U: 12. 提示提交成功，进入审批流程
  
    Note right of U: ...采购审批、招标、签订合同、验收...

    U->>FE: 13. 基于已验收的采购单，发起付款申请
    FE->>GW: 14. 发送API请求
    GW->>ES: 15. 路由到支出服务
    ES->>PS: 16. 查询关联的采购单与合同信息
    PS-->>ES: 17. 返回信息
    ES-->>DB: 18. 创建付款单，状态“待审批”
    DB-->>ES: 19. 返回成功
    ES->>GW: 20. 响应成功
    GW->>FE: 21. 响应成功
    FE->>U: 22. 提示付款申请提交成功

    Note right of U: ...付款审批流程...

    participant Fin as 财务人员
    Fin->>FE: 23. 审批通过付款单
    FE->>GW: 24. 发送API请求
    GW->>ES: 25. 路由到支出服务
    ES-->>DB: 26. 更新付款单状态为“审批通过，待支付”
    ES->>BS: 27. 请求正式扣减预算
    BS-->>DB: 28. 解冻额度，扣减额度
    BS-->>ES: 29. 返回成功
    ES->>FE: 30. 响应成功

    Note right of Fin: ...出纳支付，生成凭证...
```

---

### **第三部分：数据库设计深化**

扩展核心模块的表设计，增加关键字段和说明。

**`expenditure_applications` (支出申请/报销单主表)**

| 字段名                    | 类型                  | 约束            | 备注                                           |
| ------------------------- | --------------------- | --------------- | ---------------------------------------------- |
| `id`                      | `bigserial`           | `PRIMARY KEY`   | 自增主键                                       |
| `application_no`          | `varchar(64)`         | `UNIQUE, NOT NULL` | 单据编号，按规则生成                           |
| `applicant_id`            | `bigint`              | `NOT NULL`      | 申请人ID，关联 `users` 表                    |
| `department_id`           | `bigint`              | `NOT NULL`      | 申请人所在部门ID                               |
| `title`                   | `varchar(255)`        | `NOT NULL`      | 报销事由                                       |
| `type`                    | `smallint`            | `NOT NULL`      | 单据类型 (1:通用报销, 2:差旅报销, 3:借款)  |
| `total_amount`            | `numeric(18, 2)`      | `NOT NULL`      | 总金额                                         |
| `status`                  | `varchar(32)`         | `NOT NULL`      | 单据状态 (draft, pending, approved, rejected, paid, closed) |
| `workflow_instance_id`    | `varchar(64)`         |                 | 关联的工作流实例ID                             |
| `related_application_id`  | `bigint`              |                 | 关联的事前申请单ID                             |
| `created_at`              | `timestamp with time zone` | `NOT NULL`      | 创建时间                                       |
| `updated_at`              | `timestamp with time zone` | `NOT NULL`      | 更新时间                                       |

**`application_budget_links` (支出与预算关联表)**

| 字段名          | 类型             | 约束          | 备注                                                         |
| --------------- | ---------------- | ------------- | ------------------------------------------------------------ |
| `id`            | `bigserial`      | `PRIMARY KEY` | 自增主键                                                     |
| `application_id`| `bigint`         | `NOT NULL`    | 关联的支出单ID                                               |
| `budget_item_id`| `bigint`         | `NOT NULL`    | 关联的预算指标ID                                             |
| `amount`        | `numeric(18, 2)` | `NOT NULL`    | 从该预算指标中占用的金额                                     |
| `link_type`     | `smallint`       | `NOT NULL`    | 关联类型 (1: 冻结, 2: 扣减)                                |

**`audit_logs` (审计日志表)**

| 字段名          | 类型                  | 约束          | 备注                                                         |
| --------------- | --------------------- | ------------- | ------------------------------------------------------------ |
| `id`            | `bigserial`           | `PRIMARY KEY` | 自增主键                                                     |
| `user_id`       | `bigint`              |               | 操作用户ID                                                   |
| `action`        | `varchar(255)`        | `NOT NULL`    | 操作描述 (如: "创建报销单", "审批通过合同")                |
| `target_id`     | `varchar(64)`         |               | 操作对象的ID (如: 报销单号)                                |
| `target_type`   | `varchar(64)`         |               | 操作对象的类型 (如: "ExpenditureApplication")              |
| `old_value`     | `jsonb`               |               | 变更前的数据 (JSON格式)                                    |
| `new_value`     | `jsonb`               |               | 变更后的数据 (JSON格式)                                    |
| `ip_address`    | `varchar(64)`         |               | 操作者IP地址                                                 |
| `created_at`    | `timestamp with time zone` | `NOT NULL`    | 操作时间                                                     |

---

### **第四部分：技术栈实施要点**

*   **后端Go实施要点：**
    *   **统一的API响应结构：** 所有API接口返回统一的JSON格式，如 `{ "code": 0, "message": "success", "data": {...} }`，便于前端统一处理。
    *   **中间件 (Middleware):** 使用Gin的中间件实现通用功能，如：
        *   **认证中间件：** 解析JWT，将用户信息注入到请求上下文中。
        *   **权限中间件：** 从上下文中获取用户信息，并检查其是否有权访问当前API。
        *   **日志中间件：** 记录每个请求的详细信息。
        *   **异常恢复中间件 (Recovery):** 捕获panic，防止程序崩溃，并返回统一的错误响应。
    *   **服务间通信：** 优先采用轻量级的HTTP RESTful API进行同步调用。对于需要解耦和异步处理的场景（如通知、日志），通过RabbitMQ进行通信。

*   **前端Vue3实施要点：**
    *   **组件化开发：**
        *   **业务组件：** 封装特定业务逻辑，如 `BudgetSelector` (预算选择器)、`ApprovalFlowViewer` (审批流查看器)。
        *   **基础组件：** 封装通用功能，如 `FileUpload` (附件上传，集成对象存储)、`DataGrid` (数据表格，集成查询、分页)。
    *   **API服务层：** 创建`services`目录，按模块组织API请求函数，所有与后端的交互都在此层完成，使组件逻辑更纯粹。
    *   **路由守卫 (Navigation Guards):** 在`Vue Router`中设置全局前置守卫，用于检查用户登录状态和页面访问权限，未登录则跳转登录页，无权限则显示403页面。

*   **安全设计强化：**
    *   **防SQL注入：** 使用GORM等ORM框架，避免手动拼接SQL。
    *   **防XSS攻击：** Vue框架本身有较好的防XSS机制，但对用户输入的富文本内容（如合同备注）需使用专门的库（如`DOMPurify`）进行过滤。
    *   **数据传输加密：** 全站强制使用HTTPS。
    *   **密码存储：** 用户密码在数据库中必须使用强哈希算法（如bcrypt）加盐存储，绝不存明文。
    *   **审计日志：** 对所有关键操作（登录、审批、修改金额、权限变更等）记录详细的审计日志，存入`audit_logs`表，确保所有行为可追溯。



