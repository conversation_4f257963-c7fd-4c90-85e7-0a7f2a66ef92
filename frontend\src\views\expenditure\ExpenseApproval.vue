<template>
  <div class="expense-approval">
    <a-card :bordered="false">
      <template #title>
        <h3>报销审批</h3>
      </template>

      <!-- 筛选区 -->
      <div class="filter-section">
        <a-form layout="inline" :model="filterForm" @finish="handleSearch">
          <a-form-item label="申请编号">
            <a-input v-model:value="filterForm.application_no" placeholder="请输入申请编号" />
          </a-form-item>
          
          <a-form-item label="申请人">
            <a-input v-model:value="filterForm.applicant" placeholder="请输入申请人" />
          </a-form-item>
          
          <a-form-item label="申请部门">
            <a-tree-select
              v-model:value="filterForm.department_id"
              :tree-data="departmentTree"
              :field-names="{ children: 'children', label: 'name', value: 'id' }"
              placeholder="请选择部门"
              allow-clear
              style="width: 200px"
            />
          </a-form-item>
          
          <a-form-item label="报销类型">
            <a-select v-model:value="filterForm.expense_type" placeholder="请选择类型" allow-clear style="width: 150px">
              <a-select-option value="travel">差旅费</a-select-option>
              <a-select-option value="office">办公费</a-select-option>
              <a-select-option value="training">培训费</a-select-option>
              <a-select-option value="entertainment">招待费</a-select-option>
              <a-select-option value="other">其他</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon><SearchOutlined /></template>
                查询
              </a-button>
              <a-button @click="resetFilter">
                <template #icon><ReloadOutlined /></template>
                重置
              </a-button>
              <a-button @click="batchApprove" :disabled="selectedRowKeys.length === 0">
                <template #icon><CheckOutlined /></template>
                批量审批
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 统计卡片 -->
      <div class="statistics-section">
        <a-row :gutter="16">
          <a-col :xs="12" :sm="6">
            <a-card size="small">
              <a-statistic
                title="待审批"
                :value="statistics.pending_count"
                suffix="笔"
                :value-style="{ color: '#faad14' }"
              />
            </a-card>
          </a-col>
          <a-col :xs="12" :sm="6">
            <a-card size="small">
              <a-statistic
                title="待审批金额"
                :value="statistics.pending_amount"
                :precision="2"
                suffix="元"
                :value-style="{ color: '#fa8c16' }"
              />
            </a-card>
          </a-col>
          <a-col :xs="12" :sm="6">
            <a-card size="small">
              <a-statistic
                title="今日已审批"
                :value="statistics.today_approved"
                suffix="笔"
                :value-style="{ color: '#52c41a' }"
              />
            </a-card>
          </a-col>
          <a-col :xs="12" :sm="6">
            <a-card size="small">
              <a-statistic
                title="本月已审批"
                :value="statistics.month_approved"
                suffix="笔"
                :value-style="{ color: '#1890ff' }"
              />
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 数据表格 -->
      <div class="table-section">
        <a-table
          :columns="columns"
          :data-source="dataSource"
          :pagination="pagination"
          :loading="loading"
          :row-selection="rowSelection"
          row-key="id"
          @change="handleTableChange"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'application_no'">
              <a @click="viewDetail(record)">{{ record.application_no }}</a>
            </template>
            
            <template v-else-if="column.key === 'expense_type'">
              <a-tag :color="getExpenseTypeColor(record.expense_type)">
                {{ getExpenseTypeName(record.expense_type) }}
              </a-tag>
            </template>
            
            <template v-else-if="column.key === 'total_amount'">
              <span class="amount-text">{{ formatMoney(record.total_amount) }}</span>
            </template>
            
            <template v-else-if="column.key === 'urgency'">
              <a-tag :color="getUrgencyColor(record.urgency)">
                {{ getUrgencyName(record.urgency) }}
              </a-tag>
            </template>
            
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="viewDetail(record)">查看</a>
                <a @click="approveApplication(record, 'approve')" style="color: #52c41a;">通过</a>
                <a @click="approveApplication(record, 'reject')" style="color: #ff4d4f;">驳回</a>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-card>

    <!-- 详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="报销申请详情"
      width="1200px"
      :footer="null"
    >
      <expense-detail
        v-if="detailModalVisible"
        :application-id="selectedApplicationId"
        @close="detailModalVisible = false"
        @approve="handleApprove"
      />
    </a-modal>

    <!-- 审批模态框 -->
    <a-modal
      v-model:open="approvalModalVisible"
      title="审批意见"
      @ok="handleApprovalSubmit"
      @cancel="approvalModalVisible = false"
    >
      <a-form layout="vertical">
        <a-form-item label="审批结果">
          <a-radio-group v-model:value="approvalForm.action">
            <a-radio value="approve" style="color: #52c41a;">通过</a-radio>
            <a-radio value="reject" style="color: #ff4d4f;">驳回</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item label="审批意见">
          <a-textarea
            v-model:value="approvalForm.comment"
            placeholder="请输入审批意见"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 批量审批模态框 -->
    <a-modal
      v-model:open="batchApprovalModalVisible"
      title="批量审批"
      @ok="handleBatchApprovalSubmit"
      @cancel="batchApprovalModalVisible = false"
    >
      <a-form layout="vertical">
        <a-form-item label="审批结果">
          <a-radio-group v-model:value="batchApprovalForm.action">
            <a-radio value="approve" style="color: #52c41a;">批量通过</a-radio>
            <a-radio value="reject" style="color: #ff4d4f;">批量驳回</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item label="审批意见">
          <a-textarea
            v-model:value="batchApprovalForm.comment"
            placeholder="请输入审批意见"
            :rows="4"
          />
        </a-form-item>
        
        <a-form-item label="选中的申请">
          <div class="selected-applications">
            <a-tag v-for="app in selectedApplications" :key="app.id" closable @close="removeFromSelection(app.id)">
              {{ app.application_no }} - {{ formatMoney(app.total_amount) }}
            </a-tag>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  CheckOutlined
} from '@ant-design/icons-vue'
import {
  getPendingApprovals,
  approveExpenditureApplication,
  batchApproveApplications,
  getExpenditureStatistics
} from '@/api/expenditure'
import { getDepartments } from '@/api/organization'
import ExpenseDetail from './components/ExpenseDetail.vue'

// 响应式数据
const loading = ref(false)
const dataSource = ref([])
const departmentTree = ref([])
const detailModalVisible = ref(false)
const approvalModalVisible = ref(false)
const batchApprovalModalVisible = ref(false)
const selectedApplicationId = ref(null)
const selectedRowKeys = ref([])
const currentApprovalRecord = ref(null)

// 筛选表单
const filterForm = reactive({
  application_no: '',
  applicant: '',
  department_id: null,
  expense_type: null
})

// 统计数据
const statistics = reactive({
  pending_count: 0,
  pending_amount: 0,
  today_approved: 0,
  month_approved: 0
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 审批表单
const approvalForm = reactive({
  action: 'approve',
  comment: ''
})

// 批量审批表单
const batchApprovalForm = reactive({
  action: 'approve',
  comment: ''
})

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  }
}

// 计算属性
const selectedApplications = computed(() => {
  return dataSource.value.filter(app => selectedRowKeys.value.includes(app.id))
})

// 表格列配置
const columns = [
  { title: '申请编号', key: 'application_no', width: 150 },
  { title: '申请人', dataIndex: 'applicant_name', key: 'applicant_name', width: 100 },
  { title: '申请部门', dataIndex: 'department_name', key: 'department_name', width: 120 },
  { title: '报销类型', key: 'expense_type', width: 100 },
  { title: '申请金额', key: 'total_amount', width: 120 },
  { title: '申请时间', dataIndex: 'apply_date', key: 'apply_date', width: 120 },
  { title: '紧急程度', key: 'urgency', width: 100 },
  { title: '操作', key: 'action', width: 200, fixed: 'right' }
]

// 工具函数
const formatMoney = (amount) => {
  return (amount / 100).toFixed(2) + '元'
}

const getExpenseTypeColor = (type) => {
  const colors = {
    'travel': 'blue',
    'office': 'green',
    'training': 'orange',
    'entertainment': 'purple',
    'other': 'default'
  }
  return colors[type] || 'default'
}

const getExpenseTypeName = (type) => {
  const names = {
    'travel': '差旅费',
    'office': '办公费',
    'training': '培训费',
    'entertainment': '招待费',
    'other': '其他'
  }
  return names[type] || '未知'
}

const getUrgencyColor = (urgency) => {
  const colors = {
    'low': 'default',
    'normal': 'blue',
    'high': 'orange',
    'urgent': 'red'
  }
  return colors[urgency] || 'default'
}

const getUrgencyName = (urgency) => {
  const names = {
    'low': '低',
    'normal': '普通',
    'high': '高',
    'urgent': '紧急'
  }
  return names[urgency] || '普通'
}

// 数据加载函数
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      ...filterForm,
      page: pagination.current,
      page_size: pagination.pageSize
    }
    
    const response = await getPendingApprovals(params)
    dataSource.value = response.data.list || []
    pagination.total = response.data.total || 0
  } catch (error) {
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  try {
    const response = await getExpenditureStatistics({ type: 'approval' })
    Object.assign(statistics, response.data)
  } catch (error) {
    message.error('加载统计数据失败')
  }
}

const loadDepartments = async () => {
  try {
    const response = await getDepartments()
    departmentTree.value = response.data || []
  } catch (error) {
    message.error('加载部门数据失败')
  }
}

// 事件处理函数
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const resetFilter = () => {
  Object.assign(filterForm, {
    application_no: '',
    applicant: '',
    department_id: null,
    expense_type: null
  })
  pagination.current = 1
  loadData()
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

const viewDetail = (record) => {
  selectedApplicationId.value = record.id
  detailModalVisible.value = true
}

const approveApplication = (record, action) => {
  currentApprovalRecord.value = record
  approvalForm.action = action
  approvalForm.comment = ''
  approvalModalVisible.value = true
}

const handleApprovalSubmit = async () => {
  try {
    await approveExpenditureApplication(currentApprovalRecord.value.id, approvalForm)
    message.success('审批成功')
    approvalModalVisible.value = false
    loadData()
    loadStatistics()
  } catch (error) {
    message.error('审批失败')
  }
}

const batchApprove = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要审批的申请')
    return
  }
  batchApprovalForm.action = 'approve'
  batchApprovalForm.comment = ''
  batchApprovalModalVisible.value = true
}

const handleBatchApprovalSubmit = async () => {
  try {
    await batchApproveApplications({
      application_ids: selectedRowKeys.value,
      action: batchApprovalForm.action,
      comment: batchApprovalForm.comment
    })
    message.success('批量审批成功')
    batchApprovalModalVisible.value = false
    selectedRowKeys.value = []
    loadData()
    loadStatistics()
  } catch (error) {
    message.error('批量审批失败')
  }
}

const removeFromSelection = (id) => {
  selectedRowKeys.value = selectedRowKeys.value.filter(key => key !== id)
}

const handleApprove = (applicationId, action, comment) => {
  // 从详情页面触发的审批
  approveExpenditureApplication(applicationId, { action, comment }).then(() => {
    message.success('审批成功')
    detailModalVisible.value = false
    loadData()
    loadStatistics()
  }).catch(() => {
    message.error('审批失败')
  })
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
  loadStatistics()
  loadDepartments()
})
</script>

<style scoped>
.expense-approval {
  padding: 24px;
}

.filter-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.statistics-section {
  margin-bottom: 24px;
}

.table-section {
  margin-bottom: 24px;
}

.amount-text {
  font-weight: 500;
  color: #1890ff;
}

.selected-applications {
  max-height: 200px;
  overflow-y: auto;
}

.selected-applications .ant-tag {
  margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .expense-approval {
    padding: 16px;
  }
  
  .filter-section {
    padding: 12px;
  }
}
</style>
