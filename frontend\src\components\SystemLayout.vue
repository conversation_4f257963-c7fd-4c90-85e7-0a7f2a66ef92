<template>
  <a-layout class="system-layout">
    <!-- 顶部导航栏 - 企业级专业设计 -->
    <a-layout-header class="header">
      <div class="header-left">
        <div class="logo-container">
          <div class="logo-icon">
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
              <rect width="32" height="32" rx="8" fill="url(#gradient)"/>
              <path d="M8 12h16v2H8v-2zm0 4h16v2H8v-2zm0 4h12v2H8v-2z" fill="white"/>
              <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#1664FF"/>
                  <stop offset="100%" style="stop-color:#0D47A1"/>
                </linearGradient>
              </defs>
            </svg>
          </div>
          <div class="logo-text">
            <h1 class="system-title">医院运营管理系统</h1>
          </div>
        </div>

        <!-- 菜单折叠按钮 -->
        <a-button
          type="text"
          class="menu-toggle"
          :icon="collapsed ? h(MenuUnfoldOutlined) : h(MenuFoldOutlined)"
          @click="collapsed = !collapsed"
        />
      </div>

      <div class="header-center">
        <!-- 面包屑导航 -->
        <a-breadcrumb class="breadcrumb-nav">
          <a-breadcrumb-item>
            <HomeOutlined />
            <router-link to="/">首页</router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item v-for="item in breadcrumbItems" :key="item.path">
            <router-link v-if="item.path" :to="item.path">{{ item.title }}</router-link>
            <span v-else>{{ item.title }}</span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>

      <div class="header-right">
        <!-- 搜索框 -->
        <div class="search-container">
          <a-input-search
            v-model:value="searchValue"
            placeholder="搜索功能、单据、用户..."
            class="global-search"
            :loading="searchLoading"
            @search="onSearch"
          />
        </div>

        <!-- 操作按钮组 -->
        <div class="header-actions">
          <!-- 新建报销 -->
          <a-tooltip title="新建报销">
            <a-button type="text" class="header-btn" @click="quickAction('expense')">
              <PlusOutlined />
            </a-button>
          </a-tooltip>

          <!-- 通知中心 -->
          <a-tooltip title="通知中心">
            <a-badge :count="unreadCount" :offset="[-2, 2]">
              <a-button type="text" class="header-btn" @click="showNotifications">
                <BellOutlined />
              </a-button>
            </a-badge>
          </a-tooltip>

          <!-- 帮助中心 -->
          <a-tooltip title="帮助中心">
            <a-button type="text" class="header-btn" @click="showHelp">
              <QuestionCircleOutlined />
            </a-button>
          </a-tooltip>
        </div>

        <!-- 用户信息 -->
        <a-dropdown placement="bottomRight" :trigger="['click']">
          <div class="user-profile">
            <a-avatar :size="32" class="user-avatar">
              <template #icon>
                <UserOutlined />
              </template>
            </a-avatar>
            <div class="user-info">
              <div class="user-name">{{ user?.real_name || '用户' }}</div>
              <div class="user-role">{{ user?.role_name || '普通用户' }}</div>
            </div>
            <DownOutlined class="dropdown-icon" />
          </div>
          <template #overlay>
            <a-menu class="user-menu">
              <a-menu-item key="profile">
                <UserOutlined />
                <span>个人信息</span>
              </a-menu-item>
              <a-menu-item key="settings">
                <SettingOutlined />
                <span>偏好设置</span>
              </a-menu-item>
              <a-menu-item key="security">
                <SafetyOutlined />
                <span>安全设置</span>
              </a-menu-item>
              <a-menu-divider />
              <a-menu-item key="logout" @click="handleLogout" class="logout-item">
                <LogoutOutlined />
                <span>退出登录</span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </a-layout-header>

    <a-layout class="main-layout">
      <!-- 左侧菜单栏 - 专业导航设计 -->
      <a-layout-sider
        v-model:collapsed="collapsed"
        :trigger="null"
        collapsible
        width="280"
        collapsed-width="64"
        class="sidebar"
      >
        <!-- 菜单容器 -->
        <div class="menu-container">
          <a-menu
            v-model:selectedKeys="selectedKeys"
            v-model:openKeys="openKeys"
            mode="inline"
            theme="light"
            :inline-collapsed="collapsed"
            class="main-menu"
            @click="handleMenuClick"
          >
            <!-- 工作台 -->
            <a-menu-item key="dashboard">
              <template #icon><DashboardOutlined /></template>
              <span>工作台</span>
            </a-menu-item>

            <!-- 预算管理模块 -->
            <a-sub-menu key="budget">
              <template #icon><FundOutlined /></template>
              <template #title>预算管理</template>
              <a-menu-item key="budget-plan">
                <template #icon><EditOutlined /></template>
                <span>预算编制</span>
              </a-menu-item>
              <a-menu-item key="budget-analysis">
                <template #icon><BarChartOutlined /></template>
                <span>预算分析</span>
              </a-menu-item>
              <a-menu-item key="budget-monitor">
                <template #icon><EyeOutlined /></template>
                <span>预算监控</span>
              </a-menu-item>
              <a-menu-item key="budget-adjustment">
                <template #icon><SwapOutlined /></template>
                <span>预算调整</span>
              </a-menu-item>
            </a-sub-menu>

            <!-- 支出控制模块 -->
            <a-sub-menu key="expenditure">
              <template #icon><AccountBookOutlined /></template>
              <template #title>支出控制</template>
              <a-menu-item key="expense-apply">
                <template #icon><PlusCircleOutlined /></template>
                <span>报销申请</span>
              </a-menu-item>
              <a-menu-item key="expense-list">
                <template #icon><UnorderedListOutlined /></template>
                <span>报销管理</span>
              </a-menu-item>
              <a-menu-item key="expense-approve">
                <template #icon><CheckCircleOutlined /></template>
                <span>报销审批</span>
              </a-menu-item>
              <a-menu-item key="expense-payment">
                <template #icon><CreditCardOutlined /></template>
                <span>支付管理</span>
              </a-menu-item>
              <a-menu-item key="expense-analysis">
                <template #icon><LineChartOutlined /></template>
                <span>费用分析</span>
              </a-menu-item>
            </a-sub-menu>

            <!-- 合同管理模块 -->
            <a-sub-menu key="contract">
              <template #icon><FileTextOutlined /></template>
              <template #title>合同管理</template>
              <a-menu-item key="contract-create">
                <template #icon><PlusOutlined /></template>
                <span>合同创建</span>
              </a-menu-item>
              <a-menu-item key="contract-list">
                <template #icon><FolderOutlined /></template>
                <span>合同列表</span>
              </a-menu-item>
              <a-menu-item key="contract-approve">
                <template #icon><AuditOutlined /></template>
                <span>合同审批</span>
              </a-menu-item>
              <a-menu-item key="contract-execute">
                <template #icon><PlayCircleOutlined /></template>
                <span>合同执行</span>
              </a-menu-item>
            </a-sub-menu>

            <!-- 采购管理模块 -->
            <a-sub-menu key="procurement">
              <template #icon><ShoppingCartOutlined /></template>
              <template #title>采购管理</template>
              <a-menu-item key="procurement-plan">
                <template #icon><CalendarOutlined /></template>
                <span>采购计划</span>
              </a-menu-item>
              <a-menu-item key="procurement-apply">
                <template #icon><FormOutlined /></template>
                <span>采购申请</span>
              </a-menu-item>
              <a-menu-item key="procurement-approve">
                <template #icon><CheckSquareOutlined /></template>
                <span>采购审批</span>
              </a-menu-item>
              <a-menu-item key="procurement-execute">
                <template #icon><CarOutlined /></template>
                <span>采购执行</span>
              </a-menu-item>
            </a-sub-menu>

            <!-- 财务管理模块 -->
            <a-sub-menu key="finance">
              <template #icon><CalculatorOutlined /></template>
              <template #title>财务管理</template>
              <a-menu-item key="voucher-rules">
                <template #icon><FileProtectOutlined /></template>
                <span>凭证生成规则</span>
              </a-menu-item>
              <a-menu-item key="voucher-generate">
                <template #icon><FileAddOutlined /></template>
                <span>凭证生成</span>
              </a-menu-item>
              <a-menu-item key="account-book">
                <template #icon><BookOutlined /></template>
                <span>账簿管理</span>
              </a-menu-item>
              <a-menu-item key="financial-report">
                <template #icon><FileTextOutlined /></template>
                <span>财务报表</span>
              </a-menu-item>
            </a-sub-menu>

            <!-- 系统管理模块 -->
            <a-sub-menu key="system" v-if="isAdmin">
              <template #icon><SettingOutlined /></template>
              <template #title>系统管理</template>
              <a-menu-item key="user-manage">
                <template #icon><TeamOutlined /></template>
                <span>用户管理</span>
              </a-menu-item>
              <a-menu-item key="role-manage">
                <template #icon><CrownOutlined /></template>
                <span>角色管理</span>
              </a-menu-item>
              <a-menu-item key="permission-manage">
                <template #icon><KeyOutlined /></template>
                <span>权限管理</span>
              </a-menu-item>
              <a-menu-item key="org-manage">
                <template #icon><ApartmentOutlined /></template>
                <span>组织管理</span>
              </a-menu-item>
              <a-menu-item key="system-config">
                <template #icon><ControlOutlined /></template>
                <span>系统配置</span>
              </a-menu-item>
            </a-sub-menu>
          </a-menu>
        </div>

        <!-- 侧边栏底部信息 -->
        <div v-if="!collapsed" class="sidebar-footer">
          <div class="system-info">
            <div class="version-info">
              <span class="version-label">版本</span>
              <span class="version-number">v2.1.0</span>
            </div>
            <div class="online-status">
              <a-badge status="processing" />
              <span>系统正常</span>
            </div>
          </div>
        </div>
      </a-layout-sider>

      <!-- 主内容区 - 现代化内容布局 -->
      <a-layout-content class="main-content">
        <!-- 页面内容容器 -->
        <div class="content-container">
          <transition name="fade" mode="out-in">
            <router-view v-slot="{ Component, route }">
              <keep-alive :include="['Dashboard', 'BudgetAnalysis']">
                <component :is="Component" :key="route.path" />
              </keep-alive>
            </router-view>
          </transition>
        </div>
      </a-layout-content>
    </a-layout>

    <!-- 消息通知抽屉 - 增强版 -->
    <a-drawer
      v-model:open="notificationDrawerVisible"
      title="消息中心"
      placement="right"
      width="420"
      class="notification-drawer"
    >
      <template #title>
        <div class="drawer-header">
          <div class="drawer-title">
            <BellOutlined />
            <span>消息中心</span>
          </div>
          <div class="drawer-actions">
            <a-button type="text" size="small" @click="markAllAsRead">
              全部已读
            </a-button>
          </div>
        </div>
      </template>

      <div class="notification-content">
        <!-- 通知筛选 -->
        <div class="notification-filter">
          <a-radio-group v-model:value="notificationFilter" size="small">
            <a-radio-button value="all">全部</a-radio-button>
            <a-radio-button value="unread">未读</a-radio-button>
            <a-radio-button value="system">系统</a-radio-button>
            <a-radio-button value="workflow">工作流</a-radio-button>
          </a-radio-group>
        </div>

        <!-- 通知列表 -->
        <div class="notification-list">
          <a-list
            :data-source="filteredNotifications"
            :loading="notificationLoading"
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item
                class="notification-item"
                :class="{ 'unread': !item.is_read }"
                @click="readNotification(item)"
              >
                <a-list-item-meta>
                  <template #avatar>
                    <a-avatar :style="{ backgroundColor: getNotificationColor(item.type) }">
                      <component :is="getNotificationIcon(item.type)" />
                    </a-avatar>
                  </template>
                  <template #title>
                    <div class="notification-title">
                      {{ item.title }}
                      <a-tag v-if="!item.is_read" color="red" size="small">未读</a-tag>
                    </div>
                  </template>
                  <template #description>
                    <div class="notification-desc">
                      <p>{{ item.content }}</p>
                      <div class="notification-meta">
                        <span class="notification-time">{{ formatNotificationTime(item.created_at) }}</span>
                        <a-tag :color="getNotificationTypeColor(item.type)" size="small">
                          {{ getNotificationTypeName(item.type) }}
                        </a-tag>
                      </div>
                    </div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>

            <template #loadMore>
              <div v-if="hasMoreNotifications" class="load-more">
                <a-button type="link" @click="loadMoreNotifications">
                  加载更多
                </a-button>
              </div>
            </template>
          </a-list>
        </div>
      </div>
    </a-drawer>

    <!-- 帮助中心抽屉 -->
    <a-drawer
      v-model:open="helpDrawerVisible"
      title="帮助中心"
      placement="right"
      width="380"
      class="help-drawer"
    >
      <div class="help-content">
        <a-collapse v-model:activeKey="helpActiveKeys" ghost>
          <a-collapse-panel key="quick-start" header="快速开始">
            <div class="help-section">
              <h4>新用户指南</h4>
              <ul>
                <li>完善个人信息设置</li>
                <li>了解系统主要功能模块</li>
                <li>查看待办事项和通知</li>
              </ul>
            </div>
          </a-collapse-panel>

          <a-collapse-panel key="common-operations" header="常用操作">
            <div class="help-section">
              <h4>报销流程</h4>
              <ol>
                <li>点击"我要报销"创建申请</li>
                <li>填写报销信息并上传凭证</li>
                <li>提交审批流程</li>
                <li>跟踪审批进度</li>
              </ol>
            </div>
          </a-collapse-panel>

          <a-collapse-panel key="contact" header="联系支持">
            <div class="help-section">
              <p>如需技术支持，请联系：</p>
              <p>📞 技术热线：400-xxx-xxxx</p>
              <p>📧 邮箱：<EMAIL></p>
              <p>🕐 服务时间：9:00-18:00</p>
            </div>
          </a-collapse-panel>
        </a-collapse>
      </div>
    </a-drawer>
  </a-layout>
</template>

<script setup>
import { ref, computed, onMounted, watch, h, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  BellOutlined,
  DownOutlined,
  SettingOutlined,
  LogoutOutlined,
  DashboardOutlined,
  FundOutlined,
  AccountBookOutlined,
  SearchOutlined,
  HomeOutlined,
  PlusOutlined,
  QuestionCircleOutlined,
  SafetyOutlined,
  EditOutlined,
  BarChartOutlined,
  EyeOutlined,
  SwapOutlined,
  PlusCircleOutlined,
  UnorderedListOutlined,
  CheckCircleOutlined,
  CreditCardOutlined,
  LineChartOutlined,
  FileTextOutlined,
  FolderOutlined,
  AuditOutlined,
  PlayCircleOutlined,
  ShoppingCartOutlined,
  CalendarOutlined,
  FormOutlined,
  CheckSquareOutlined,
  CarOutlined,
  TeamOutlined,
  CrownOutlined,
  KeyOutlined,
  ContactsOutlined,
  ApartmentOutlined,
  ControlOutlined,
  NotificationOutlined,
  PartitionOutlined,
  HistoryOutlined,
  CalculatorOutlined,
  FileProtectOutlined,
  FileAddOutlined,
  BookOutlined,
  InfoCircleOutlined,
  ExclamationCircleOutlined,
  WarningOutlined
} from '@ant-design/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { getNotifications, markNotificationAsRead, getUnreadNotificationCount } from '@/api/system'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 响应式数据
const collapsed = ref(false)
const selectedKeys = ref(['dashboard'])
const openKeys = ref(['budget', 'expenditure'])
const notificationDrawerVisible = ref(false)
const helpDrawerVisible = ref(false)
const notifications = ref([])
const unreadCount = ref(0)
const todoCount = ref(0)
const pendingExpenses = ref(0)
const pendingApprovals = ref(0)

// 搜索相关
const searchValue = ref('')
const searchLoading = ref(false)
const showSearchSuggestions = ref(false)
const searchSuggestions = ref([
  { key: 'expense-apply', title: '我要报销', icon: PlusCircleOutlined },
  { key: 'budget-analysis', title: '预算分析', icon: BarChartOutlined },
  { key: 'expense-list', title: '报销管理', icon: UnorderedListOutlined },
  { key: 'user-manage', title: '用户管理', icon: TeamOutlined }
])

// 通知相关
const notificationFilter = ref('all')
const notificationLoading = ref(false)
const hasMoreNotifications = ref(false)
const helpActiveKeys = ref(['quick-start'])

// 计算属性
const user = computed(() => authStore.user)
const isAdmin = computed(() => authStore.isAdmin)

const filteredNotifications = computed(() => {
  if (notificationFilter.value === 'all') return notifications.value
  if (notificationFilter.value === 'unread') return notifications.value.filter(n => !n.is_read)
  return notifications.value.filter(n => n.type === notificationFilter.value)
})

// 面包屑导航 - 增强版
const breadcrumbItems = computed(() => {
  const items = []
  const pathSegments = route.path.split('/').filter(Boolean)

  // 路由映射表
  const routeMap = {
    // 系统管理
    'system': { title: '系统管理', icon: SettingOutlined },
    'users': { title: '用户管理', icon: TeamOutlined },
    'roles': { title: '角色管理', icon: CrownOutlined },
    'permissions': { title: '权限管理', icon: KeyOutlined },
    'positions': { title: '岗位管理', icon: ContactsOutlined },
    'organizations': { title: '组织管理', icon: ApartmentOutlined },
    'config': { title: '系统配置', icon: ControlOutlined },
    'notifications': { title: '通知管理', icon: NotificationOutlined },
    'workflow-designer': { title: '工作流设计器', icon: PartitionOutlined },
    'audit-logs': { title: '审计日志', icon: HistoryOutlined },

    // 预算管理
    'budget': { title: '预算管理', icon: FundOutlined },
    'compile': { title: '预算编制', icon: EditOutlined },
    'analysis': { title: '预算分析', icon: BarChartOutlined },
    'monitor': { title: '预算监控', icon: EyeOutlined },
    'adjustment': { title: '预算调整', icon: SwapOutlined },

    // 支出控制
    'expenditure': { title: '支出控制', icon: AccountBookOutlined },
    'apply': { title: '报销申请', icon: PlusCircleOutlined },
    'list': { title: '报销管理', icon: UnorderedListOutlined },
    'approval': { title: '报销审批', icon: CheckCircleOutlined },
    'payment': { title: '支付管理', icon: CreditCardOutlined },

    // 合同管理
    'contract': { title: '合同管理', icon: FileTextOutlined },
    'create': { title: '合同创建', icon: PlusOutlined },
    'approve': { title: '合同审批', icon: AuditOutlined },
    'execute': { title: '合同执行', icon: PlayCircleOutlined },

    // 采购管理
    'procurement': { title: '采购管理', icon: ShoppingCartOutlined },
    'plan': { title: '采购计划', icon: CalendarOutlined }
  }

  // 构建面包屑路径
  let currentPath = ''
  pathSegments.forEach((segment, index) => {
    currentPath += `/${segment}`
    const routeInfo = routeMap[segment]

    if (routeInfo) {
      items.push({
        title: routeInfo.title,
        path: index === pathSegments.length - 1 ? null : currentPath,
        icon: routeInfo.icon
      })
    }
  })

  return items
})

// 更新选中的菜单键 - 增强版
const updateSelectedKeys = (path) => {
  const keyMap = {
    '/': ['dashboard'],
    // 预算管理
    '/budget/compile': ['budget-plan'],
    '/budget/analysis': ['budget-analysis'],
    '/budget/monitor': ['budget-monitor'],
    '/budget/adjustment': ['budget-adjustment'],
    // 支出控制
    '/expenditure/apply': ['expense-apply'],
    '/expenditure/list': ['expense-list'],
    '/expenditure/approval': ['expense-approve'],
    '/expenditure/payment': ['expense-payment'],
    '/expenditure/analysis': ['expense-analysis'],
    // 合同管理
    '/contract/create': ['contract-create'],
    '/contract/list': ['contract-list'],
    '/contract/approve': ['contract-approve'],
    '/contract/execute': ['contract-execute'],
    // 采购管理
    '/procurement/plan': ['procurement-plan'],
    '/procurement/apply': ['procurement-apply'],
    '/procurement/approve': ['procurement-approve'],
    '/procurement/execute': ['procurement-execute'],
    // 系统管理
    '/system/users': ['user-manage'],
    '/system/roles': ['role-manage'],
    '/system/permissions': ['permission-manage'],
    '/system/positions': ['position-manage'],
    '/system/organizations': ['org-manage'],
    '/system/config': ['system-config'],
    '/system/notifications': ['notification-manage'],
    '/system/workflow-designer': ['workflow-designer'],
    '/system/audit-logs': ['audit-log']
  }

  selectedKeys.value = keyMap[path] || ['dashboard']

  // 自动展开对应的菜单组
  const openKeyMap = {
    '/budget': 'budget',
    '/expenditure': 'expenditure',
    '/contract': 'contract',
    '/procurement': 'procurement',
    '/finance': 'finance',
    '/system': 'system'
  }

  const pathPrefix = '/' + path.split('/')[1]
  if (openKeyMap[pathPrefix] && !openKeys.value.includes(openKeyMap[pathPrefix])) {
    openKeys.value.push(openKeyMap[pathPrefix])
  }
}

// 监听路由变化，更新选中的菜单
watch(() => route.path, (newPath) => {
  updateSelectedKeys(newPath)
}, { immediate: true })

// 搜索功能 - 增强版
const onSearch = async (value) => {
  if (!value.trim()) return

  searchLoading.value = true
  try {
    // 模拟搜索API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    message.success(`搜索"${value}"完成`)
    // 这里可以跳转到搜索结果页面
    router.push(`/search?q=${encodeURIComponent(value)}`)
  } catch (error) {
    message.error('搜索失败')
  } finally {
    searchLoading.value = false
    showSearchSuggestions.value = false
  }
}

// 处理搜索建议点击
const handleSuggestionClick = (suggestion) => {
  const routeMap = {
    'expense-apply': '/expenditure/apply',
    'budget-analysis': '/budget/analysis',
    'expense-list': '/expenditure/list',
    'user-manage': '/system/users'
  }

  if (routeMap[suggestion.key]) {
    router.push(routeMap[suggestion.key])
  }
  showSearchSuggestions.value = false
}

// 隐藏搜索建议
const hideSearchSuggestions = () => {
  setTimeout(() => {
    showSearchSuggestions.value = false
  }, 200)
}

// 快捷操作
const quickAction = (type) => {
  const actionMap = {
    'expense': '/expenditure/apply'
  }

  if (actionMap[type]) {
    router.push(actionMap[type])
  }
}

// 显示帮助
const showHelp = () => {
  helpDrawerVisible.value = true
}

// 显示个人信息
const showProfile = () => {
  router.push('/profile')
}

// 显示设置
const showSettings = () => {
  router.push('/settings')
}

// 显示通知 - 增强版
const showNotifications = async () => {
  notificationDrawerVisible.value = true
  await loadNotifications()
}

// 加载通知列表
const loadNotifications = async () => {
  notificationLoading.value = true
  try {
    const response = await getNotifications({ page_size: 20 })
    notifications.value = response.data.list || []
    hasMoreNotifications.value = response.data.has_more || false
  } catch (error) {
    message.error('加载通知失败')
  } finally {
    notificationLoading.value = false
  }
}

// 加载更多通知
const loadMoreNotifications = async () => {
  try {
    const response = await getNotifications({
      page_size: 10,
      offset: notifications.value.length
    })
    notifications.value.push(...(response.data.list || []))
    hasMoreNotifications.value = response.data.has_more || false
  } catch (error) {
    message.error('加载更多通知失败')
  }
}

// 加载未读通知数量
const loadUnreadCount = async () => {
  try {
    const response = await getUnreadNotificationCount()
    unreadCount.value = response.data || 0
  } catch (error) {
    console.error('加载未读通知数量失败:', error)
  }
}

// 阅读通知
const readNotification = async (notification) => {
  if (!notification.is_read) {
    try {
      await markNotificationAsRead(notification.id)
      notification.is_read = true
      unreadCount.value = Math.max(0, unreadCount.value - 1)
    } catch (error) {
      message.error('标记已读失败')
    }
  }
}

// 全部标记为已读
const markAllAsRead = async () => {
  try {
    // 这里需要调用批量标记已读的API
    notifications.value.forEach(n => n.is_read = true)
    unreadCount.value = 0
    message.success('已全部标记为已读')
  } catch (error) {
    message.error('操作失败')
  }
}

// 通知相关工具函数
const getNotificationColor = (type) => {
  const colors = {
    'system': '#1890ff',
    'workflow': '#52c41a',
    'warning': '#faad14',
    'error': '#ff4d4f'
  }
  return colors[type] || '#1890ff'
}

const getNotificationIcon = (type) => {
  const icons = {
    'system': SettingOutlined,
    'workflow': PartitionOutlined,
    'warning': WarningOutlined,
    'error': ExclamationCircleOutlined
  }
  return icons[type] || InfoCircleOutlined
}

const getNotificationTypeColor = (type) => {
  const colors = {
    'system': 'blue',
    'workflow': 'green',
    'warning': 'orange',
    'error': 'red'
  }
  return colors[type] || 'default'
}

const getNotificationTypeName = (type) => {
  const names = {
    'system': '系统通知',
    'workflow': '工作流',
    'warning': '警告',
    'error': '错误'
  }
  return names[type] || '通知'
}

const formatNotificationTime = (time) => {
  const now = new Date()
  const notificationTime = new Date(time)
  const diff = now - notificationTime

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

// 处理菜单点击 - 增强版
const handleMenuClick = ({ key }) => {
  const routeMap = {
    'dashboard': '/',
    // 预算管理
    'budget-plan': '/budget/compile',
    'budget-analysis': '/budget/analysis',
    'budget-monitor': '/budget/monitor',
    'budget-adjustment': '/budget/adjustment',
    // 支出管理
    'expense-apply': '/expenditure/apply',
    'expense-list': '/expenditure/list',
    'expense-approve': '/expenditure/approval',
    'expense-payment': '/expenditure/payment',
    'expense-analysis': '/expenditure/analysis',
    // 合同管理
    'contract-create': '/contract/create',
    'contract-list': '/contract/list',
    'contract-approve': '/contract/approve',
    'contract-execute': '/contract/execute',
    // 采购管理
    'procurement-plan': '/procurement/plan',
    'procurement-apply': '/procurement/apply',
    'procurement-approve': '/procurement/approve',
    'procurement-execute': '/procurement/execute',
    // 财务管理
    'voucher-rules': '/finance/voucher-rules',
    'voucher-generate': '/finance/voucher-generate',
    'account-book': '/finance/account-book',
    'financial-report': '/finance/financial-report',
    // 系统管理
    'user-manage': '/system/users',
    'role-manage': '/system/roles',
    'permission-manage': '/system/permissions',
    'org-manage': '/system/organizations',
    'system-config': '/system/config'
  }

  if (routeMap[key]) {
    router.push(routeMap[key])
  } else {
    message.info(`${key} 功能开发中...`)
  }
}

// 退出登录
const handleLogout = async () => {
  try {
    await authStore.logout()
    message.success('退出成功')
    router.push('/login')
  } catch (error) {
    message.error('退出失败')
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    // 模拟加载统计数据
    todoCount.value = 5
    pendingExpenses.value = 12
    pendingApprovals.value = 8
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadUnreadCount()
  loadStatistics()

  // 定时刷新数据
  setInterval(() => {
    loadUnreadCount()
    loadStatistics()
  }, 30000) // 每30秒刷新一次
})
</script>

<style scoped>
/* 系统布局 - 企业级专业设计 */
.system-layout {
  min-height: 100vh;
  background: var(--bg-secondary);
}

/* 顶部导航栏 */
.header {
  background: var(--bg-primary);
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  box-shadow: var(--shadow-md);
  border-bottom: 1px solid var(--border-light);
  position: relative;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-text {
  display: flex;
  flex-direction: column;
}

.system-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  line-height: var(--line-height-tight);
}

.system-subtitle {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  margin-top: 2px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.menu-toggle {
  color: var(--text-secondary);
  font-size: 16px;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.menu-toggle:hover {
  background: var(--bg-hover);
  color: var(--primary-color);
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  max-width: 600px;
  margin: 0 var(--spacing-xl);
}

.breadcrumb-nav {
  font-size: var(--font-size-sm);
}

.breadcrumb-nav :deep(.ant-breadcrumb-link) {
  color: var(--text-secondary);
  transition: color 0.2s ease;
}

.breadcrumb-nav :deep(.ant-breadcrumb-link:hover) {
  color: var(--primary-color);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  min-width: 400px;
  justify-content: flex-end;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.header-btn {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.header-btn:hover {
  background: var(--bg-hover);
  color: var(--primary-color);
}

/* 搜索容器 */
.search-container {
  position: relative;
}

.global-search {
  width: 280px;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
}

.global-search:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(22, 100, 255, 0.1);
}

.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  margin-top: var(--spacing-xs);
}

.suggestion-group {
  padding: var(--spacing-sm);
}

.suggestion-title {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  margin-bottom: var(--spacing-sm);
  padding: 0 var(--spacing-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.suggestion-item:hover {
  background: var(--bg-hover);
}

.suggestion-icon {
  color: var(--primary-color);
  font-size: 14px;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.action-btn {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--bg-hover);
  color: var(--primary-color);
}

/* 通知徽章 */
.notification-badge {
  margin-right: var(--spacing-sm);
}

/* 用户信息 */
.user-profile {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.user-profile:hover {
  background: var(--bg-hover);
}

.user-avatar {
  border: 2px solid var(--border-light);
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
}

.user-role {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  line-height: var(--line-height-tight);
}

.dropdown-icon {
  color: var(--text-tertiary);
  font-size: 12px;
  transition: transform 0.2s ease;
}

.user-profile:hover .dropdown-icon {
  transform: rotate(180deg);
}

/* 用户菜单 */
.user-menu {
  min-width: 180px;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
}

.user-menu :deep(.ant-menu-item) {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  margin: 0;
  border-radius: var(--radius-sm);
}

.logout-item {
  color: var(--error-color) !important;
}

.logout-item:hover {
  background: rgba(244, 67, 54, 0.1) !important;
}

/* 主布局 */
.main-layout {
  height: calc(100vh - 64px);
}

/* 侧边栏 */
.sidebar {
  background: var(--bg-primary);
  box-shadow: var(--shadow-md);
  border-right: 1px solid var(--border-light);
  height: 100%;
  overflow: hidden;
  transition: all 0.3s ease;
}

.menu-container {
  height: calc(100% - 60px);
  overflow-y: auto;
  overflow-x: hidden;
  padding: var(--spacing-md) 0;
}

/* 主菜单样式 */
.main-menu {
  border: none;
  background: transparent;
  height: 100%;
}

.main-menu :deep(.ant-menu-item) {
  margin: 4px 8px;
  border-radius: 8px;
  height: 40px;
  line-height: 40px;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
}

.main-menu :deep(.ant-menu-item:hover) {
  background: var(--primary-color-light);
  color: var(--primary-color);
}

.main-menu :deep(.ant-menu-item-selected) {
  background: var(--primary-color);
  color: white;
  font-weight: 500;
}

.main-menu :deep(.ant-menu-item-selected::after) {
  display: none;
}

.main-menu :deep(.ant-menu-submenu) {
  margin: 4px 8px;
}

.main-menu :deep(.ant-menu-submenu-title) {
  border-radius: 8px;
  height: 40px;
  line-height: 40px;
  margin: 0;
  padding: 0 16px;
  transition: all 0.2s ease;
}

.main-menu :deep(.ant-menu-submenu-title:hover) {
  background: var(--bg-hover);
  color: var(--primary-color);
}

.main-menu :deep(.ant-menu-submenu-open > .ant-menu-submenu-title) {
  background: var(--bg-hover);
  color: var(--primary-color);
}

.main-menu :deep(.ant-menu-sub) {
  background: transparent;
}

.main-menu :deep(.ant-menu-sub .ant-menu-item) {
  margin: 2px 16px;
  padding-left: 32px !important;
  height: 36px;
  line-height: 36px;
}

/* 菜单图标包装器 */
.menu-icon-wrapper {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  margin-right: var(--spacing-sm);
}

.budget-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.expenditure-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.contract-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.procurement-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.system-icon {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
}

/* 菜单标题和描述 */
.menu-group-title {
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.menu-description {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  display: block;
  margin-top: 2px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.menu-title {
  font-weight: var(--font-weight-medium);
}

/* 菜单徽章 */
.menu-badge {
  margin-left: auto;
}

.menu-item-badge {
  margin-left: auto;
}

.menu-item-dashboard {
  background: linear-gradient(135deg, var(--primary-color-light) 0%, rgba(22, 100, 255, 0.1) 100%);
  border: 1px solid var(--primary-color-light);
}

/* 侧边栏底部 */
.sidebar-footer {
  height: 60px;
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-light);
  background: var(--bg-tertiary);
}

.system-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.version-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.version-label {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.version-number {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.online-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

/* 主内容区 */
.main-content {
  background: var(--bg-secondary);
  height: 100%;
  overflow: hidden;
}

.content-container {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 通知抽屉 */
.notification-drawer :deep(.ant-drawer-header) {
  border-bottom: 1px solid var(--border-light);
  padding: var(--spacing-md) var(--spacing-lg);
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.drawer-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.drawer-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.notification-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.notification-filter {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
}

.notification-list {
  flex: 1;
  overflow-y: auto;
}

.notification-item {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.notification-item:hover {
  background: var(--bg-hover);
}

.notification-item.unread {
  background: rgba(22, 100, 255, 0.02);
  border-left: 3px solid var(--primary-color);
}

.notification-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: var(--font-weight-medium);
}

.notification-desc p {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.notification-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notification-time {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.load-more {
  text-align: center;
  padding: var(--spacing-md);
}

/* 帮助抽屉 */
.help-drawer :deep(.ant-drawer-body) {
  padding: var(--spacing-lg);
}

.help-content {
  font-size: var(--font-size-sm);
}

.help-section h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.help-section ul,
.help-section ol {
  margin: 0;
  padding-left: var(--spacing-lg);
}

.help-section li {
  margin-bottom: var(--spacing-xs);
  color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-center {
    max-width: 400px;
    margin: 0 var(--spacing-md);
  }

  .global-search {
    width: 200px;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 0 var(--spacing-md);
  }

  .logo-text {
    display: none;
  }

  .global-search {
    width: 160px;
  }

  .user-info {
    display: none;
  }

  .sidebar {
    position: fixed;
    z-index: 200;
    height: 100vh;
  }

  .main-content {
    margin-left: 0;
  }
}

@media (max-width: 576px) {
  .header-center,
  .quick-actions {
    display: none;
  }

  .global-search {
    width: 120px;
  }
}
</style>
