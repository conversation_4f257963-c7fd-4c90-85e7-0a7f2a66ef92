package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"
)

// JSON 自定义JSON类型，用于处理PostgreSQL的JSONB字段
type JSON map[string]interface{}

// Value 实现driver.Valuer接口
func (j JSON) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan 实现sql.Scanner接口
func (j *JSON) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return errors.New("cannot scan into JSON")
	}

	return json.Unmarshal(bytes, j)
}

// WorkflowDefinition 工作流定义模型
type WorkflowDefinition struct {
	BaseModel
	Name        string                 `gorm:"size:100;not null" json:"name"`
	Code        string                 `gorm:"uniqueIndex;size:50;not null" json:"code"`
	Category    string                 `gorm:"size:50;not null" json:"category"`
	Version     int                    `gorm:"default:1" json:"version"`
	Definition  map[string]interface{} `gorm:"type:jsonb;not null" json:"definition"`
	Description string                 `gorm:"size:255" json:"description"`
	IsActive    bool                   `gorm:"default:true" json:"is_active"`
	Status      Status                 `gorm:"size:20;default:'active'" json:"status"`
	CreatorID   uint                   `gorm:"index" json:"creator_id"`

	// 关联字段
	Creator   User               `gorm:"foreignKey:CreatorID" json:"creator,omitempty"`
	Instances []WorkflowInstance `gorm:"foreignKey:DefinitionID" json:"instances,omitempty"`
}

// TableName 指定表名
func (WorkflowDefinition) TableName() string {
	return "workflow_definitions"
}

// WorkflowInstance 工作流实例模型
type WorkflowInstance struct {
	BaseModel
	InstanceID   string     `gorm:"uniqueIndex;size:64;not null" json:"instance_id"`
	DefinitionID uint       `gorm:"index;not null" json:"definition_id"`
	BusinessType string     `gorm:"size:50;not null" json:"business_type"`
	BusinessID   string     `gorm:"size:64;not null" json:"business_id"`
	Status       string     `gorm:"size:20;default:'running'" json:"status"`
	CurrentNode  string     `gorm:"size:100" json:"current_node"`
	InitiatorID  uint       `gorm:"index;not null" json:"initiator_id"`
	StartedAt    time.Time  `gorm:"default:NOW()" json:"started_at"`
	CompletedAt  *time.Time `json:"completed_at"`

	// 关联字段
	Definition WorkflowDefinition `gorm:"foreignKey:DefinitionID" json:"definition,omitempty"`
	Initiator  User               `gorm:"foreignKey:InitiatorID" json:"initiator,omitempty"`
	Tasks      []WorkflowTask     `gorm:"foreignKey:InstanceID;references:InstanceID" json:"tasks,omitempty"`
}

// TableName 指定表名
func (WorkflowInstance) TableName() string {
	return "workflow_instances"
}

// WorkflowTask 工作流任务模型
type WorkflowTask struct {
	BaseModel
	TaskID      string     `gorm:"uniqueIndex;size:64;not null" json:"task_id"`
	InstanceID  string     `gorm:"index;not null" json:"instance_id"`
	NodeID      string     `gorm:"size:100;not null" json:"node_id"`
	TaskName    string     `gorm:"size:200;not null" json:"task_name"`
	TaskType    string     `gorm:"size:50;not null" json:"task_type"`
	Status      string     `gorm:"size:20;default:'pending'" json:"status"`
	AssigneeID  *uint      `gorm:"index" json:"assignee_id"`
	AssignedAt  *time.Time `json:"assigned_at"`
	CompletedAt *time.Time `json:"completed_at"`
	Comment     string     `gorm:"type:text" json:"comment"`
	FormData    JSON       `gorm:"type:jsonb" json:"form_data"`

	// 关联字段
	Instance *WorkflowInstance `gorm:"foreignKey:InstanceID;references:InstanceID" json:"instance,omitempty"`
	Assignee *User             `gorm:"foreignKey:AssigneeID" json:"assignee,omitempty"`
}

// TableName 指定表名
func (WorkflowTask) TableName() string {
	return "workflow_tasks"
}

// ProcessDefinition 流程定义结构
type ProcessDefinition struct {
	Nodes []ProcessNode `json:"nodes"`
	Edges []ProcessEdge `json:"edges"`
}

// ProcessNode 流程节点
type ProcessNode struct {
	ID       string                 `json:"id"`
	Type     string                 `json:"type"` // start, task, gateway, end
	Name     string                 `json:"name"`
	Config   map[string]interface{} `json:"config"`
	Position NodePosition           `json:"position"`
}

// ProcessEdge 流程边
type ProcessEdge struct {
	ID     string                 `json:"id"`
	Source string                 `json:"source"`
	Target string                 `json:"target"`
	Label  string                 `json:"label"`
	Config map[string]interface{} `json:"config"`
}

// NodePosition 节点位置信息
type NodePosition struct {
	X float64 `json:"x"`
	Y float64 `json:"y"`
}

// ProcessVariables 流程变量
type ProcessVariables map[string]interface{}

// Value 实现 driver.Valuer 接口
func (p ProcessDefinition) Value() (driver.Value, error) {
	return json.Marshal(p)
}

// Scan 实现 sql.Scanner 接口
func (p *ProcessDefinition) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, p)
}

// Value 实现 driver.Valuer 接口
func (p ProcessVariables) Value() (driver.Value, error) {
	return json.Marshal(p)
}

// Scan 实现 sql.Scanner 接口
func (p *ProcessVariables) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, p)
}
