<template>
  <div class="budget-monitor">
    <a-card :bordered="false">
      <template #title>
        <h3>预算监控</h3>
      </template>

      <!-- 监控概览 -->
      <div class="monitor-overview">
        <a-row :gutter="16">
          <a-col :xs="12" :sm="6">
            <a-card size="small">
              <a-statistic
                title="预算总额"
                :value="overview.total_budget"
                :precision="2"
                suffix="万元"
                :value-style="{ color: '#1890ff' }"
              />
            </a-card>
          </a-col>
          <a-col :xs="12" :sm="6">
            <a-card size="small">
              <a-statistic
                title="已执行"
                :value="overview.executed_amount"
                :precision="2"
                suffix="万元"
                :value-style="{ color: '#52c41a' }"
              />
            </a-card>
          </a-col>
          <a-col :xs="12" :sm="6">
            <a-card size="small">
              <a-statistic
                title="执行率"
                :value="overview.execution_rate"
                :precision="1"
                suffix="%"
                :value-style="{ color: getExecutionRateColor(overview.execution_rate) }"
              />
            </a-card>
          </a-col>
          <a-col :xs="12" :sm="6">
            <a-card size="small">
              <a-statistic
                title="预警项目"
                :value="overview.alert_count"
                suffix="个"
                :value-style="{ color: '#ff4d4f' }"
              />
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 预算执行进度 -->
      <div class="execution-progress">
        <a-card title="预算执行进度" size="small">
          <a-table
            :columns="progressColumns"
            :data-source="progressData"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'budget_amount'">
                {{ formatMoney(record.budget_amount) }}
              </template>
              <template v-else-if="column.key === 'executed_amount'">
                {{ formatMoney(record.executed_amount) }}
              </template>
              <template v-else-if="column.key === 'remaining_amount'">
                {{ formatMoney(record.remaining_amount) }}
              </template>
              <template v-else-if="column.key === 'execution_rate'">
                <a-progress
                  :percent="record.execution_rate"
                  :stroke-color="getProgressColor(record.execution_rate)"
                  size="small"
                />
              </template>
              <template v-else-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ getStatusName(record.status) }}
                </a-tag>
              </template>
            </template>
          </a-table>
        </a-card>
      </div>

      <!-- 预警信息 -->
      <div class="alert-section">
        <a-card title="预算预警" size="small">
          <a-list
            :data-source="alertData"
            :pagination="false"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #avatar>
                    <a-avatar :style="{ backgroundColor: getAlertColor(item.level) }">
                      <component :is="getAlertIcon(item.level)" />
                    </a-avatar>
                  </template>
                  <template #title>
                    <span>{{ item.title }}</span>
                    <a-tag :color="getAlertColor(item.level)" style="margin-left: 8px;">
                      {{ getAlertLevelName(item.level) }}
                    </a-tag>
                  </template>
                  <template #description>
                    {{ item.description }}
                  </template>
                </a-list-item-meta>
                <template #actions>
                  <a @click="handleAlert(item)">处理</a>
                  <a @click="viewAlertDetail(item)">详情</a>
                </template>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </div>

      <!-- 趋势图表 -->
      <div class="trend-charts">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-card title="预算执行趋势" size="small">
              <v-chart
                class="chart"
                :option="trendChartOption"
                autoresize
              />
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="部门执行对比" size="small">
              <v-chart
                class="chart"
                :option="departmentChartOption"
                autoresize
              />
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, BarChart } from 'echarts/charts'
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components'
import VChart from 'vue-echarts'
import {
  ExclamationCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue'
import { getBudgetMonitorData } from '@/api/budget'

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

// 响应式数据
const progressData = ref([])
const alertData = ref([])

// 监控概览数据
const overview = reactive({
  total_budget: 0,
  executed_amount: 0,
  execution_rate: 0,
  alert_count: 0
})

// 表格列配置
const progressColumns = [
  { title: '预算项目', dataIndex: 'item_name', key: 'item_name' },
  { title: '预算金额', key: 'budget_amount' },
  { title: '已执行', key: 'executed_amount' },
  { title: '剩余金额', key: 'remaining_amount' },
  { title: '执行进度', key: 'execution_rate' },
  { title: '状态', key: 'status' }
]

// 图表配置
const trendChartOption = ref({
  title: {
    text: '预算执行趋势',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['预算金额', '执行金额'],
    top: 30
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value}万'
    }
  },
  series: [
    {
      name: '预算金额',
      type: 'line',
      data: [120, 132, 101, 134, 90, 230],
      itemStyle: { color: '#1890ff' }
    },
    {
      name: '执行金额',
      type: 'line',
      data: [100, 120, 95, 125, 85, 200],
      itemStyle: { color: '#52c41a' }
    }
  ]
})

const departmentChartOption = ref({
  title: {
    text: '部门执行对比',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['急诊科', '外科', '内科', '儿科', '妇产科']
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value}%'
    }
  },
  series: [
    {
      name: '执行率',
      type: 'bar',
      data: [85, 92, 78, 88, 95],
      itemStyle: { color: '#1890ff' }
    }
  ]
})

// 工具函数
const formatMoney = (amount) => {
  return (amount / 10000).toFixed(2) + '万'
}

const getExecutionRateColor = (rate) => {
  if (rate >= 90) return '#ff4d4f'
  if (rate >= 80) return '#faad14'
  return '#52c41a'
}

const getProgressColor = (rate) => {
  if (rate >= 90) return '#ff4d4f'
  if (rate >= 80) return '#faad14'
  return '#52c41a'
}

const getStatusColor = (status) => {
  const colors = {
    'normal': 'green',
    'warning': 'orange',
    'danger': 'red'
  }
  return colors[status] || 'default'
}

const getStatusName = (status) => {
  const names = {
    'normal': '正常',
    'warning': '预警',
    'danger': '超支'
  }
  return names[status] || '未知'
}

const getAlertColor = (level) => {
  const colors = {
    'info': '#1890ff',
    'warning': '#faad14',
    'danger': '#ff4d4f'
  }
  return colors[level] || '#1890ff'
}

const getAlertIcon = (level) => {
  const icons = {
    'info': InfoCircleOutlined,
    'warning': WarningOutlined,
    'danger': ExclamationCircleOutlined
  }
  return icons[level] || InfoCircleOutlined
}

const getAlertLevelName = (level) => {
  const names = {
    'info': '提醒',
    'warning': '预警',
    'danger': '严重'
  }
  return names[level] || '未知'
}

// 数据加载函数
const loadMonitorData = async () => {
  try {
    const response = await getBudgetMonitorData()
    const data = response.data
    
    // 更新概览数据
    Object.assign(overview, data.overview)
    
    // 更新进度数据
    progressData.value = data.progress_list || []
    
    // 更新预警数据
    alertData.value = data.alert_list || []
    
  } catch (error) {
    message.error('加载监控数据失败')
  }
}

// 事件处理函数
const handleAlert = (item) => {
  message.info('处理预警功能开发中...')
}

const viewAlertDetail = (item) => {
  message.info('查看预警详情功能开发中...')
}

// 组件挂载时加载数据
onMounted(() => {
  loadMonitorData()
})
</script>

<style scoped>
.budget-monitor {
  padding: 24px;
}

.monitor-overview {
  margin-bottom: 24px;
}

.execution-progress {
  margin-bottom: 24px;
}

.alert-section {
  margin-bottom: 24px;
}

.trend-charts {
  margin-bottom: 24px;
}

.chart {
  height: 300px;
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .budget-monitor {
    padding: 16px;
  }
  
  .chart {
    height: 250px;
  }
}
</style>
