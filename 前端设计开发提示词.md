
---

## **🏥 医院运营管理系统 - 前端开发设计提示词 (Frontend Development Prompts)**

### **一、🎨 通用设计与交互要求 (General Design & UX Requirements)**

在开始具体页面开发前，请遵循以下全局规范：

1.  **整体布局 (Layout):**
    *   采用 **Ant Design Pro** 的经典后台管理布局：顶部导航栏 + 左侧菜单栏 + 内容区 + 页脚。
    *   左侧菜单栏需根据用户权限动态生成，并支持折叠。
    *   内容区应包含面包屑导航，方便用户定位。

2.  **响应式设计 (Responsiveness):**
    *   系统需适配主流桌面浏览器分辨率（1280px以上）。表格、表单等复杂元素在较小屏幕下应有合理的展示方式（如横向滚动条）。

3.  **组件风格 (Component Style):**
    *   严格遵循 **Ant Design Vue** 的设计语言，保持界面风格统一。非必要不引入第三方UI组件。

4.  **交互反馈 (Interaction Feedback):**
    *   **加载状态：** 所有数据加载（表格、图表、提交表单）都必须有明确的加载指示，如使用 `<a-spin>`。
    *   **操作确认：** 对于删除、作废等不可逆操作，必须使用 `<a-popconfirm>` 或 `<a-modal>` 进行二次确认。
    *   **结果通知：** 操作成功或失败后，使用 `<a-message>` (轻量提示) 或 `<a-notification>` (重量通知) 给予用户清晰反馈。

5.  **数据展示 (Data Display):**
    *   **金额：** 统一格式化为千分位，保留两位小数（如 `1,234,567.89` 元）。
    *   **日期时间：** 统一格式化为 `YYYY-MM-DD HH:mm:ss`。
    *   **状态/类型：** 使用 `<a-tag>` 组件，并根据不同状态（如待审批、已通过、已驳回）赋予不同颜色。

6.  **权限控制 (Permission Control):**
    *   无权限访问的菜单/路由应直接隐藏。
    *   页面内无权限的操作按钮（如“新增”、“删除”、“审批”）应置灰（disabled）并给出提示，而不是直接隐藏。

---

### **二、🛠️ 核心模块页面与组件设计 (Module & Page Design)**

#### **💠 模块一：基础平台与门户 (Platform & Portal)**

*   **1.1 登录页 (`/login`)**
    *   **布局：** 页面居中布局。
    *   **组件：** 系统Logo、系统全称、`<a-card>`包裹的登录表单、`<a-input>` (用户名)、`<a-input-password>` (密码)、`<a-button type="primary" block>` (登录按钮)。
    *   **交互：** 表单校验（必填），登录请求期间按钮显示加载状态。支持企业微信/OA扫码单点登录入口。

*   **1.2 工作台/管理驾驶舱 (`/dashboard`)**
    *   **布局：** 采用 `<a-row>` 和 `<a-col>` 进行栅格布局，由多个 `<a-card>` 组成。
    *   **通用组件：**
        *   **待办事项卡片：** 使用 `<a-list>` 展示当前用户的待审批任务列表，包含单据类型、标题、申请人、申请时间。点击列表项可跳转至审批详情页。
        *   **快捷入口卡片：** 一组带有图标的 `<a-button>` 或卡片链接，如“我要报销”、“发起合同”、“采购申请”。
        *   **通知公告卡片：** 滚动或分页展示最新公告。
    *   **领导视图组件 (管理驾驶舱):**
        *   **预算执行总览：** 使用 `<a-progress type="dashboard">` 仪表盘展示全院年度预算执行进度。
        *   **支出分类统计：** 使用 **ECharts饼图** 展示本月/本年各项费用的支出占比。
        *   **科室支出排行：** 使用 **ECharts条形图** 展示各科室支出金额TOP 10。

#### **💰 模块二：全面预算管理 (Budget Management)**

*   **2.1 预算编制页 (`/budget/compile`)**
    *   **布局：** 采用 `<a-steps>` 引导用户完成多步流程：`选择方案` -> `科室填报` -> `归口审核` -> `委员会审批`。
    *   **科室填报视图：**
        *   使用 `<a-table>` 展示本科室的预算事项（行）和月份（列），金额输入框内嵌在表格中。
        *   表格需支持**历史数据**和**系统建议值**的展示，作为填报参考。
        *   提供“暂存”和“提交审批”按钮。

*   **2.2 预算分析页 (`/budget/analysis`)**
    *   **布局：** 顶部为筛选区，下方为图表和数据表格。
    *   **筛选区组件：** `<a-select>` (预算年度)、`<a-tree-select>` (部门/科室)、`<a-range-picker>` (月份范围)。
    *   **数据展示：**
        *   **核心指标卡片：** 使用 `<a-statistic>` 展示预算总额、执行总额、执行进度、同比/环比。
        *   **趋势图：** 使用 **ECharts折线图** 展示预算与实际执行金额的月度对比。
        *   **数据明细表：** 使用 `<a-table>` 展示各部门各预算项目的详细执行情况。支持**下钻功能**，点击某项预算执行数，弹出模态框展示关联的支出单据列表。

#### **🧾 模块三：支出控制管理 (Expenditure Control)**

*   **3.1 报销申请表单页 (`/expenditure/apply`)**
    *   **布局：** 使用 `<a-card>` 分割为 `基本信息`、`费用明细`、`预算信息`、`附件上传` 等区域。
    *   **关键组件与交互：**
        *   **关联事前申请：** 提供一个 `<a-select>` 或弹出选择框，让用户选择已通过的事前申请单，选择后自动填充大部分信息。
        *   **费用明细表格 (`<a-table>`)：**
            *   支持动态增删行。
            *   **OCR识别按钮：** 表格上方放置一个 `<a-upload>` 按钮，文案为“上传发票并识别”。上传后，调用OCR接口，并将识别结果自动填充到表格新行中。
            *   表格列：费用类型、发生日期、发票号码、金额、税额、备注等。
        *   **预算占用展示：** 自动或手动关联预算后，清晰展示所选预算项目的 `总额`、`可用余额`、`本次占用`。若可用余额不足，需高亮提示。
        *   **审批流程预览：** 提交前，使用 `<a-timeline>` 动态展示该笔报销将流经的审批节点和审批人。

*   **3.2 审批详情页 (`/expenditure/approval/:id`)**
    *   **布局：** 上半部分为只读的报销单详情，下半部分为审批操作区和审批历史。
    *   **组件：**
        *   **报销单详情：** 复用报销申请表单，但所有输入框设为 `disabled`。
        *   **附件预览：** 图片附件支持点击放大预览。
        *   **审批操作区：** `<a-textarea>` (审批意见)、`<a-button type="primary">` (同意)、`<a-button type="danger">` (驳回)。
        *   **审批历史：** 使用 `<a-timeline>` 展示从申请到当前节点的完整审批记录，包括每一步的操作人、时间和意见。

#### **📄 模块四 & 五：采购与合同管理 (Procurement & Contract)**

*   **4.1 合同台账页 (`/contract/list`)**
    *   **布局：** 顶部为高级搜索区，下方为数据表格。
    *   **搜索区组件：** `<a-input>` (合同名称/编号)、`<a-select>` (合同类型/状态)、`<a-input-number>` (金额范围)、`<a-date-picker>` (签订日期)。
    *   **表格 (`<a-table>`) 列：** 合同编号、合同名称、相对方、总金额、已付金额、合同状态、签订日期、到期日期、操作（查看、编辑、发起支付）。
    *   **关键交互：**
        *   **智能提醒：** 对即将到期或即将到达付款节点的合同，在行内用图标或角标进行提示。
        *   **穿透查询：** 点击“已付金额”，弹出模态框展示所有关联的付款记录及对应的报销单号。

*   **4.2 合同创建/编辑页 (`/contract/edit/:id`)**
    *   **布局：** 采用 `<a-tabs>` 分页，如 `基本信息`、`付款计划`、`合同正文与附件`、`关联信息`。
    *   **关键组件：**
        *   **付款计划：** 一个可编辑的表格，让用户设置分期付款的计划日期和金额。
        *   **合同比对功能：** 在“合同正文与附件”Tab下，当用户上传最终盖章版的合同时，提供一个“与审批版比对”按钮。点击后，弹出一个左右分栏的视图，高亮显示两个版本文本内容的差异。

#### **📱 模块六：移动应用平台 (Mobile - Uni-app)**

*   **整体风格：** 简洁、聚焦。避免在移动端展示复杂的表格和图表。
*   **6.1 移动端首页**
    *   **布局：** 卡片式布局。
    *   **组件：** 顶部个人信息，下方为 `待我审批`、`我的申请`、`通知公告` 三个关键入口卡片，并显示数字角标。
*   **6.2 审批列表页**
    *   **组件：** 使用卡片式列表，每个卡片展示单据的核心信息（类型、申请人、金额、事由）和状态。
*   **6.3 移动审批详情页**
    *   **布局：** 采用分段信息展示，避免单页过长。
    *   **交互：** 底部固定一个操作栏，包含“同意”和“驳回”按钮，方便单手操作。
*   **6.4 移动报销申请页**
    *   **布局：** 采用 `<a-steps>` 或全屏页面切换，将PC端复杂的表单拆分为 `填写基本信息` -> `添加费用明细` -> `上传附件` 等多个步骤。
    *   **关键交互：** “添加费用明细”步骤中，提供“拍照识票”按钮，直接调用手机摄像头拍照并进行OCR识别。

---


