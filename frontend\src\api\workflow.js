import request from './request'

// 工作流定义管理
export const getWorkflowDefinitions = (params) => {
  return request.get('/workflow/definitions', { params })
}

export const getWorkflowDefinition = (id) => {
  return request.get(`/workflow/definitions/${id}`)
}

export const createWorkflowDefinition = (data) => {
  return request.post('/workflow/definitions', data)
}

export const updateWorkflowDefinition = (id, data) => {
  return request.put(`/workflow/definitions/${id}`, data)
}

export const deleteWorkflowDefinition = (id) => {
  return request.delete(`/workflow/definitions/${id}`)
}

export const deployWorkflowDefinition = (id) => {
  return request.post(`/workflow/definitions/${id}/deploy`)
}

export const suspendWorkflowDefinition = (id) => {
  return request.post(`/workflow/definitions/${id}/suspend`)
}

// 工作流实例管理
export const getWorkflowInstances = (params) => {
  return request.get('/workflow/instances', { params })
}

export const getWorkflowInstance = (id) => {
  return request.get(`/workflow/instances/${id}`)
}

export const startWorkflowInstance = (data) => {
  return request.post('/workflow/instances/start', data)
}

export const terminateWorkflowInstance = (id, reason) => {
  return request.post(`/workflow/instances/${id}/terminate`, { reason })
}

export const getWorkflowInstanceHistory = (id) => {
  return request.get(`/workflow/instances/${id}/history`)
}

// 工作流任务管理
export const getWorkflowTasks = (params) => {
  return request.get('/workflow/tasks', { params })
}

export const getWorkflowTask = (id) => {
  return request.get(`/workflow/tasks/${id}`)
}

export const completeWorkflowTask = (id, data) => {
  return request.post(`/workflow/tasks/${id}/complete`, data)
}

export const claimWorkflowTask = (id) => {
  return request.post(`/workflow/tasks/${id}/claim`)
}

export const unclaimWorkflowTask = (id) => {
  return request.post(`/workflow/tasks/${id}/unclaim`)
}

export const delegateWorkflowTask = (id, userId) => {
  return request.post(`/workflow/tasks/${id}/delegate`, { user_id: userId })
}

// 获取用户待办任务
export const getUserTodoTasks = (params) => {
  return request.get('/workflow/tasks/todo', { params })
}

// 获取用户已办任务
export const getUserDoneTasks = (params) => {
  return request.get('/workflow/tasks/done', { params })
}

// 工作流统计
export const getWorkflowStats = () => {
  return request.get('/workflow/stats')
}

export const getWorkflowDefinitionStats = (id) => {
  return request.get(`/workflow/definitions/${id}/stats`)
}

// 工作流模板
export const getWorkflowTemplates = () => {
  return request.get('/workflow/templates')
}

export const createWorkflowFromTemplate = (templateId, data) => {
  return request.post(`/workflow/templates/${templateId}/create`, data)
}
