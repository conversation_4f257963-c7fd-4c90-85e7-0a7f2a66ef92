<template>
  <div class="audit-log">
    <a-card :bordered="false">
      <template #title>
        <div class="page-header">
          <h3>审计日志</h3>
          <div class="header-actions">
            <a-button @click="exportLogs">
              <template #icon><ExportOutlined /></template>
              导出日志
            </a-button>
            <a-button @click="refreshLogs">
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="操作用户">
            <a-input v-model:value="searchForm.username" placeholder="请输入用户名" />
          </a-form-item>
          <a-form-item label="操作描述">
            <a-input v-model:value="searchForm.action" placeholder="请输入操作描述" />
          </a-form-item>
          <a-form-item label="模块">
            <a-select v-model:value="searchForm.module" placeholder="请选择模块" allow-clear>
              <a-select-option value="user">用户管理</a-select-option>
              <a-select-option value="role">角色管理</a-select-option>
              <a-select-option value="permission">权限管理</a-select-option>
              <a-select-option value="organization">组织管理</a-select-option>
              <a-select-option value="system">系统配置</a-select-option>
              <a-select-option value="workflow">工作流</a-select-option>
              <a-select-option value="budget">预算管理</a-select-option>
              <a-select-option value="expenditure">支出管理</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="操作时间">
            <a-range-picker 
              v-model:value="searchForm.date_range" 
              show-time
              format="YYYY-MM-DD HH:mm:ss"
            />
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon><SearchOutlined /></template>
                搜索
              </a-button>
              <a-button @click="resetSearch">
                <template #icon><ReloadOutlined /></template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 日志表格 -->
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="tableLoading"
        row-key="id"
        @change="handleTableChange"
        :scroll="{ x: 1200 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'user'">
            <div class="user-info">
              <a-avatar :size="24" :icon="h(UserOutlined)" />
              <span style="margin-left: 8px">{{ record.user?.real_name || '系统' }}</span>
            </div>
          </template>
          <template v-else-if="column.key === 'module'">
            <a-tag :color="getModuleColor(record.module)">
              {{ getModuleName(record.module) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'target_info'">
            <div v-if="record.target_type && record.target_id">
              <a-tag color="blue">{{ record.target_type }}</a-tag>
              <span style="margin-left: 4px">{{ record.target_id }}</span>
            </div>
            <span v-else>-</span>
          </template>
          <template v-else-if="column.key === 'ip_address'">
            <a-tag color="geekblue">{{ record.ip_address || '-' }}</a-tag>
          </template>
          <template v-else-if="column.key === 'created_at'">
            {{ formatDate(record.created_at) }}
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a @click="viewDetail(record)">查看详情</a>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 日志详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="审计日志详情"
      :footer="null"
      width="800px"
    >
      <div v-if="currentLog">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="操作用户">
            {{ currentLog.user?.real_name || '系统' }}
          </a-descriptions-item>
          <a-descriptions-item label="操作时间">
            {{ formatDate(currentLog.created_at) }}
          </a-descriptions-item>
          <a-descriptions-item label="操作描述">
            {{ currentLog.action }}
          </a-descriptions-item>
          <a-descriptions-item label="模块">
            <a-tag :color="getModuleColor(currentLog.module)">
              {{ getModuleName(currentLog.module) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="操作对象类型">
            {{ currentLog.target_type || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="操作对象ID">
            {{ currentLog.target_id || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="IP地址">
            {{ currentLog.ip_address || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="用户代理">
            <a-tooltip :title="currentLog.user_agent">
              <span>{{ getUserAgentShort(currentLog.user_agent) }}</span>
            </a-tooltip>
          </a-descriptions-item>
        </a-descriptions>

        <!-- 数据变更对比 -->
        <div v-if="currentLog.old_value || currentLog.new_value" style="margin-top: 16px">
          <h4>数据变更对比</h4>
          <a-row :gutter="16">
            <a-col :span="12">
              <h5>变更前</h5>
              <pre class="json-viewer">{{ formatJson(currentLog.old_value) }}</pre>
            </a-col>
            <a-col :span="12">
              <h5>变更后</h5>
              <pre class="json-viewer">{{ formatJson(currentLog.new_value) }}</pre>
            </a-col>
          </a-row>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  SearchOutlined,
  ReloadOutlined,
  ExportOutlined,
  UserOutlined
} from '@ant-design/icons-vue'
import { getAuditLogs, exportAuditLogs } from '@/api/system'

// 响应式数据
const tableData = ref([])
const tableLoading = ref(false)
const detailModalVisible = ref(false)
const currentLog = ref(null)

// 搜索表单
const searchForm = reactive({
  username: '',
  action: '',
  module: '',
  date_range: null
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  { title: '操作用户', key: 'user', width: 120 },
  { title: '操作描述', dataIndex: 'action', key: 'action', ellipsis: true },
  { title: '模块', key: 'module', width: 100 },
  { title: '操作对象', key: 'target_info', width: 150 },
  { title: 'IP地址', key: 'ip_address', width: 120 },
  { title: '操作时间', key: 'created_at', width: 180 },
  { title: '操作', key: 'action', width: 100, fixed: 'right' }
]

// 获取模块名称
const getModuleName = (module) => {
  const names = {
    user: '用户管理',
    role: '角色管理',
    permission: '权限管理',
    organization: '组织管理',
    system: '系统配置',
    workflow: '工作流',
    budget: '预算管理',
    expenditure: '支出管理'
  }
  return names[module] || module || '未知'
}

// 获取模块颜色
const getModuleColor = (module) => {
  const colors = {
    user: 'blue',
    role: 'green',
    permission: 'orange',
    organization: 'purple',
    system: 'red',
    workflow: 'cyan',
    budget: 'magenta',
    expenditure: 'gold'
  }
  return colors[module] || 'default'
}

// 格式化日期
const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

// 获取用户代理简短信息
const getUserAgentShort = (userAgent) => {
  if (!userAgent) return '-'
  
  // 简单解析浏览器信息
  if (userAgent.includes('Chrome')) return 'Chrome'
  if (userAgent.includes('Firefox')) return 'Firefox'
  if (userAgent.includes('Safari')) return 'Safari'
  if (userAgent.includes('Edge')) return 'Edge'
  
  return userAgent.substring(0, 20) + '...'
}

// 格式化JSON
const formatJson = (jsonData) => {
  if (!jsonData) return ''
  try {
    return JSON.stringify(jsonData, null, 2)
  } catch {
    return String(jsonData)
  }
}

// 加载审计日志
const loadAuditLogs = async () => {
  tableLoading.value = true
  try {
    const params = {
      page: pagination.current,
      page_size: pagination.pageSize,
      username: searchForm.username,
      action: searchForm.action,
      module: searchForm.module
    }
    
    // 处理日期范围
    if (searchForm.date_range && searchForm.date_range.length === 2) {
      params.start_time = searchForm.date_range[0].format('YYYY-MM-DD HH:mm:ss')
      params.end_time = searchForm.date_range[1].format('YYYY-MM-DD HH:mm:ss')
    }
    
    const response = await getAuditLogs(params)
    tableData.value = response.data.list || []
    pagination.total = response.data.total || 0
  } catch (error) {
    message.error('加载审计日志失败')
  } finally {
    tableLoading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadAuditLogs()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    username: '',
    action: '',
    module: '',
    date_range: null
  })
  pagination.current = 1
  loadAuditLogs()
}

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadAuditLogs()
}

// 查看详情
const viewDetail = (log) => {
  currentLog.value = log
  detailModalVisible.value = true
}

// 导出日志
const exportLogs = async () => {
  try {
    const params = {
      username: searchForm.username,
      action: searchForm.action,
      module: searchForm.module
    }
    
    // 处理日期范围
    if (searchForm.date_range && searchForm.date_range.length === 2) {
      params.start_time = searchForm.date_range[0].format('YYYY-MM-DD HH:mm:ss')
      params.end_time = searchForm.date_range[1].format('YYYY-MM-DD HH:mm:ss')
    }
    
    const response = await exportAuditLogs(params)
    
    // 创建下载链接
    const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `audit_logs_${dayjs().format('YYYY-MM-DD')}.xlsx`
    a.click()
    URL.revokeObjectURL(url)
    
    message.success('审计日志导出成功')
  } catch (error) {
    message.error('导出失败')
  }
}

// 刷新日志
const refreshLogs = () => {
  loadAuditLogs()
  message.success('日志已刷新')
}

// 组件挂载时加载数据
onMounted(() => {
  loadAuditLogs()
})
</script>

<style scoped>
.audit-log {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h3 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.user-info {
  display: flex;
  align-items: center;
}

.json-viewer {
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
