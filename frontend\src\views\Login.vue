<template>
  <div class="login-container">
    <!-- 左侧背景区域 -->
    <div class="login-left">
      <div class="login-bg">
        <div class="bg-content">
          <h1>医院质控系统</h1>
          <p>专业的医院质量控制管理平台</p>
          <div class="feature-list">
            <div class="feature-item">
              <div class="feature-icon">✓</div>
              <span>全面的质量管理</span>
            </div>
            <div class="feature-item">
              <div class="feature-icon">✓</div>
              <span>智能化数据分析</span>
            </div>
            <div class="feature-item">
              <div class="feature-icon">✓</div>
              <span>规范化流程控制</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 右侧登录区域 -->
    <div class="login-right">
      <div class="login-box">
        <div class="login-header">
          <h2>用户登录</h2>
          <p>请输入您的账号和密码</p>
        </div>
        
        <a-form
          :model="formData"
          :rules="rules"
          @finish="handleLogin"
          class="login-form"
          size="large"
        >
          <a-form-item name="username">
            <a-input
              v-model:value="formData.username"
              placeholder="请输入用户名"
              :prefix="h(UserOutlined)"
            />
          </a-form-item>
          
          <a-form-item name="password">
            <a-input-password
              v-model:value="formData.password"
              placeholder="请输入密码"
              :prefix="h(LockOutlined)"
              @keyup.enter="handleLogin"
            />
          </a-form-item>
          
          <a-form-item>
            <a-button
              type="primary"
              html-type="submit"
              :loading="loading"
              block
              size="large"
            >
              登录
            </a-button>
          </a-form-item>
        </a-form>

        <!-- 扫码登录选项 -->
        <a-divider>或</a-divider>

        <div class="qrcode-login">
          <a-button
            block
            size="large"
            @click="showQRCodeLogin"
            :icon="h(QrcodeOutlined)"
          >
            企业微信扫码登录
          </a-button>
        </div>

        <div class="login-footer">
          <p>默认账户：admin / password</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, h } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { UserOutlined, LockOutlined, QrcodeOutlined } from '@ant-design/icons-vue'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 表单数据
const formData = reactive({
  username: '',
  password: ''
})

// 加载状态
const loading = ref(false)

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 50, message: '用户名长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 50, message: '密码长度在 6 到 50 个字符', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  loading.value = true

  try {
    const result = await authStore.login(formData)

    if (result.success) {
      message.success('登录成功')
      // 跳转到首页
      router.push('/')
    } else {
      message.error(result.message)
    }
  } catch (error) {
    message.error('登录失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 显示二维码登录
const showQRCodeLogin = () => {
  message.info('企业微信扫码登录功能开发中...')
  // TODO: 实现企业微信扫码登录
  // 1. 获取企业微信登录二维码
  // 2. 显示二维码模态框
  // 3. 轮询检查扫码状态
  // 4. 扫码成功后自动登录
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
}

/* 左侧背景区域 */
.login-left {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.login-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px;
}

.bg-content {
  text-align: center;
  color: white;
}

.bg-content h1 {
  font-size: 48px;
  font-weight: 700;
  margin: 0 0 16px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.bg-content > p {
  font-size: 18px;
  margin: 0 0 40px 0;
  opacity: 0.9;
}

.feature-list {
  text-align: left;
  max-width: 300px;
  margin: 0 auto;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
}

.feature-icon {
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-weight: bold;
}

/* 右侧登录区域 */
.login-right {
  width: 480px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.login-box {
  width: 100%;
  max-width: 360px;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.login-header h2 {
  color: #333;
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.login-header p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.login-form {
  margin-bottom: 24px;
}

.login-form :deep(.ant-input-affix-wrapper) {
  height: 48px;
  border-radius: 8px;
}

.login-form :deep(.ant-btn) {
  height: 48px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
}

.qrcode-login {
  margin: 16px 0;
}

.login-footer {
  text-align: center;
  color: #999;
  font-size: 12px;
}

.login-footer p {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
  }
  
  .login-left {
    min-height: 40vh;
  }
  
  .login-right {
    width: 100%;
    min-height: 60vh;
    padding: 20px;
  }
  
  .bg-content h1 {
    font-size: 32px;
  }
  
  .bg-content > p {
    font-size: 16px;
  }
  
  .feature-list {
    display: none;
  }
}

@media (max-width: 480px) {
  .login-right {
    padding: 16px;
  }
  
  .login-header h2 {
    font-size: 24px;
  }
}
</style>