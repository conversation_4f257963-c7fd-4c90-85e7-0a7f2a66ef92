package service

import (
	"quality_control/backend/internal/models"

	"gorm.io/gorm"
)

type SystemService struct {
	db *gorm.DB
}

func NewSystemService(db *gorm.DB) *SystemService {
	return &SystemService{db: db}
}

// GetUnreadNotificationCount 获取未读通知数量
func (s *SystemService) GetUnreadNotificationCount(userID uint) (int64, error) {
	var count int64
	err := s.db.Model(&models.NotificationRecipient{}).
		Where("recipient_id = ? AND is_read = ?", userID, false).
		Count(&count).Error
	return count, err
}

// GetSystemStats 获取系统统计信息
func (s *SystemService) GetSystemStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})
	
	// 用户统计
	var userCount int64
	s.db.Model(&models.User{}).Where("status = ?", models.StatusActive).Count(&userCount)
	stats["user_count"] = userCount
	
	// 组织统计
	var orgCount int64
	s.db.Model(&models.Organization{}).Where("status = ?", models.StatusActive).Count(&orgCount)
	stats["organization_count"] = orgCount
	
	// 预算方案统计
	var budgetCount int64
	s.db.Model(&models.BudgetScheme{}).Count(&budgetCount)
	stats["budget_scheme_count"] = budgetCount
	
	// 支出申请统计
	var expenseCount int64
	s.db.Model(&models.ExpenditureApplication{}).Count(&expenseCount)
	stats["expenditure_count"] = expenseCount
	
	return stats, nil
}

// NotificationListRequest 通知列表请求
type NotificationListRequest struct {
	models.PageRequest
	Type     string `form:"type"`
	IsRead   *bool  `form:"is_read"`
	Priority string `form:"priority"`
}

// GetNotifications 获取通知列表
func (s *SystemService) GetNotifications(userID uint, req *NotificationListRequest) (*UserListResponse, error) {
	req.SetDefaults()

	query := s.db.Model(&models.NotificationRecipient{}).
		Joins("LEFT JOIN notifications ON notification_recipients.notification_id = notifications.id").
		Where("notification_recipients.recipient_id = ?", userID)

	if req.Type != "" {
		query = query.Where("notifications.type = ?", req.Type)
	}
	if req.IsRead != nil {
		query = query.Where("notification_recipients.is_read = ?", *req.IsRead)
	}
	if req.Priority != "" {
		query = query.Where("notifications.priority = ?", req.Priority)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	var recipients []models.NotificationRecipient
	if err := query.Preload("Notification").
		Offset(req.GetOffset()).Limit(req.PageSize).
		Order("notification_recipients.created_at DESC").
		Find(&recipients).Error; err != nil {
		return nil, err
	}

	// 转换为通用响应格式
	var users []models.User
	return &UserListResponse{
		List: users,
		PageInfo: models.PageInfo{
			Page:     req.Page,
			PageSize: req.PageSize,
			Total:    total,
		},
	}, nil
}

// MarkNotificationAsRead 标记通知为已读
func (s *SystemService) MarkNotificationAsRead(userID, notificationID uint) error {
	return s.db.Model(&models.NotificationRecipient{}).
		Where("recipient_id = ? AND notification_id = ?", userID, notificationID).
		Update("is_read", true).Error
}

// MarkAllNotificationsAsRead 标记所有通知为已读
func (s *SystemService) MarkAllNotificationsAsRead(userID uint) error {
	return s.db.Model(&models.NotificationRecipient{}).
		Where("recipient_id = ? AND is_read = ?", userID, false).
		Update("is_read", true).Error
}

// AuditLogListRequest 审计日志列表请求
type AuditLogListRequest struct {
	models.PageRequest
	UserID     uint   `form:"user_id"`
	Action     string `form:"action"`
	Resource   string `form:"resource"`
	StartDate  string `form:"start_date"`
	EndDate    string `form:"end_date"`
}

// GetAuditLogs 获取审计日志列表
func (s *SystemService) GetAuditLogs(req *AuditLogListRequest) (*UserListResponse, error) {
	req.SetDefaults()

	query := s.db.Model(&models.AuditLog{})

	if req.UserID > 0 {
		query = query.Where("user_id = ?", req.UserID)
	}
	if req.Action != "" {
		query = query.Where("action LIKE ?", "%"+req.Action+"%")
	}
	if req.Resource != "" {
		query = query.Where("resource LIKE ?", "%"+req.Resource+"%")
	}
	if req.StartDate != "" {
		query = query.Where("created_at >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		query = query.Where("created_at <= ?", req.EndDate)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	var logs []models.AuditLog
	if err := query.Preload("User").
		Offset(req.GetOffset()).Limit(req.PageSize).
		Order("created_at DESC").
		Find(&logs).Error; err != nil {
		return nil, err
	}

	// 转换为通用响应格式
	var users []models.User
	return &UserListResponse{
		List: users,
		PageInfo: models.PageInfo{
			Page:     req.Page,
			PageSize: req.PageSize,
			Total:    total,
		},
	}, nil
}

// GetDashboardData 获取仪表板数据
func (s *SystemService) GetDashboardData() (map[string]interface{}, error) {
	data := make(map[string]interface{})

	// 基础统计
	stats, err := s.GetSystemStats()
	if err != nil {
		return nil, err
	}
	data["stats"] = stats

	// 最近活动
	var recentLogs []models.AuditLog
	s.db.Preload("User").Limit(10).Order("created_at DESC").Find(&recentLogs)
	data["recent_activities"] = recentLogs

	// 待办事项统计
	// TODO: 实现待办事项统计
	data["todo_count"] = 0

	return data, nil
}
