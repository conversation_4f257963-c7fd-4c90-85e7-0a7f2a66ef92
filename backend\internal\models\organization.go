package models

// Organization 组织架构模型
type Organization struct {
	BaseModel
	Name        string `gorm:"size:100;not null" json:"name"`
	Code        string `gorm:"uniqueIndex;size:50;not null" json:"code"`
	Type        string `gorm:"size:20;not null" json:"type"` // hospital, department, section
	ParentID    *uint  `gorm:"index" json:"parent_id"`
	Level       int    `gorm:"default:1" json:"level"`
	Sort        int    `gorm:"default:0" json:"sort"`
	Description string `gorm:"size:255" json:"description"`
	Status      Status `gorm:"size:20;default:'active'" json:"status"`
	
	// 负责人信息
	ManagerID   *uint  `gorm:"index" json:"manager_id"`
	Manager     *User  `gorm:"foreignKey:ManagerID" json:"manager,omitempty"`
	
	// 联系信息
	Phone       string `gorm:"size:20" json:"phone"`
	Email       string `gorm:"size:100" json:"email"`
	Address     string `gorm:"size:255" json:"address"`
	
	// 自关联
	Parent   *Organization  `gorm:"foreignKey:ParentID" json:"parent,omitempty"`
	Children []Organization `gorm:"foreignKey:ParentID" json:"children,omitempty"`
	
	// 关联用户和岗位
	Users     []User     `gorm:"foreignKey:OrganizationID" json:"users,omitempty"`
	Positions []Position `gorm:"foreignKey:OrganizationID" json:"positions,omitempty"`
}

// TableName 指定表名
func (Organization) TableName() string {
	return "organizations"
}

// CostCenter 成本中心模型
type CostCenter struct {
	BaseModel
	Name           string `gorm:"size:100;not null" json:"name"`
	Code           string `gorm:"uniqueIndex;size:50;not null" json:"code"`
	Type           string `gorm:"size:20;not null" json:"type"` // revenue, cost, profit
	OrganizationID uint   `gorm:"index" json:"organization_id"`
	ManagerID      *uint  `gorm:"index" json:"manager_id"`
	Status         Status `gorm:"size:20;default:'active'" json:"status"`
	Description    string `gorm:"size:255" json:"description"`
	
	// 关联字段
	Organization Organization `gorm:"foreignKey:OrganizationID" json:"organization,omitempty"`
	Manager      *User        `gorm:"foreignKey:ManagerID" json:"manager,omitempty"`
}

// TableName 指定表名
func (CostCenter) TableName() string {
	return "cost_centers"
}
