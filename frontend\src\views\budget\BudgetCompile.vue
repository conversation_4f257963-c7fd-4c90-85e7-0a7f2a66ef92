<template>
  <div class="budget-compile">
    <a-card :bordered="false">
      <template #title>
        <div class="page-header">
          <h3>预算编制</h3>
          <div class="header-actions">
            <a-button @click="goBack">
              <template #icon><ArrowLeftOutlined /></template>
              返回
            </a-button>
          </div>
        </div>
      </template>

      <!-- 步骤导航 -->
      <a-steps :current="currentStep" class="budget-steps">
        <a-step title="选择方案" description="选择预算编制方案" />
        <a-step title="科室填报" description="各科室填报预算数据" />
        <a-step title="归口审核" description="归口部门审核预算" />
        <a-step title="委员会审批" description="预算委员会最终审批" />
      </a-steps>

      <!-- 步骤内容 -->
      <div class="step-content">
        <!-- 步骤1：选择方案 -->
        <div v-if="currentStep === 0" class="step-scheme">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-card title="预算方案信息" size="small">
                <a-form layout="vertical" :model="schemeForm">
                  <a-form-item label="预算年度" name="year">
                    <a-select v-model:value="schemeForm.year" placeholder="请选择预算年度">
                      <a-select-option :value="2024">2024年</a-select-option>
                      <a-select-option :value="2025">2025年</a-select-option>
                      <a-select-option :value="2026">2026年</a-select-option>
                    </a-select>
                  </a-form-item>
                  
                  <a-form-item label="编制方案" name="scheme_id">
                    <a-select v-model:value="schemeForm.scheme_id" placeholder="请选择编制方案">
                      <a-select-option v-for="scheme in budgetSchemes" :key="scheme.id" :value="scheme.id">
                        {{ scheme.name }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                  
                  <a-form-item label="编制说明" name="description">
                    <a-textarea v-model:value="schemeForm.description" placeholder="请输入编制说明" :rows="4" />
                  </a-form-item>
                </a-form>
              </a-card>
            </a-col>
            
            <a-col :span="12">
              <a-card title="方案详情" size="small">
                <div v-if="selectedScheme">
                  <a-descriptions :column="1" size="small">
                    <a-descriptions-item label="方案名称">{{ selectedScheme.name }}</a-descriptions-item>
                    <a-descriptions-item label="编制周期">{{ selectedScheme.cycle }}</a-descriptions-item>
                    <a-descriptions-item label="预算类型">{{ selectedScheme.budget_type }}</a-descriptions-item>
                    <a-descriptions-item label="方案说明">{{ selectedScheme.description }}</a-descriptions-item>
                  </a-descriptions>
                  
                  <a-divider />
                  
                  <h4>预算科目</h4>
                  <a-tree
                    :tree-data="selectedScheme.budget_items"
                    :field-names="{ children: 'children', title: 'name', key: 'id' }"
                    :selectable="false"
                    :show-line="true"
                  />
                </div>
                <a-empty v-else description="请选择编制方案" />
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 步骤2：科室填报 -->
        <div v-if="currentStep === 1" class="step-department">
          <div class="department-header">
            <a-row :gutter="16" align="middle">
              <a-col :span="8">
                <a-select v-model:value="selectedDepartment" placeholder="选择科室" style="width: 100%">
                  <a-select-option v-for="dept in departments" :key="dept.id" :value="dept.id">
                    {{ dept.name }}
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :span="8">
                <a-space>
                  <a-button @click="loadHistoryData">
                    <template #icon><HistoryOutlined /></template>
                    加载历史数据
                  </a-button>
                  <a-button @click="loadSuggestedData">
                    <template #icon><BulbOutlined /></template>
                    系统建议值
                  </a-button>
                </a-space>
              </a-col>
              <a-col :span="8" style="text-align: right;">
                <a-space>
                  <a-button @click="saveDraft">
                    <template #icon><SaveOutlined /></template>
                    暂存
                  </a-button>
                  <a-button type="primary" @click="submitForApproval">
                    <template #icon><SendOutlined /></template>
                    提交审批
                  </a-button>
                </a-space>
              </a-col>
            </a-row>
          </div>

          <!-- 预算填报表格 -->
          <div class="budget-table-container">
            <a-table
              :columns="budgetColumns"
              :data-source="budgetData"
              :pagination="false"
              :scroll="{ x: 1500, y: 600 }"
              bordered
              size="small"
              class="budget-table"
            >
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key.startsWith('month_')">
                  <a-input-number
                    v-model:value="record[column.key]"
                    :min="0"
                    :precision="2"
                    :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                    :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                    style="width: 100%"
                    size="small"
                    @change="calculateTotal(record)"
                  />
                </template>
                <template v-else-if="column.key === 'total'">
                  <span class="total-amount">{{ formatMoney(record.total) }}</span>
                </template>
                <template v-else-if="column.key === 'history'">
                  <a-tooltip :title="`去年同期：${formatMoney(record.history_amount)}`">
                    <span class="history-amount">{{ formatMoney(record.history_amount) }}</span>
                  </a-tooltip>
                </template>
                <template v-else-if="column.key === 'suggested'">
                  <a-tooltip :title="`系统建议：${formatMoney(record.suggested_amount)}`">
                    <a-button size="small" type="link" @click="applySuggested(record)">
                      {{ formatMoney(record.suggested_amount) }}
                    </a-button>
                  </a-tooltip>
                </template>
              </template>
            </a-table>
          </div>

          <!-- 汇总信息 -->
          <div class="budget-summary">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-statistic title="本年度预算总额" :value="budgetSummary.total" :precision="2" suffix="万元" />
              </a-col>
              <a-col :span="6">
                <a-statistic title="去年同期金额" :value="budgetSummary.history" :precision="2" suffix="万元" />
              </a-col>
              <a-col :span="6">
                <a-statistic title="同比增长率" :value="budgetSummary.growth_rate" :precision="2" suffix="%" />
              </a-col>
              <a-col :span="6">
                <a-statistic title="填报完成度" :value="budgetSummary.completion_rate" :precision="1" suffix="%" />
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 步骤3：归口审核 -->
        <div v-if="currentStep === 2" class="step-review">
          <a-card title="归口审核" size="small">
            <a-table
              :columns="reviewColumns"
              :data-source="reviewData"
              :pagination="false"
              row-key="id"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'status'">
                  <a-tag :color="getStatusColor(record.status)">
                    {{ getStatusName(record.status) }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === 'action'">
                  <a-space>
                    <a @click="viewBudgetDetail(record)">查看</a>
                    <a @click="reviewBudget(record)" v-if="record.status === 'pending'">审核</a>
                  </a-space>
                </template>
              </template>
            </a-table>
          </a-card>
        </div>

        <!-- 步骤4：委员会审批 -->
        <div v-if="currentStep === 3" class="step-approval">
          <a-card title="委员会审批" size="small">
            <a-result
              status="success"
              title="预算编制完成"
              sub-title="所有科室预算已通过委员会审批，预算编制流程已完成。"
            >
              <template #extra>
                <a-space>
                  <a-button type="primary" @click="downloadBudget">下载预算报告</a-button>
                  <a-button @click="viewBudgetReport">查看预算报告</a-button>
                </a-space>
              </template>
            </a-result>
          </a-card>
        </div>
      </div>

      <!-- 步骤操作按钮 -->
      <div class="step-actions">
        <a-space>
          <a-button v-if="currentStep > 0" @click="prevStep">上一步</a-button>
          <a-button v-if="currentStep < 3" type="primary" @click="nextStep">下一步</a-button>
        </a-space>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  HistoryOutlined,
  BulbOutlined,
  SaveOutlined,
  SendOutlined
} from '@ant-design/icons-vue'
import {
  getBudgetSchemes,
  getDepartments,
  getBudgetData,
  saveBudgetDraft,
  submitBudgetForApproval,
  getHistoryBudgetData,
  getSuggestedBudgetData
} from '@/api/budget'

const router = useRouter()

// 响应式数据
const currentStep = ref(0)
const budgetSchemes = ref([])
const departments = ref([])
const budgetData = ref([])
const reviewData = ref([])
const selectedDepartment = ref(null)

// 表单数据
const schemeForm = reactive({
  year: new Date().getFullYear() + 1,
  scheme_id: null,
  description: ''
})

// 计算属性
const selectedScheme = computed(() => {
  return budgetSchemes.value.find(scheme => scheme.id === schemeForm.scheme_id)
})

const budgetSummary = computed(() => {
  const total = budgetData.value.reduce((sum, item) => sum + (item.total || 0), 0)
  const history = budgetData.value.reduce((sum, item) => sum + (item.history_amount || 0), 0)
  const growth_rate = history > 0 ? ((total - history) / history * 100) : 0
  const completion_rate = budgetData.value.length > 0 ? 
    (budgetData.value.filter(item => item.total > 0).length / budgetData.value.length * 100) : 0
  
  return {
    total: total / 10000, // 转换为万元
    history: history / 10000,
    growth_rate,
    completion_rate
  }
})

// 预算表格列配置
const budgetColumns = [
  { title: '预算科目', dataIndex: 'item_name', key: 'item_name', width: 200, fixed: 'left' },
  { title: '1月', key: 'month_1', width: 100 },
  { title: '2月', key: 'month_2', width: 100 },
  { title: '3月', key: 'month_3', width: 100 },
  { title: '4月', key: 'month_4', width: 100 },
  { title: '5月', key: 'month_5', width: 100 },
  { title: '6月', key: 'month_6', width: 100 },
  { title: '7月', key: 'month_7', width: 100 },
  { title: '8月', key: 'month_8', width: 100 },
  { title: '9月', key: 'month_9', width: 100 },
  { title: '10月', key: 'month_10', width: 100 },
  { title: '11月', key: 'month_11', width: 100 },
  { title: '12月', key: 'month_12', width: 100 },
  { title: '合计', key: 'total', width: 120, fixed: 'right' },
  { title: '历史数据', key: 'history', width: 100, fixed: 'right' },
  { title: '系统建议', key: 'suggested', width: 100, fixed: 'right' }
]

// 审核表格列配置
const reviewColumns = [
  { title: '科室名称', dataIndex: 'department_name', key: 'department_name' },
  { title: '预算总额', dataIndex: 'total_amount', key: 'total_amount' },
  { title: '提交时间', dataIndex: 'submit_time', key: 'submit_time' },
  { title: '状态', key: 'status' },
  { title: '操作', key: 'action', width: 150 }
]

// 工具函数
const formatMoney = (amount) => {
  if (!amount) return '0'
  return (amount / 10000).toFixed(2) + '万'
}

const getStatusColor = (status) => {
  const colors = {
    'pending': 'orange',
    'approved': 'green',
    'rejected': 'red'
  }
  return colors[status] || 'default'
}

const getStatusName = (status) => {
  const names = {
    'pending': '待审核',
    'approved': '已通过',
    'rejected': '已驳回'
  }
  return names[status] || '未知'
}

// 数据加载函数
const loadBudgetSchemes = async () => {
  try {
    const response = await getBudgetSchemes()
    budgetSchemes.value = response.data || []
  } catch (error) {
    message.error('加载预算方案失败')
  }
}

const loadDepartments = async () => {
  try {
    const response = await getDepartments()
    departments.value = response.data || []
  } catch (error) {
    message.error('加载科室列表失败')
  }
}

const loadBudgetData = async () => {
  if (!selectedDepartment.value || !schemeForm.scheme_id) return
  
  try {
    const response = await getBudgetData({
      department_id: selectedDepartment.value,
      scheme_id: schemeForm.scheme_id,
      year: schemeForm.year
    })
    budgetData.value = response.data || []
  } catch (error) {
    message.error('加载预算数据失败')
  }
}

// 事件处理函数
const goBack = () => {
  router.go(-1)
}

const nextStep = () => {
  if (currentStep.value < 3) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const calculateTotal = (record) => {
  let total = 0
  for (let i = 1; i <= 12; i++) {
    total += record[`month_${i}`] || 0
  }
  record.total = total
}

const loadHistoryData = async () => {
  try {
    const response = await getHistoryBudgetData({
      department_id: selectedDepartment.value,
      year: schemeForm.year - 1
    })
    
    const historyData = response.data || []
    budgetData.value.forEach(item => {
      const historyItem = historyData.find(h => h.item_id === item.item_id)
      if (historyItem) {
        item.history_amount = historyItem.total_amount
      }
    })
    
    message.success('历史数据加载成功')
  } catch (error) {
    message.error('加载历史数据失败')
  }
}

const loadSuggestedData = async () => {
  try {
    const response = await getSuggestedBudgetData({
      department_id: selectedDepartment.value,
      scheme_id: schemeForm.scheme_id
    })
    
    const suggestedData = response.data || []
    budgetData.value.forEach(item => {
      const suggestedItem = suggestedData.find(s => s.item_id === item.item_id)
      if (suggestedItem) {
        item.suggested_amount = suggestedItem.suggested_amount
      }
    })
    
    message.success('系统建议值加载成功')
  } catch (error) {
    message.error('加载系统建议值失败')
  }
}

const applySuggested = (record) => {
  if (record.suggested_amount) {
    // 将建议值平均分配到12个月
    const monthlyAmount = Math.round(record.suggested_amount / 12)
    for (let i = 1; i <= 12; i++) {
      record[`month_${i}`] = monthlyAmount
    }
    calculateTotal(record)
    message.success('已应用系统建议值')
  }
}

const saveDraft = async () => {
  try {
    await saveBudgetDraft({
      department_id: selectedDepartment.value,
      scheme_id: schemeForm.scheme_id,
      year: schemeForm.year,
      budget_data: budgetData.value
    })
    message.success('暂存成功')
  } catch (error) {
    message.error('暂存失败')
  }
}

const submitForApproval = async () => {
  try {
    await submitBudgetForApproval({
      department_id: selectedDepartment.value,
      scheme_id: schemeForm.scheme_id,
      year: schemeForm.year,
      budget_data: budgetData.value
    })
    message.success('提交审批成功')
  } catch (error) {
    message.error('提交失败')
  }
}

const viewBudgetDetail = (record) => {
  message.info('查看预算详情功能开发中...')
}

const reviewBudget = (record) => {
  message.info('预算审核功能开发中...')
}

const downloadBudget = () => {
  message.info('下载预算报告功能开发中...')
}

const viewBudgetReport = () => {
  message.info('查看预算报告功能开发中...')
}

// 监听器
watch(() => selectedDepartment.value, () => {
  if (selectedDepartment.value) {
    loadBudgetData()
  }
})

watch(() => schemeForm.scheme_id, () => {
  if (schemeForm.scheme_id && selectedDepartment.value) {
    loadBudgetData()
  }
})

// 组件挂载时加载数据
onMounted(() => {
  loadBudgetSchemes()
  loadDepartments()
})
</script>

<style scoped>
.budget-compile {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h3 {
  margin: 0;
}

.budget-steps {
  margin: 24px 0;
}

.step-content {
  margin: 32px 0;
  min-height: 400px;
}

.department-header {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.budget-table-container {
  margin: 16px 0;
}

.budget-table :deep(.ant-table-cell) {
  padding: 4px 8px !important;
}

.total-amount {
  font-weight: bold;
  color: #1890ff;
}

.history-amount {
  color: #666;
  font-size: 12px;
}

.budget-summary {
  margin-top: 24px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 6px;
}

.step-actions {
  margin-top: 24px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .budget-compile {
    padding: 16px;
  }
  
  .budget-table-container {
    overflow-x: auto;
  }
}
</style>
