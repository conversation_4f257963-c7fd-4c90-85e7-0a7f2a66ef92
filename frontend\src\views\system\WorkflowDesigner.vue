<template>
  <div class="workflow-designer">
    <a-card :bordered="false">
      <template #title>
        <div class="page-header">
          <h3>工作流设计器</h3>
          <div class="header-actions">
            <a-button type="primary" @click="showCreateModal">
              <template #icon><PlusOutlined /></template>
              新建流程
            </a-button>
            <a-button @click="importWorkflow">
              <template #icon><ImportOutlined /></template>
              导入流程
            </a-button>
          </div>
        </div>
      </template>

      <!-- 工作流列表 -->
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="tableLoading"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === 1 ? 'green' : 'red'">
              {{ record.status === 1 ? '启用' : '禁用' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'version'">
            <a-tag color="blue">v{{ record.version }}</a-tag>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a @click="designWorkflow(record)">设计</a>
              <a @click="previewWorkflow(record)">预览</a>
              <a @click="deployWorkflow(record)" v-if="record.status === 0">部署</a>
              <a @click="suspendWorkflow(record)" v-if="record.status === 1">暂停</a>
              <a-dropdown>
                <a>更多 <DownOutlined /></a>
                <template #overlay>
                  <a-menu @click="({ key }) => handleMoreAction(key, record)">
                    <a-menu-item key="copy">复制流程</a-menu-item>
                    <a-menu-item key="export">导出流程</a-menu-item>
                    <a-menu-item key="statistics">流程统计</a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" style="color: #ff4d4f">删除</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新建流程模态框 -->
    <a-modal
      v-model:open="modalVisible"
      title="新建工作流"
      :confirm-loading="modalLoading"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-form-item label="流程名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入流程名称" />
        </a-form-item>
        
        <a-form-item label="流程编码" name="code">
          <a-input v-model:value="formData.code" placeholder="请输入流程编码" />
        </a-form-item>
        
        <a-form-item label="流程描述" name="description">
          <a-textarea v-model:value="formData.description" placeholder="请输入流程描述" :rows="3" />
        </a-form-item>
        
        <a-form-item label="流程模板" name="template">
          <a-select v-model:value="formData.template" placeholder="请选择流程模板" allow-clear>
            <a-select-option value="simple_approval">简单审批流程</a-select-option>
            <a-select-option value="multi_level_approval">多级审批流程</a-select-option>
            <a-select-option value="parallel_approval">并行审批流程</a-select-option>
            <a-select-option value="conditional_approval">条件审批流程</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 流程设计器模态框 -->
    <a-modal
      v-model:open="designerModalVisible"
      title="工作流设计器"
      :footer="null"
      width="90%"
      :body-style="{ height: '70vh', padding: 0 }"
    >
      <div class="workflow-designer-container">
        <div class="designer-toolbar">
          <a-space>
            <a-button type="primary" @click="saveWorkflow">
              <template #icon><SaveOutlined /></template>
              保存
            </a-button>
            <a-button @click="validateWorkflow">
              <template #icon><CheckOutlined /></template>
              验证
            </a-button>
            <a-button @click="previewWorkflow">
              <template #icon><EyeOutlined /></template>
              预览
            </a-button>
            <a-divider type="vertical" />
            <a-button @click="addStartNode">开始节点</a-button>
            <a-button @click="addApprovalNode">审批节点</a-button>
            <a-button @click="addConditionNode">条件节点</a-button>
            <a-button @click="addEndNode">结束节点</a-button>
          </a-space>
        </div>
        
        <div class="designer-canvas">
          <div class="canvas-container" ref="canvasRef">
            <!-- 这里将来可以集成流程设计器组件，如 bpmn-js 或自定义画布 -->
            <div class="placeholder">
              <a-empty description="工作流设计器">
                <template #image>
                  <PartitionOutlined style="font-size: 64px; color: #ccc;" />
                </template>
                <p>拖拽左侧节点到此处开始设计工作流</p>
              </a-empty>
            </div>
          </div>
        </div>
        
        <div class="designer-properties">
          <a-card title="节点属性" size="small">
            <div v-if="selectedNode">
              <a-form layout="vertical" size="small">
                <a-form-item label="节点名称">
                  <a-input v-model:value="selectedNode.name" />
                </a-form-item>
                <a-form-item label="节点类型">
                  <a-select v-model:value="selectedNode.type" disabled>
                    <a-select-option value="start">开始节点</a-select-option>
                    <a-select-option value="approval">审批节点</a-select-option>
                    <a-select-option value="condition">条件节点</a-select-option>
                    <a-select-option value="end">结束节点</a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item label="处理人" v-if="selectedNode.type === 'approval'">
                  <a-select v-model:value="selectedNode.assignee" mode="multiple">
                    <a-select-option value="role:admin">管理员角色</a-select-option>
                    <a-select-option value="role:manager">经理角色</a-select-option>
                    <a-select-option value="user:specific">指定用户</a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item label="条件表达式" v-if="selectedNode.type === 'condition'">
                  <a-textarea v-model:value="selectedNode.condition" :rows="3" />
                </a-form-item>
              </a-form>
            </div>
            <div v-else>
              <a-empty description="请选择一个节点" />
            </div>
          </a-card>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ImportOutlined,
  DownOutlined,
  SaveOutlined,
  CheckOutlined,
  EyeOutlined,
  PartitionOutlined
} from '@ant-design/icons-vue'
import {
  getWorkflowDefinitions,
  createWorkflowDefinition,
  updateWorkflowDefinition,
  deleteWorkflowDefinition,
  deployWorkflowDefinition,
  suspendWorkflowDefinition
} from '@/api/workflow'

// 响应式数据
const tableData = ref([])
const tableLoading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const designerModalVisible = ref(false)
const formRef = ref()
const canvasRef = ref()
const selectedNode = ref(null)
const currentWorkflow = ref(null)

// 表单数据
const formData = reactive({
  name: '',
  code: '',
  description: '',
  template: ''
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  { title: '流程名称', dataIndex: 'name', key: 'name' },
  { title: '流程编码', dataIndex: 'code', key: 'code' },
  { title: '版本', key: 'version', width: 80 },
  { title: '状态', key: 'status', width: 80 },
  { title: '描述', dataIndex: 'description', key: 'description', ellipsis: true },
  { title: '创建时间', dataIndex: 'created_at', key: 'created_at', width: 180 },
  { title: '操作', key: 'action', width: 200, fixed: 'right' }
]

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入流程名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入流程编码', trigger: 'blur' }]
}

// 加载工作流列表
const loadWorkflows = async () => {
  tableLoading.value = true
  try {
    const params = {
      page: pagination.current,
      page_size: pagination.pageSize
    }
    
    const response = await getWorkflowDefinitions(params)
    tableData.value = response.data.list || []
    pagination.total = response.data.total || 0
  } catch (error) {
    message.error('加载工作流列表失败')
  } finally {
    tableLoading.value = false
  }
}

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadWorkflows()
}

// 显示创建模态框
const showCreateModal = () => {
  resetForm()
  modalVisible.value = true
}

// 设计工作流
const designWorkflow = (workflow) => {
  currentWorkflow.value = workflow
  designerModalVisible.value = true
  selectedNode.value = null
}

// 预览工作流
const previewWorkflow = (workflow) => {
  message.info('预览功能开发中...')
}

// 部署工作流
const deployWorkflow = async (workflow) => {
  Modal.confirm({
    title: '部署工作流',
    content: `确定要部署工作流"${workflow.name}"吗？部署后将生效。`,
    onOk: async () => {
      try {
        await deployWorkflowDefinition(workflow.id)
        message.success('部署成功')
        loadWorkflows()
      } catch (error) {
        message.error('部署失败')
      }
    }
  })
}

// 暂停工作流
const suspendWorkflow = async (workflow) => {
  Modal.confirm({
    title: '暂停工作流',
    content: `确定要暂停工作流"${workflow.name}"吗？暂停后将不再生效。`,
    onOk: async () => {
      try {
        await suspendWorkflowDefinition(workflow.id)
        message.success('暂停成功')
        loadWorkflows()
      } catch (error) {
        message.error('暂停失败')
      }
    }
  })
}

// 更多操作
const handleMoreAction = async (key, workflow) => {
  switch (key) {
    case 'copy':
      copyWorkflow(workflow)
      break
    case 'export':
      exportWorkflow(workflow)
      break
    case 'statistics':
      viewStatistics(workflow)
      break
    case 'delete':
      deleteWorkflowConfirm(workflow)
      break
  }
}

// 复制工作流
const copyWorkflow = (workflow) => {
  Object.assign(formData, {
    name: `${workflow.name}_副本`,
    code: `${workflow.code}_copy`,
    description: workflow.description,
    template: ''
  })
  modalVisible.value = true
}

// 导出工作流
const exportWorkflow = (workflow) => {
  message.info('导出功能开发中...')
}

// 查看统计
const viewStatistics = (workflow) => {
  message.info('统计功能开发中...')
}

// 删除工作流确认
const deleteWorkflowConfirm = (workflow) => {
  Modal.confirm({
    title: '删除工作流',
    content: `确定要删除工作流"${workflow.name}"吗？删除后不可恢复。`,
    onOk: async () => {
      try {
        await deleteWorkflowDefinition(workflow.id)
        message.success('删除成功')
        loadWorkflows()
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

// 导入工作流
const importWorkflow = () => {
  message.info('导入功能开发中...')
}

// 保存工作流
const saveWorkflow = () => {
  message.info('保存功能开发中...')
}

// 验证工作流
const validateWorkflow = () => {
  message.info('验证功能开发中...')
}

// 添加节点方法
const addStartNode = () => {
  selectedNode.value = {
    type: 'start',
    name: '开始节点',
    id: Date.now()
  }
  message.success('添加开始节点')
}

const addApprovalNode = () => {
  selectedNode.value = {
    type: 'approval',
    name: '审批节点',
    id: Date.now(),
    assignee: []
  }
  message.success('添加审批节点')
}

const addConditionNode = () => {
  selectedNode.value = {
    type: 'condition',
    name: '条件节点',
    id: Date.now(),
    condition: ''
  }
  message.success('添加条件节点')
}

const addEndNode = () => {
  selectedNode.value = {
    type: 'end',
    name: '结束节点',
    id: Date.now()
  }
  message.success('添加结束节点')
}

// 处理模态框确认
const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    modalLoading.value = true
    
    const workflowData = {
      ...formData,
      definition: {
        nodes: [],
        edges: []
      }
    }
    
    await createWorkflowDefinition(workflowData)
    message.success('创建成功')
    
    modalVisible.value = false
    loadWorkflows()
  } catch (error) {
    if (error.errorFields) {
      message.error('请检查表单输入')
    } else {
      message.error('创建失败')
    }
  } finally {
    modalLoading.value = false
  }
}

// 处理模态框取消
const handleModalCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    code: '',
    description: '',
    template: ''
  })
  formRef.value?.resetFields()
}

// 组件挂载时加载数据
onMounted(() => {
  loadWorkflows()
})
</script>

<style scoped>
.workflow-designer {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h3 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.workflow-designer-container {
  display: flex;
  height: 100%;
  flex-direction: column;
}

.designer-toolbar {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.designer-canvas {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.canvas-container {
  flex: 1;
  background: #f5f5f5;
  position: relative;
  overflow: auto;
}

.placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
}

.designer-properties {
  width: 300px;
  border-left: 1px solid #f0f0f0;
  background: #fff;
  overflow-y: auto;
}
</style>
