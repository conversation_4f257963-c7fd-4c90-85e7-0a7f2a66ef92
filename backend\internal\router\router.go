package router

import (
	"quality_control/backend/internal/config"
	"quality_control/backend/internal/handler"
	"quality_control/backend/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

// Init 初始化路由
func Init(r *gin.Engine, db *gorm.DB, rdb *redis.Client, cfg *config.Config) {
	// 初始化服务
	authService := service.NewAuthService(db, cfg.JWT.Secret)
	userService := service.NewUserService(db)
	organizationService := service.NewOrganizationService(db)
	budgetService := service.NewBudgetService(db)
	expenditureService := service.NewExpenditureService(db)
	systemService := service.NewSystemService(db)
	rbacService := service.NewRBACService(db)
	positionService := service.NewPositionService(db)
	workflowService := service.NewWorkflowService(db)

	// 初始化控制器
	authHandler := handler.NewAuthHandler(authService)
	userHandler := handler.NewUserHandler(userService)
	organizationHandler := handler.NewOrganizationHandler(organizationService)
	budgetHandler := handler.NewBudgetHandler(budgetService)
	expenditureHandler := handler.NewExpenditureHandler(expenditureService)
	systemHandler := handler.NewSystemHandler(systemService)
	rbacHandler := handler.NewRBACHandler(rbacService)
	positionHandler := handler.NewPositionHandler(positionService)
	workflowHandler := handler.NewWorkflowHandler(workflowService)

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "Hospital Management System is running",
		})
	})

	// API路由组
	api := r.Group("/api")
	{
		// 认证相关路由
		auth := api.Group("/auth")
		{
			auth.POST("/login", authHandler.Login)
			auth.POST("/logout", authHandler.Logout)
			auth.GET("/profile", authHandler.AuthMiddleware(), authHandler.GetProfile)
			auth.POST("/refresh", authHandler.AuthMiddleware(), authHandler.RefreshToken)
			auth.POST("/change-password", authHandler.AuthMiddleware(), authHandler.ChangePassword)
		}

		// 用户管理路由
		users := api.Group("/users", authHandler.AuthMiddleware())
		{
			users.GET("", userHandler.GetUsers)
			users.POST("", userHandler.CreateUser)
			users.GET("/:id", userHandler.GetUser)
			users.PUT("/:id", userHandler.UpdateUser)
			users.DELETE("/:id", userHandler.DeleteUser)
			users.POST("/:id/reset-password", userHandler.ResetPassword)
			users.PATCH("/:id/status", userHandler.ToggleUserStatus)
			users.GET("/:id/positions", positionHandler.GetUserPositions)
			users.POST("/:id/positions", positionHandler.AssignUserPositions)
		}

		// 组织架构路由
		organizations := api.Group("/organizations", authHandler.AuthMiddleware())
		{
			organizations.GET("/tree", organizationHandler.GetOrganizationTree)
			organizations.GET("", organizationHandler.GetOrganizations)
			organizations.POST("", organizationHandler.CreateOrganization)
			organizations.GET("/:id", organizationHandler.GetOrganization)
			organizations.PUT("/:id", organizationHandler.UpdateOrganization)
			organizations.DELETE("/:id", organizationHandler.DeleteOrganization)
			organizations.PUT("/:id/move", organizationHandler.MoveOrganization)
		}

		// 角色权限路由
		roles := api.Group("/roles", authHandler.AuthMiddleware())
		{
			roles.GET("", rbacHandler.GetRoles)
			roles.POST("", rbacHandler.CreateRole)
			roles.GET("/:id", rbacHandler.GetRole)
			roles.PUT("/:id", rbacHandler.UpdateRole)
			roles.DELETE("/:id", rbacHandler.DeleteRole)
			roles.POST("/:id/permissions", rbacHandler.AssignRolePermissions)
		}

		// 权限管理路由
		permissions := api.Group("/permissions", authHandler.AuthMiddleware())
		{
			permissions.GET("", rbacHandler.GetPermissions)
			permissions.POST("", rbacHandler.CreatePermission)
			permissions.GET("/tree", rbacHandler.GetPermissionTree)
		}

		// 岗位管理路由
		positions := api.Group("/positions", authHandler.AuthMiddleware())
		{
			positions.GET("", positionHandler.GetPositions)
			positions.POST("", positionHandler.CreatePosition)
			positions.GET("/:id", positionHandler.GetPosition)
			positions.PUT("/:id", positionHandler.UpdatePosition)
			positions.DELETE("/:id", positionHandler.DeletePosition)
		}

		// 预算管理路由
		budget := api.Group("/budget", authHandler.AuthMiddleware())
		{
			// 预算方案
			budget.GET("/schemes", budgetHandler.GetBudgetSchemes)
			budget.POST("/schemes", budgetHandler.CreateBudgetScheme)
			budget.GET("/schemes/:id", budgetHandler.GetBudgetScheme)
			budget.PUT("/schemes/:id", budgetHandler.UpdateBudgetScheme)
			budget.DELETE("/schemes/:id", budgetHandler.DeleteBudgetScheme)
			budget.POST("/schemes/:id/submit", budgetHandler.SubmitBudgetScheme)
			budget.POST("/schemes/:id/approve", budgetHandler.ApproveBudgetScheme)

			// 预算科目
			budget.GET("/items/tree", budgetHandler.GetBudgetItemTree)
			budget.POST("/items", budgetHandler.CreateBudgetItem)

			// 预算分析
			budget.GET("/analysis", budgetHandler.GetBudgetAnalysis)
		}

		// 支出控制路由
		expenditure := api.Group("/expenditure", authHandler.AuthMiddleware())
		{
			// 支出申请
			expenditure.GET("/applications", expenditureHandler.GetExpenditureApplications)
			expenditure.POST("/applications", expenditureHandler.CreateExpenditureApplication)
			expenditure.GET("/applications/:id", expenditureHandler.GetExpenditureApplication)
			expenditure.PUT("/applications/:id", expenditureHandler.UpdateExpenditureApplication)
			expenditure.DELETE("/applications/:id", expenditureHandler.DeleteExpenditureApplication)
			expenditure.POST("/applications/:id/submit", expenditureHandler.SubmitExpenditureApplication)
			expenditure.POST("/applications/:id/approve", expenditureHandler.ApproveExpenditureApplication)

			// 支付管理
			expenditure.POST("/payments", expenditureHandler.CreatePayment)
		}

		// 系统管理路由
		system := api.Group("/system", authHandler.AuthMiddleware())
		{
			// 通知管理
			system.GET("/notifications", systemHandler.GetNotifications)
			system.GET("/notifications/unread-count", systemHandler.GetUnreadNotificationCount)
			system.PUT("/notifications/:id/read", systemHandler.MarkNotificationAsRead)
			system.PUT("/notifications/read-all", systemHandler.MarkAllNotificationsAsRead)

			// 审计日志
			system.GET("/audit-logs", systemHandler.GetAuditLogs)

			// 系统统计
			system.GET("/stats", systemHandler.GetSystemStats)
			system.GET("/dashboard", systemHandler.GetDashboardData)

			// 系统配置
			system.GET("/configs", func(c *gin.Context) {
				c.JSON(200, gin.H{"message": "Get system configs"})
			})
		}

		// 工作流路由
		workflow := api.Group("/workflow", authHandler.AuthMiddleware())
		{
			// 工作流定义管理
			workflow.GET("/definitions", workflowHandler.GetWorkflowDefinitions)
			workflow.POST("/definitions", workflowHandler.CreateWorkflowDefinition)
			workflow.GET("/definitions/:id", workflowHandler.GetWorkflowDefinition)
			workflow.PUT("/definitions/:id", workflowHandler.UpdateWorkflowDefinition)
			workflow.DELETE("/definitions/:id", workflowHandler.DeleteWorkflowDefinition)

			// 工作流任务管理
			workflow.GET("/tasks", workflowHandler.GetWorkflowTasks)
			workflow.GET("/tasks/todo", workflowHandler.GetUserTodoTasks)
			workflow.GET("/tasks/done", workflowHandler.GetUserDoneTasks)
		}
	}
}
