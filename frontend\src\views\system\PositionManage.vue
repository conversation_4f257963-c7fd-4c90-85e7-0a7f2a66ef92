<template>
  <div class="position-manage">
    <a-card :bordered="false">
      <template #title>
        <div class="page-header">
          <h3>岗位管理</h3>
          <div class="header-actions">
            <a-button type="primary" @click="showCreateModal">
              <template #icon><PlusOutlined /></template>
              新增岗位
            </a-button>
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="岗位名称">
            <a-input v-model:value="searchForm.name" placeholder="请输入岗位名称" />
          </a-form-item>
          <a-form-item label="岗位编码">
            <a-input v-model:value="searchForm.code" placeholder="请输入岗位编码" />
          </a-form-item>
          <a-form-item label="所属组织">
            <a-tree-select
              v-model:value="searchForm.organization_id"
              :tree-data="orgTreeData"
              :field-names="{ children: 'children', label: 'name', value: 'id' }"
              placeholder="请选择组织"
              allow-clear
              tree-default-expand-all
            />
          </a-form-item>
          <a-form-item label="状态">
            <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
              <a-select-option :value="1">启用</a-select-option>
              <a-select-option :value="0">禁用</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon><SearchOutlined /></template>
                搜索
              </a-button>
              <a-button @click="resetSearch">
                <template #icon><ReloadOutlined /></template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 岗位表格 -->
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="tableLoading"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'organization'">
            {{ record.organization?.name || '-' }}
          </template>
          <template v-else-if="column.key === 'level'">
            <a-tag color="blue">{{ getLevelName(record.level) }}</a-tag>
          </template>
          <template v-else-if="column.key === 'status'">
            <a-tag :color="record.status === 1 ? 'green' : 'red'">
              {{ record.status === 1 ? '启用' : '禁用' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a @click="editPosition(record)">编辑</a>
              <a @click="viewUsers(record)">查看人员</a>
              <a @click="toggleStatus(record)">
                {{ record.status === 1 ? '禁用' : '启用' }}
              </a>
              <a-dropdown>
                <a>更多 <DownOutlined /></a>
                <template #overlay>
                  <a-menu @click="({ key }) => handleMoreAction(key, record)">
                    <a-menu-item key="copy">复制岗位</a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" style="color: #ff4d4f">删除</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑岗位模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :confirm-loading="modalLoading"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-form-item label="岗位名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入岗位名称" />
        </a-form-item>
        
        <a-form-item label="岗位编码" name="code">
          <a-input v-model:value="formData.code" placeholder="请输入岗位编码" />
        </a-form-item>
        
        <a-form-item label="所属组织" name="organization_id">
          <a-tree-select
            v-model:value="formData.organization_id"
            :tree-data="orgTreeData"
            :field-names="{ children: 'children', label: 'name', value: 'id' }"
            placeholder="请选择所属组织"
            tree-default-expand-all
          />
        </a-form-item>
        
        <a-form-item label="岗位级别" name="level">
          <a-select v-model:value="formData.level" placeholder="请选择岗位级别">
            <a-select-option :value="1">初级</a-select-option>
            <a-select-option :value="2">中级</a-select-option>
            <a-select-option :value="3">高级</a-select-option>
            <a-select-option :value="4">专家级</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="岗位描述" name="description">
          <a-textarea v-model:value="formData.description" placeholder="请输入岗位描述" :rows="3" />
        </a-form-item>
        
        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="formData.status">
            <a-radio :value="1">启用</a-radio>
            <a-radio :value="0">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 查看人员模态框 -->
    <a-modal
      v-model:open="usersModalVisible"
      title="岗位人员"
      :footer="null"
      width="800px"
    >
      <div v-if="currentPosition">
        <p>岗位：<strong>{{ currentPosition.name }}</strong></p>
        <a-table
          :columns="userColumns"
          :data-source="positionUsers"
          :pagination="false"
          :loading="usersLoading"
          row-key="id"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'avatar'">
              <a-avatar :src="record.avatar" :icon="h(UserOutlined)" />
            </template>
            <template v-else-if="column.key === 'department'">
              {{ record.department?.name || '-' }}
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getUserStatusColor(record.status)">
                {{ getUserStatusName(record.status) }}
              </a-tag>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, h } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  DownOutlined,
  UserOutlined
} from '@ant-design/icons-vue'
import {
  getPositions,
  createPosition,
  updatePosition,
  deletePosition
} from '@/api/rbac'
import { getOrganizationTree } from '@/api/organization'

// 响应式数据
const tableData = ref([])
const tableLoading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const usersModalVisible = ref(false)
const usersLoading = ref(false)
const formRef = ref()
const orgTreeData = ref([])
const currentPosition = ref(null)
const positionUsers = ref([])

// 搜索表单
const searchForm = reactive({
  name: '',
  code: '',
  organization_id: null,
  status: null
})

// 表单数据
const formData = reactive({
  id: null,
  name: '',
  code: '',
  organization_id: null,
  level: 1,
  description: '',
  status: 1
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 计算属性
const modalTitle = computed(() => formData.id ? '编辑岗位' : '新增岗位')

// 表格列配置
const columns = [
  { title: '岗位名称', dataIndex: 'name', key: 'name' },
  { title: '岗位编码', dataIndex: 'code', key: 'code' },
  { title: '所属组织', key: 'organization', width: 150 },
  { title: '岗位级别', key: 'level', width: 100 },
  { title: '岗位描述', dataIndex: 'description', key: 'description', ellipsis: true },
  { title: '状态', key: 'status', width: 80 },
  { title: '创建时间', dataIndex: 'created_at', key: 'created_at', width: 180 },
  { title: '操作', key: 'action', width: 200, fixed: 'right' }
]

// 用户表格列配置
const userColumns = [
  { title: '头像', key: 'avatar', width: 60 },
  { title: '姓名', dataIndex: 'real_name', key: 'real_name' },
  { title: '用户名', dataIndex: 'username', key: 'username' },
  { title: '工号', dataIndex: 'employee_no', key: 'employee_no' },
  { title: '部门', key: 'department' },
  { title: '状态', key: 'status', width: 80 }
]

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入岗位名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入岗位编码', trigger: 'blur' }],
  organization_id: [{ required: true, message: '请选择所属组织', trigger: 'change' }],
  level: [{ required: true, message: '请选择岗位级别', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 获取级别名称
const getLevelName = (level) => {
  const names = { 1: '初级', 2: '中级', 3: '高级', 4: '专家级' }
  return names[level] || '未知'
}

// 获取用户状态名称
const getUserStatusName = (status) => {
  const names = { 0: '禁用', 1: '在职', 2: '离职' }
  return names[status] || '未知'
}

// 获取用户状态颜色
const getUserStatusColor = (status) => {
  const colors = { 0: 'red', 1: 'green', 2: 'orange' }
  return colors[status] || 'default'
}

// 加载岗位列表
const loadPositions = async () => {
  tableLoading.value = true
  try {
    const params = {
      page: pagination.current,
      page_size: pagination.pageSize,
      ...searchForm
    }
    
    const response = await getPositions(params)
    tableData.value = response.data.list || []
    pagination.total = response.data.total || 0
  } catch (error) {
    message.error('加载岗位列表失败')
  } finally {
    tableLoading.value = false
  }
}

// 加载组织树
const loadOrganizationTree = async () => {
  try {
    const response = await getOrganizationTree()
    orgTreeData.value = response.data || []
  } catch (error) {
    message.error('加载组织架构失败')
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadPositions()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    code: '',
    organization_id: null,
    status: null
  })
  pagination.current = 1
  loadPositions()
}

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadPositions()
}

// 显示创建模态框
const showCreateModal = () => {
  resetForm()
  modalVisible.value = true
}

// 编辑岗位
const editPosition = (position) => {
  Object.assign(formData, {
    id: position.id,
    name: position.name,
    code: position.code,
    organization_id: position.organization_id,
    level: position.level,
    description: position.description,
    status: position.status
  })
  modalVisible.value = true
}

// 查看人员
const viewUsers = async (position) => {
  currentPosition.value = position
  usersModalVisible.value = true
  // 这里应该调用API获取岗位人员，暂时使用模拟数据
  positionUsers.value = []
  message.info('查看岗位人员功能开发中...')
}

// 切换状态
const toggleStatus = async (position) => {
  const newStatus = position.status === 1 ? 0 : 1
  const action = newStatus === 1 ? '启用' : '禁用'
  
  Modal.confirm({
    title: `${action}岗位`,
    content: `确定要${action}岗位"${position.name}"吗？`,
    onOk: async () => {
      try {
        await updatePosition(position.id, { status: newStatus })
        message.success(`${action}成功`)
        loadPositions()
      } catch (error) {
        message.error(`${action}失败`)
      }
    }
  })
}

// 更多操作
const handleMoreAction = async (key, position) => {
  switch (key) {
    case 'copy':
      copyPosition(position)
      break
    case 'delete':
      deletePositionConfirm(position)
      break
  }
}

// 复制岗位
const copyPosition = (position) => {
  Object.assign(formData, {
    id: null,
    name: `${position.name}_副本`,
    code: `${position.code}_copy`,
    organization_id: position.organization_id,
    level: position.level,
    description: position.description,
    status: position.status
  })
  modalVisible.value = true
}

// 删除岗位确认
const deletePositionConfirm = (position) => {
  Modal.confirm({
    title: '删除岗位',
    content: `确定要删除岗位"${position.name}"吗？删除后不可恢复。`,
    onOk: async () => {
      try {
        await deletePosition(position.id)
        message.success('删除成功')
        loadPositions()
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

// 处理模态框确认
const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    modalLoading.value = true
    
    if (formData.id) {
      await updatePosition(formData.id, formData)
      message.success('更新成功')
    } else {
      await createPosition(formData)
      message.success('创建成功')
    }
    
    modalVisible.value = false
    loadPositions()
  } catch (error) {
    if (error.errorFields) {
      message.error('请检查表单输入')
    } else {
      message.error('操作失败')
    }
  } finally {
    modalLoading.value = false
  }
}

// 处理模态框取消
const handleModalCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: null,
    name: '',
    code: '',
    organization_id: null,
    level: 1,
    description: '',
    status: 1
  })
  formRef.value?.resetFields()
}

// 组件挂载时加载数据
onMounted(() => {
  loadPositions()
  loadOrganizationTree()
})
</script>

<style scoped>
.position-manage {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h3 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}
</style>
