<template>
  <div class="expense-payment">
    <a-card :bordered="false">
      <template #title>
        <h3>支付管理</h3>
      </template>

      <!-- 筛选区 -->
      <div class="filter-section">
        <a-form layout="inline" :model="filterForm" @finish="handleSearch">
          <a-form-item label="申请编号">
            <a-input v-model:value="filterForm.application_no" placeholder="请输入申请编号" />
          </a-form-item>
          
          <a-form-item label="申请人">
            <a-input v-model:value="filterForm.applicant" placeholder="请输入申请人" />
          </a-form-item>
          
          <a-form-item label="支付状态">
            <a-select v-model:value="filterForm.payment_status" placeholder="请选择状态" allow-clear style="width: 150px">
              <a-select-option value="pending">待支付</a-select-option>
              <a-select-option value="processing">支付中</a-select-option>
              <a-select-option value="paid">已支付</a-select-option>
              <a-select-option value="failed">支付失败</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="支付方式">
            <a-select v-model:value="filterForm.payment_method" placeholder="请选择方式" allow-clear style="width: 150px">
              <a-select-option value="bank_transfer">银行转账</a-select-option>
              <a-select-option value="cash">现金</a-select-option>
              <a-select-option value="check">支票</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon><SearchOutlined /></template>
                查询
              </a-button>
              <a-button @click="resetFilter">
                <template #icon><ReloadOutlined /></template>
                重置
              </a-button>
              <a-button @click="batchPayment" :disabled="selectedRowKeys.length === 0">
                <template #icon><PayCircleOutlined /></template>
                批量支付
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 统计卡片 -->
      <div class="statistics-section">
        <a-row :gutter="16">
          <a-col :xs="12" :sm="6">
            <a-card size="small">
              <a-statistic
                title="待支付"
                :value="statistics.pending_count"
                suffix="笔"
                :value-style="{ color: '#faad14' }"
              />
            </a-card>
          </a-col>
          <a-col :xs="12" :sm="6">
            <a-card size="small">
              <a-statistic
                title="待支付金额"
                :value="statistics.pending_amount"
                :precision="2"
                suffix="元"
                :value-style="{ color: '#fa8c16' }"
              />
            </a-card>
          </a-col>
          <a-col :xs="12" :sm="6">
            <a-card size="small">
              <a-statistic
                title="今日已支付"
                :value="statistics.today_paid"
                suffix="笔"
                :value-style="{ color: '#52c41a' }"
              />
            </a-card>
          </a-col>
          <a-col :xs="12" :sm="6">
            <a-card size="small">
              <a-statistic
                title="本月已支付"
                :value="statistics.month_paid_amount"
                :precision="2"
                suffix="元"
                :value-style="{ color: '#1890ff' }"
              />
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 数据表格 -->
      <div class="table-section">
        <a-table
          :columns="columns"
          :data-source="dataSource"
          :pagination="pagination"
          :loading="loading"
          :row-selection="rowSelection"
          row-key="id"
          @change="handleTableChange"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'application_no'">
              <a @click="viewDetail(record)">{{ record.application_no }}</a>
            </template>
            
            <template v-else-if="column.key === 'total_amount'">
              <span class="amount-text">{{ formatMoney(record.total_amount) }}</span>
            </template>
            
            <template v-else-if="column.key === 'payment_status'">
              <a-tag :color="getPaymentStatusColor(record.payment_status)">
                {{ getPaymentStatusName(record.payment_status) }}
              </a-tag>
            </template>
            
            <template v-else-if="column.key === 'payment_method'">
              <span>{{ getPaymentMethodName(record.payment_method) }}</span>
            </template>
            
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="viewDetail(record)">查看</a>
                <a @click="processPayment(record)" v-if="record.payment_status === 'pending'">支付</a>
                <a @click="confirmPayment(record)" v-if="record.payment_status === 'processing'">确认</a>
                <a @click="viewPaymentRecord(record)" v-if="record.payment_status === 'paid'">支付记录</a>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-card>

    <!-- 支付处理模态框 -->
    <a-modal
      v-model:open="paymentModalVisible"
      title="支付处理"
      @ok="handlePaymentSubmit"
      @cancel="paymentModalVisible = false"
    >
      <a-form layout="vertical" :model="paymentForm">
        <a-form-item label="申请信息">
          <a-descriptions :column="2" size="small">
            <a-descriptions-item label="申请编号">{{ currentPaymentRecord?.application_no }}</a-descriptions-item>
            <a-descriptions-item label="申请人">{{ currentPaymentRecord?.applicant_name }}</a-descriptions-item>
            <a-descriptions-item label="申请金额">{{ formatMoney(currentPaymentRecord?.total_amount) }}</a-descriptions-item>
            <a-descriptions-item label="申请部门">{{ currentPaymentRecord?.department_name }}</a-descriptions-item>
          </a-descriptions>
        </a-form-item>
        
        <a-form-item label="支付方式" name="payment_method">
          <a-select v-model:value="paymentForm.payment_method" placeholder="请选择支付方式">
            <a-select-option value="bank_transfer">银行转账</a-select-option>
            <a-select-option value="cash">现金</a-select-option>
            <a-select-option value="check">支票</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="收款账户" name="payee_account" v-if="paymentForm.payment_method === 'bank_transfer'">
          <a-input v-model:value="paymentForm.payee_account" placeholder="请输入收款账户" />
        </a-form-item>
        
        <a-form-item label="收款人" name="payee_name">
          <a-input v-model:value="paymentForm.payee_name" placeholder="请输入收款人姓名" />
        </a-form-item>
        
        <a-form-item label="支付金额" name="payment_amount">
          <a-input-number
            v-model:value="paymentForm.payment_amount"
            :min="0"
            :max="currentPaymentRecord?.total_amount"
            :precision="2"
            style="width: 100%"
            placeholder="请输入支付金额"
          />
        </a-form-item>
        
        <a-form-item label="支付备注" name="payment_remark">
          <a-textarea
            v-model:value="paymentForm.payment_remark"
            placeholder="请输入支付备注"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 批量支付模态框 -->
    <a-modal
      v-model:open="batchPaymentModalVisible"
      title="批量支付"
      @ok="handleBatchPaymentSubmit"
      @cancel="batchPaymentModalVisible = false"
    >
      <a-form layout="vertical">
        <a-form-item label="支付方式">
          <a-select v-model:value="batchPaymentForm.payment_method" placeholder="请选择支付方式">
            <a-select-option value="bank_transfer">银行转账</a-select-option>
            <a-select-option value="cash">现金</a-select-option>
            <a-select-option value="check">支票</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="支付备注">
          <a-textarea
            v-model:value="batchPaymentForm.payment_remark"
            placeholder="请输入支付备注"
            :rows="3"
          />
        </a-form-item>
        
        <a-form-item label="选中的申请">
          <div class="selected-payments">
            <a-list
              :data-source="selectedPayments"
              size="small"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>
                      {{ item.application_no }} - {{ item.applicant_name }}
                    </template>
                    <template #description>
                      金额：{{ formatMoney(item.total_amount) }}
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </div>
          <div class="batch-summary">
            <a-statistic
              title="批量支付总额"
              :value="batchPaymentTotal"
              :precision="2"
              suffix="元"
              :value-style="{ color: '#1890ff' }"
            />
          </div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 支付记录模态框 -->
    <a-modal
      v-model:open="paymentRecordModalVisible"
      title="支付记录"
      width="800px"
      :footer="null"
    >
      <payment-record
        v-if="paymentRecordModalVisible"
        :application-id="selectedApplicationId"
      />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  PayCircleOutlined
} from '@ant-design/icons-vue'
import {
  getPendingPayments,
  createPaymentRequest,
  confirmPayment as confirmPaymentAPI,
  batchPayment as batchPaymentAPI,
  getPaymentRecords
} from '@/api/expenditure'
import PaymentRecord from './components/PaymentRecord.vue'

// 响应式数据
const loading = ref(false)
const dataSource = ref([])
const paymentModalVisible = ref(false)
const batchPaymentModalVisible = ref(false)
const paymentRecordModalVisible = ref(false)
const selectedApplicationId = ref(null)
const selectedRowKeys = ref([])
const currentPaymentRecord = ref(null)

// 筛选表单
const filterForm = reactive({
  application_no: '',
  applicant: '',
  payment_status: null,
  payment_method: null
})

// 统计数据
const statistics = reactive({
  pending_count: 0,
  pending_amount: 0,
  today_paid: 0,
  month_paid_amount: 0
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 支付表单
const paymentForm = reactive({
  payment_method: 'bank_transfer',
  payee_account: '',
  payee_name: '',
  payment_amount: 0,
  payment_remark: ''
})

// 批量支付表单
const batchPaymentForm = reactive({
  payment_method: 'bank_transfer',
  payment_remark: ''
})

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  },
  getCheckboxProps: (record) => ({
    disabled: record.payment_status !== 'pending'
  })
}

// 计算属性
const selectedPayments = computed(() => {
  return dataSource.value.filter(app => selectedRowKeys.value.includes(app.id))
})

const batchPaymentTotal = computed(() => {
  return selectedPayments.value.reduce((sum, item) => sum + item.total_amount, 0)
})

// 表格列配置
const columns = [
  { title: '申请编号', key: 'application_no', width: 150 },
  { title: '申请人', dataIndex: 'applicant_name', key: 'applicant_name', width: 100 },
  { title: '申请部门', dataIndex: 'department_name', key: 'department_name', width: 120 },
  { title: '申请金额', key: 'total_amount', width: 120 },
  { title: '支付状态', key: 'payment_status', width: 100 },
  { title: '支付方式', key: 'payment_method', width: 100 },
  { title: '审批时间', dataIndex: 'approved_at', key: 'approved_at', width: 120 },
  { title: '操作', key: 'action', width: 200, fixed: 'right' }
]

// 工具函数
const formatMoney = (amount) => {
  return (amount / 100).toFixed(2) + '元'
}

const getPaymentStatusColor = (status) => {
  const colors = {
    'pending': 'orange',
    'processing': 'blue',
    'paid': 'green',
    'failed': 'red'
  }
  return colors[status] || 'default'
}

const getPaymentStatusName = (status) => {
  const names = {
    'pending': '待支付',
    'processing': '支付中',
    'paid': '已支付',
    'failed': '支付失败'
  }
  return names[status] || '未知'
}

const getPaymentMethodName = (method) => {
  const names = {
    'bank_transfer': '银行转账',
    'cash': '现金',
    'check': '支票'
  }
  return names[method] || '未设置'
}

// 数据加载函数
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      ...filterForm,
      page: pagination.current,
      page_size: pagination.pageSize
    }
    
    const response = await getPendingPayments(params)
    dataSource.value = response.data.list || []
    pagination.total = response.data.total || 0
  } catch (error) {
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  try {
    // 这里应该调用支付统计接口
    statistics.pending_count = 25
    statistics.pending_amount = 125000
    statistics.today_paid = 8
    statistics.month_paid_amount = 450000
  } catch (error) {
    message.error('加载统计数据失败')
  }
}

// 事件处理函数
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const resetFilter = () => {
  Object.assign(filterForm, {
    application_no: '',
    applicant: '',
    payment_status: null,
    payment_method: null
  })
  pagination.current = 1
  loadData()
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

const viewDetail = (record) => {
  selectedApplicationId.value = record.id
  // 这里可以打开详情模态框
  message.info('查看详情功能开发中...')
}

const processPayment = (record) => {
  currentPaymentRecord.value = record
  paymentForm.payment_amount = record.total_amount
  paymentForm.payee_name = record.applicant_name
  paymentForm.payee_account = ''
  paymentForm.payment_remark = ''
  paymentModalVisible.value = true
}

const handlePaymentSubmit = async () => {
  try {
    await createPaymentRequest({
      application_id: currentPaymentRecord.value.id,
      ...paymentForm
    })
    message.success('支付处理成功')
    paymentModalVisible.value = false
    loadData()
    loadStatistics()
  } catch (error) {
    message.error('支付处理失败')
  }
}

const confirmPayment = async (record) => {
  try {
    await confirmPaymentAPI(record.id, {
      status: 'paid',
      remark: '支付确认'
    })
    message.success('支付确认成功')
    loadData()
    loadStatistics()
  } catch (error) {
    message.error('支付确认失败')
  }
}

const batchPayment = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要支付的申请')
    return
  }
  batchPaymentForm.payment_method = 'bank_transfer'
  batchPaymentForm.payment_remark = ''
  batchPaymentModalVisible.value = true
}

const handleBatchPaymentSubmit = async () => {
  try {
    await batchPaymentAPI({
      application_ids: selectedRowKeys.value,
      ...batchPaymentForm
    })
    message.success('批量支付成功')
    batchPaymentModalVisible.value = false
    selectedRowKeys.value = []
    loadData()
    loadStatistics()
  } catch (error) {
    message.error('批量支付失败')
  }
}

const viewPaymentRecord = (record) => {
  selectedApplicationId.value = record.id
  paymentRecordModalVisible.value = true
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
  loadStatistics()
})
</script>

<style scoped>
.expense-payment {
  padding: 24px;
}

.filter-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.statistics-section {
  margin-bottom: 24px;
}

.table-section {
  margin-bottom: 24px;
}

.amount-text {
  font-weight: 500;
  color: #1890ff;
}

.selected-payments {
  max-height: 200px;
  overflow-y: auto;
  margin-bottom: 16px;
}

.batch-summary {
  text-align: center;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .expense-payment {
    padding: 16px;
  }
  
  .filter-section {
    padding: 12px;
  }
}
</style>
