<template>
  <div class="contract-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="page-title-section">
            <h1 class="page-title">合同管理</h1>
            <p class="page-subtitle">管理和跟踪所有合同的生命周期</p>
          </div>
        </div>
        <div class="header-actions">
          <a-button @click="exportContracts">
            <template #icon><ExportOutlined /></template>
            导出数据
          </a-button>
          <a-button type="primary" @click="createContract">
            <template #icon><PlusOutlined /></template>
            新建合同
          </a-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <a-row :gutter="[24, 16]">
        <a-col :xs="12" :sm="6">
          <div class="stat-card total">
            <div class="stat-icon">
              <FileTextOutlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">合同总数</div>
            </div>
          </div>
        </a-col>
        <a-col :xs="12" :sm="6">
          <div class="stat-card pending">
            <div class="stat-icon">
              <ClockCircleOutlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.pending }}</div>
              <div class="stat-label">待审批</div>
            </div>
          </div>
        </a-col>
        <a-col :xs="12" :sm="6">
          <div class="stat-card active">
            <div class="stat-icon">
              <CheckCircleOutlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.active }}</div>
              <div class="stat-label">执行中</div>
            </div>
          </div>
        </a-col>
        <a-col :xs="12" :sm="6">
          <div class="stat-card expiring">
            <div class="stat-icon">
              <ExclamationCircleOutlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.expiring }}</div>
              <div class="stat-label">即将到期</div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <a-card class="filter-card" :bordered="false">
        <a-form layout="inline" class="filter-form">
          <a-form-item label="合同状态">
            <a-select 
              v-model:value="filters.status" 
              style="width: 120px"
              @change="handleFilterChange"
            >
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="draft">草稿</a-select-option>
              <a-select-option value="pending">待审批</a-select-option>
              <a-select-option value="active">执行中</a-select-option>
              <a-select-option value="completed">已完成</a-select-option>
              <a-select-option value="terminated">已终止</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="合同类型">
            <a-select 
              v-model:value="filters.type" 
              style="width: 120px"
              @change="handleFilterChange"
            >
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="purchase">采购合同</a-select-option>
              <a-select-option value="service">服务合同</a-select-option>
              <a-select-option value="lease">租赁合同</a-select-option>
              <a-select-option value="other">其他</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="签约时间">
            <a-range-picker 
              v-model:value="filters.dateRange"
              @change="handleFilterChange"
            />
          </a-form-item>
          
          <a-form-item>
            <a-input-search
              v-model:value="filters.keyword"
              placeholder="搜索合同名称、编号、供应商..."
              style="width: 250px"
              @search="handleSearch"
            />
          </a-form-item>
          
          <a-form-item>
            <a-button @click="resetFilters">
              <template #icon><ReloadOutlined /></template>
              重置
            </a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 合同列表 -->
    <div class="table-section">
      <a-card class="table-card" :bordered="false">
        <template #title>
          <div class="table-header">
            <span>合同列表</span>
            <div class="table-actions">
              <a-button-group>
                <a-button 
                  :type="viewMode === 'table' ? 'primary' : 'default'"
                  @click="viewMode = 'table'"
                >
                  <template #icon><TableOutlined /></template>
                </a-button>
                <a-button 
                  :type="viewMode === 'card' ? 'primary' : 'default'"
                  @click="viewMode = 'card'"
                >
                  <template #icon><AppstoreOutlined /></template>
                </a-button>
              </a-button-group>
            </div>
          </div>
        </template>
        
        <!-- 表格视图 -->
        <a-table
          v-if="viewMode === 'table'"
          :columns="columns"
          :data-source="contracts"
          :loading="loading"
          :pagination="pagination"
          row-key="id"
          @change="handleTableChange"
          class="contract-table"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div class="contract-name">
                <a @click="viewContract(record)">{{ record.name }}</a>
                <div class="contract-code">{{ record.code }}</div>
              </div>
            </template>
            
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            
            <template v-else-if="column.key === 'amount'">
              <div class="amount-cell">
                ¥{{ formatNumber(record.amount) }}
              </div>
            </template>
            
            <template v-else-if="column.key === 'progress'">
              <a-progress 
                :percent="record.progress" 
                size="small"
                :stroke-color="getProgressColor(record.progress)"
              />
            </template>
            
            <template v-else-if="column.key === 'action'">
              <a-dropdown>
                <a-button type="text">
                  操作
                  <DownOutlined />
                </a-button>
                <template #overlay>
                  <a-menu @click="handleAction($event, record)">
                    <a-menu-item key="view">
                      <EyeOutlined />
                      查看详情
                    </a-menu-item>
                    <a-menu-item key="edit" v-if="canEdit(record)">
                      <EditOutlined />
                      编辑
                    </a-menu-item>
                    <a-menu-item key="approve" v-if="canApprove(record)">
                      <CheckOutlined />
                      审批
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" danger v-if="canDelete(record)">
                      <DeleteOutlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </template>
          </template>
        </a-table>
        
        <!-- 卡片视图 -->
        <div v-else class="card-view">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :lg="8" v-for="contract in contracts" :key="contract.id">
              <div class="contract-card" @click="viewContract(contract)">
                <div class="card-header">
                  <div class="contract-info">
                    <h4 class="contract-title">{{ contract.name }}</h4>
                    <p class="contract-code">{{ contract.code }}</p>
                  </div>
                  <a-tag :color="getStatusColor(contract.status)">
                    {{ getStatusText(contract.status) }}
                  </a-tag>
                </div>
                
                <div class="card-content">
                  <div class="info-item">
                    <span class="label">供应商：</span>
                    <span class="value">{{ contract.supplier }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">合同金额：</span>
                    <span class="value amount">¥{{ formatNumber(contract.amount) }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">签约日期：</span>
                    <span class="value">{{ formatDate(contract.sign_date) }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">执行进度：</span>
                    <a-progress 
                      :percent="contract.progress" 
                      size="small"
                      :stroke-color="getProgressColor(contract.progress)"
                    />
                  </div>
                </div>
              </div>
            </a-col>
          </a-row>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  PlusOutlined,
  ExportOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  TableOutlined,
  AppstoreOutlined,
  EyeOutlined,
  EditOutlined,
  CheckOutlined,
  DeleteOutlined,
  DownOutlined
} from '@ant-design/icons-vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const viewMode = ref('table')
const contracts = ref([])

// 统计数据
const stats = reactive({
  total: 156,
  pending: 23,
  active: 89,
  expiring: 12
})

// 筛选条件
const filters = reactive({
  status: '',
  type: '',
  dateRange: null,
  keyword: ''
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 表格列配置
const columns = [
  {
    title: '合同名称',
    key: 'name',
    width: 200,
    ellipsis: true
  },
  {
    title: '供应商',
    dataIndex: 'supplier',
    key: 'supplier',
    width: 150
  },
  {
    title: '合同类型',
    dataIndex: 'type_name',
    key: 'type',
    width: 100
  },
  {
    title: '合同金额',
    key: 'amount',
    width: 120,
    align: 'right'
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '执行进度',
    key: 'progress',
    width: 120
  },
  {
    title: '签约日期',
    dataIndex: 'sign_date',
    key: 'sign_date',
    width: 120
  },
  {
    title: '操作',
    key: 'action',
    width: 100,
    fixed: 'right'
  }
]

// 工具函数
const formatNumber = (num) => {
  return num.toLocaleString('zh-CN')
}

const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD')
}

const getStatusColor = (status) => {
  const colors = {
    'draft': 'default',
    'pending': 'processing',
    'active': 'success',
    'completed': 'success',
    'terminated': 'error'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    'draft': '草稿',
    'pending': '待审批',
    'active': '执行中',
    'completed': '已完成',
    'terminated': '已终止'
  }
  return texts[status] || '未知'
}

const getProgressColor = (progress) => {
  if (progress >= 80) return '#52c41a'
  if (progress >= 50) return '#1890ff'
  if (progress >= 20) return '#faad14'
  return '#ff4d4f'
}

// 权限检查
const canEdit = (record) => {
  return ['draft', 'pending'].includes(record.status)
}

const canApprove = (record) => {
  return record.status === 'pending'
}

const canDelete = (record) => {
  return record.status === 'draft'
}

// 事件处理
const createContract = () => {
  router.push('/contract/create')
}

const viewContract = (record) => {
  router.push(`/contract/detail/${record.id}`)
}

const handleFilterChange = () => {
  loadContracts()
}

const handleSearch = () => {
  loadContracts()
}

const resetFilters = () => {
  Object.assign(filters, {
    status: '',
    type: '',
    dateRange: null,
    keyword: ''
  })
  loadContracts()
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadContracts()
}

const handleAction = ({ key }, record) => {
  switch (key) {
    case 'view':
      viewContract(record)
      break
    case 'edit':
      router.push(`/contract/edit/${record.id}`)
      break
    case 'approve':
      router.push(`/contract/approve/${record.id}`)
      break
    case 'delete':
      deleteContract(record)
      break
  }
}

const deleteContract = (record) => {
  // 删除逻辑
  message.success('删除成功')
}

const exportContracts = () => {
  message.info('导出功能开发中...')
}

// 数据加载
const loadContracts = async () => {
  loading.value = true
  try {
    // 模拟数据
    contracts.value = Array.from({ length: 10 }, (_, i) => ({
      id: i + 1,
      name: `医疗设备采购合同${i + 1}`,
      code: `HT${String(i + 1).padStart(4, '0')}`,
      supplier: `供应商${i + 1}`,
      type: 'purchase',
      type_name: '采购合同',
      amount: Math.floor(Math.random() * 1000000) + 100000,
      status: ['draft', 'pending', 'active', 'completed'][Math.floor(Math.random() * 4)],
      progress: Math.floor(Math.random() * 100),
      sign_date: dayjs().subtract(Math.floor(Math.random() * 365), 'day').format('YYYY-MM-DD')
    }))
    pagination.total = 156
  } catch (error) {
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadContracts()
})
</script>

<style scoped>
/* 合同管理页面 - 企业级设计 */
.contract-list {
  background: var(--bg-secondary);
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-active) 100%);
  color: white;
  padding: var(--spacing-2xl) var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.page-title-section {
  flex: 1;
}

.page-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin: 0 0 var(--spacing-sm) 0;
  color: white;
}

.page-subtitle {
  font-size: var(--font-size-md);
  margin: 0;
  opacity: 0.9;
  font-weight: var(--font-weight-normal);
}

.header-actions {
  display: flex;
  gap: var(--spacing-md);
}

/* 统计卡片 */
.stats-section {
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.stat-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.stat-card.total::before {
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-hover));
}

.stat-card.pending::before {
  background: linear-gradient(90deg, #FF9800, #FFB74D);
}

.stat-card.active::before {
  background: linear-gradient(90deg, #00C853, #4CAF50);
}

.stat-card.expiring::before {
  background: linear-gradient(90deg, #F44336, #E57373);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-card.total .stat-icon {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-color-hover));
}

.stat-card.pending .stat-icon {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}

.stat-card.active .stat-icon {
  background: linear-gradient(135deg, #00C853, #4CAF50);
}

.stat-card.expiring .stat-icon {
  background: linear-gradient(135deg, #F44336, #E57373);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 筛选区域 */
.filter-section {
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.filter-card {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
}

.filter-form {
  width: 100%;
}

.filter-form :deep(.ant-form-item) {
  margin-bottom: 0;
}

/* 表格区域 */
.table-section {
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.table-card {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.table-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* 表格样式 */
.contract-table :deep(.ant-table-thead > tr > th) {
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-light);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.contract-table :deep(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid var(--border-light);
}

.contract-table :deep(.ant-table-tbody > tr:hover > td) {
  background: var(--bg-hover);
}

.contract-name a {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
}

.contract-name a:hover {
  text-decoration: underline;
}

.contract-code {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  margin-top: 2px;
}

.amount-cell {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

/* 卡片视图 */
.card-view {
  padding: var(--spacing-md);
}

.contract-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  border: 1px solid var(--border-light);
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100%;
}

.contract-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
}

.contract-info {
  flex: 1;
}

.contract-title {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item .label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.info-item .value {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.info-item .value.amount {
  color: var(--primary-color);
  font-weight: var(--font-weight-semibold);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: var(--spacing-lg);
    text-align: center;
  }

  .stats-section,
  .filter-section,
  .table-section {
    padding: 0 var(--spacing-md);
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .page-title {
    font-size: var(--font-size-2xl);
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .filter-form {
    flex-direction: column;
  }

  .filter-form :deep(.ant-form-item) {
    margin-bottom: var(--spacing-md);
    width: 100%;
  }

  .table-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;
  }
}

@media (max-width: 576px) {
  .page-header {
    padding: var(--spacing-md);
  }

  .page-title {
    font-size: var(--font-size-xl);
  }

  .page-subtitle {
    font-size: var(--font-size-sm);
  }

  .stats-section,
  .filter-section,
  .table-section {
    padding: 0 var(--spacing-sm);
  }

  .stat-card {
    padding: var(--spacing-md);
  }

  .contract-card {
    padding: var(--spacing-md);
  }
}
</style>
