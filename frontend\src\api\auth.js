import request from './request'

// 用户登录
export const login = (data) => {
  return request.post('/auth/login', data)
}

// 用户退出
export const logout = () => {
  return request.post('/auth/logout')
}

// 获取用户信息
export const getProfile = () => {
  return request.get('/auth/profile')
}

// 刷新令牌
export const refreshToken = () => {
  return request.post('/auth/refresh')
}

// 修改密码
export const changePassword = (data) => {
  return request.post('/auth/change-password', data)
}