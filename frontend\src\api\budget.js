import request from './request'

// ==================== 预算编制相关 ====================

// 获取预算方案列表
export const getBudgetSchemes = (params) => {
  return request.get('/budget/schemes', { params })
}

// 获取预算方案详情
export const getBudgetScheme = (id) => {
  return request.get(`/budget/schemes/${id}`)
}

// 创建预算方案
export const createBudgetScheme = (data) => {
  return request.post('/budget/schemes', data)
}

// 更新预算方案
export const updateBudgetScheme = (id, data) => {
  return request.put(`/budget/schemes/${id}`, data)
}

// 删除预算方案
export const deleteBudgetScheme = (id) => {
  return request.delete(`/budget/schemes/${id}`)
}

// 获取预算数据
export const getBudgetData = (params) => {
  return request.get('/budget/data', { params })
}

// 获取历史预算数据
export const getHistoryBudgetData = (params) => {
  return request.get('/budget/history', { params })
}

// 获取系统建议预算数据
export const getSuggestedBudgetData = (params) => {
  return request.get('/budget/suggested', { params })
}

// 保存预算草稿
export const saveBudgetDraft = (data) => {
  return request.post('/budget/draft', data)
}

// 提交预算方案审批
export const submitBudgetScheme = (id) => {
  return request.post(`/budget/schemes/${id}/submit`)
}

// 审批预算方案
export const approveBudgetScheme = (id, data) => {
  return request.post(`/budget/schemes/${id}/approve`, data)
}

// 批量审核预算
export const batchReviewBudget = (data) => {
  return request.post('/budget/batch-review', data)
}

// ==================== 预算分析相关 ====================

// 获取预算分析数据
export const getBudgetAnalysisData = (params) => {
  return request.get('/budget/analysis', { params })
}

// 获取预算趋势数据
export const getBudgetTrendData = (params) => {
  return request.get('/budget/trend', { params })
}

// 获取预算执行明细
export const getBudgetExecutionDetail = (budgetId, params) => {
  return request.get(`/budget/${budgetId}/execution-detail`, { params })
}

// 导出预算分析报告
export const exportBudgetAnalysis = (params) => {
  return request.get('/budget/analysis/export', {
    params,
    responseType: 'blob'
  })
}

// 获取预算对比分析
export const getBudgetComparison = (params) => {
  return request.get('/budget/comparison', { params })
}

// 获取预算执行预警
export const getBudgetAlerts = (params) => {
  return request.get('/budget/alerts', { params })
}

// ==================== 预算监控相关 ====================

// 获取预算监控数据
export const getBudgetMonitorData = (params) => {
  return request.get('/budget/monitor', { params })
}

// 获取预算执行进度
export const getBudgetProgress = (params) => {
  return request.get('/budget/progress', { params })
}

// 获取预算余额查询
export const getBudgetBalance = (params) => {
  return request.get('/budget/balance', { params })
}

// 预算调整申请
export const applyBudgetAdjustment = (data) => {
  return request.post('/budget/adjustment', data)
}

// 获取预算调整列表
export const getBudgetAdjustments = (params) => {
  return request.get('/budget/adjustments', { params })
}

// 创建预算调整
export const createBudgetAdjustment = (data) => {
  return request.post('/budget/adjustments', data)
}

// 更新预算调整
export const updateBudgetAdjustment = (id, data) => {
  return request.put(`/budget/adjustments/${id}`, data)
}

// 删除预算调整
export const deleteBudgetAdjustment = (id) => {
  return request.delete(`/budget/adjustments/${id}`)
}

// 审核预算调整
export const reviewBudgetAdjustment = (id, data) => {
  return request.post(`/budget/adjustments/${id}/review`, data)
}



// ==================== 预算报表相关 ====================

// 获取预算执行报表
export const getBudgetExecutionReport = (params) => {
  return request.get('/budget/reports/execution', { params })
}

// 获取预算差异分析报表
export const getBudgetVarianceReport = (params) => {
  return request.get('/budget/reports/variance', { params })
}

// 获取部门预算报表
export const getDepartmentBudgetReport = (params) => {
  return request.get('/budget/reports/department', { params })
}

// 导出预算报表
export const exportBudgetReport = (type, params) => {
  return request.get(`/budget/reports/${type}/export`, {
    params,
    responseType: 'blob'
  })
}

// ==================== 预算配置相关 ====================

// 获取预算科目树
export const getBudgetItemTree = (params) => {
  return request.get('/budget/items/tree', { params })
}

// 获取预算科目列表
export const getBudgetItems = (params) => {
  return request.get('/budget/items', { params })
}

// 获取部门列表
export const getDepartments = (params) => {
  return request.get('/organizations', { params })
}

// 创建预算科目
export const createBudgetItem = (data) => {
  return request.post('/budget/items', data)
}

// 更新预算科目
export const updateBudgetItem = (id, data) => {
  return request.put(`/budget/items/${id}`, data)
}

// 删除预算科目
export const deleteBudgetItem = (id) => {
  return request.delete(`/budget/items/${id}`)
}

// 获取预算模板
export const getBudgetTemplates = (params) => {
  return request.get('/budget/templates', { params })
}

// 创建预算模板
export const createBudgetTemplate = (data) => {
  return request.post('/budget/templates', data)
}

// 应用预算模板
export const applyBudgetTemplate = (templateId, data) => {
  return request.post(`/budget/templates/${templateId}/apply`, data)
}

// ==================== 预算统计相关 ====================

// 获取预算统计概览
export const getBudgetStatistics = (params) => {
  return request.get('/budget/statistics', { params })
}

// 获取预算执行排行
export const getBudgetExecutionRanking = (params) => {
  return request.get('/budget/ranking', { params })
}

// 获取预算趋势统计
export const getBudgetTrendStatistics = (params) => {
  return request.get('/budget/trend-statistics', { params })
}

// ==================== 预算导入导出相关 ====================

// 导入预算数据
export const importBudgetData = (file, params) => {
  const formData = new FormData()
  formData.append('file', file)

  Object.keys(params || {}).forEach(key => {
    formData.append(key, params[key])
  })

  return request.post('/budget/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 下载预算导入模板
export const downloadBudgetTemplate = (type) => {
  return request.get(`/budget/template/${type}`, {
    responseType: 'blob'
  })
}

// 导出预算数据
export const exportBudgetData = (params) => {
  return request.get('/budget/export', {
    params,
    responseType: 'blob'
  })
}

// ==================== 预算审批流程相关 ====================

// 获取预算审批流程
export const getBudgetApprovalFlow = (budgetId) => {
  return request.get(`/budget/${budgetId}/approval-flow`)
}

// 获取待审批预算列表
export const getPendingBudgets = (params) => {
  return request.get('/budget/pending', { params })
}

// 获取已审批预算列表
export const getApprovedBudgets = (params) => {
  return request.get('/budget/approved', { params })
}

// 撤回预算申请
export const withdrawBudget = (id) => {
  return request.post(`/budget/${id}/withdraw`)
}

// 重新提交预算
export const resubmitBudget = (id, data) => {
  return request.post(`/budget/${id}/resubmit`, data)
}
