import request from './request'

// 获取用户列表
export const getUsers = (params) => {
  return request.get('/users', { params })
}

// 获取单个用户信息
export const getUser = (id) => {
  return request.get(`/users/${id}`)
}

// 创建用户
export const createUser = (data) => {
  return request.post('/users', data)
}

// 更新用户
export const updateUser = (id, data) => {
  return request.put(`/users/${id}`, data)
}

// 删除用户
export const deleteUser = (id) => {
  return request.delete(`/users/${id}`)
}

// 重置用户密码
export const resetUserPassword = (id, data) => {
  return request.post(`/users/${id}/reset-password`, data)
}

// 启用/禁用用户
export const toggleUserStatus = (id, status) => {
  return request.patch(`/users/${id}/status`, { status })
}

// 分配用户角色
export const assignUserRoles = (id, roleIds) => {
  return request.post(`/users/${id}/roles`, { role_ids: roleIds })
}

// 分配用户岗位
export const assignUserPositions = (id, positionIds) => {
  return request.post(`/users/${id}/positions`, { position_ids: positionIds })
}

// 批量导入用户
export const importUsers = (data) => {
  return request.post('/users/import', data)
}

// 同步HIS/OA用户
export const syncUsers = (source) => {
  return request.post('/users/sync', { source })
}
