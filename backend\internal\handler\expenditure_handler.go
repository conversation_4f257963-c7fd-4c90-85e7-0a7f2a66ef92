package handler

import (
	"net/http"
	"quality_control/backend/internal/models"
	"quality_control/backend/internal/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

type ExpenditureHandler struct {
	expenditureService *service.ExpenditureService
}

func NewExpenditureHandler(expenditureService *service.ExpenditureService) *ExpenditureHandler {
	return &ExpenditureHandler{
		expenditureService: expenditureService,
	}
}

// GetExpenditureApplications 获取支出申请列表
// @Summary 获取支出申请列表
// @Description 获取支出申请列表，支持分页和筛选
// @Tags 支出控制
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param title query string false "申请标题"
// @Param type query int false "申请类型"
// @Param status query string false "审批状态"
// @Param payment_status query string false "支付状态"
// @Param applicant_id query int false "申请人ID"
// @Param department_id query int false "部门ID"
// @Param start_date query string false "开始日期"
// @Param end_date query string false "结束日期"
// @Success 200 {object} models.Response{data=service.UserListResponse}
// @Failure 400 {object} models.Response
// @Router /expenditure/applications [get]
func (h *ExpenditureHandler) GetExpenditureApplications(c *gin.Context) {
	var req service.ExpenditureApplicationListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	req.SetDefaults()

	resp, err := h.expenditureService.GetExpenditureApplications(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "获取支出申请列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    resp,
	})
}

// GetExpenditureApplication 获取支出申请详情
// @Summary 获取支出申请详情
// @Description 根据ID获取支出申请详情
// @Tags 支出控制
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "申请ID"
// @Success 200 {object} models.Response{data=models.ExpenditureApplication}
// @Failure 400 {object} models.Response
// @Router /expenditure/applications/{id} [get]
func (h *ExpenditureHandler) GetExpenditureApplication(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "申请ID格式错误",
		})
		return
	}

	application, err := h.expenditureService.GetExpenditureApplication(uint(id))
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    application,
	})
}

// CreateExpenditureApplication 创建支出申请
// @Summary 创建支出申请
// @Description 创建新的支出申请
// @Tags 支出控制
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body service.CreateExpenditureApplicationRequest true "创建支出申请请求"
// @Success 200 {object} models.Response{data=models.ExpenditureApplication}
// @Failure 400 {object} models.Response
// @Router /expenditure/applications [post]
func (h *ExpenditureHandler) CreateExpenditureApplication(c *gin.Context) {
	var req service.CreateExpenditureApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	userID, err := GetCurrentUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.Response{
			Code:    401,
			Message: err.Error(),
		})
		return
	}

	// 这里应该从用户信息中获取部门ID，暂时使用1
	departmentID := uint(1)

	application, err := h.expenditureService.CreateExpenditureApplication(&req, userID, departmentID)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "创建成功",
		Data:    application,
	})
}

// UpdateExpenditureApplication 更新支出申请
// @Summary 更新支出申请
// @Description 更新支出申请信息
// @Tags 支出控制
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "申请ID"
// @Param request body service.UpdateExpenditureApplicationRequest true "更新支出申请请求"
// @Success 200 {object} models.Response{data=models.ExpenditureApplication}
// @Failure 400 {object} models.Response
// @Router /expenditure/applications/{id} [put]
func (h *ExpenditureHandler) UpdateExpenditureApplication(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "申请ID格式错误",
		})
		return
	}

	var req service.UpdateExpenditureApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	application, err := h.expenditureService.UpdateExpenditureApplication(uint(id), &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "更新成功",
		Data:    application,
	})
}

// DeleteExpenditureApplication 删除支出申请
// @Summary 删除支出申请
// @Description 删除支出申请
// @Tags 支出控制
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "申请ID"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Router /expenditure/applications/{id} [delete]
func (h *ExpenditureHandler) DeleteExpenditureApplication(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "申请ID格式错误",
		})
		return
	}

	if err := h.expenditureService.DeleteExpenditureApplication(uint(id)); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "删除成功",
	})
}

// SubmitExpenditureApplication 提交支出申请
// @Summary 提交支出申请
// @Description 提交支出申请进行审批
// @Tags 支出控制
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "申请ID"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Router /expenditure/applications/{id}/submit [post]
func (h *ExpenditureHandler) SubmitExpenditureApplication(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "申请ID格式错误",
		})
		return
	}

	if err := h.expenditureService.SubmitExpenditureApplication(uint(id)); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "提交成功",
	})
}

// ApproveExpenditureApplication 审批支出申请
// @Summary 审批支出申请
// @Description 审批支出申请
// @Tags 支出控制
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "申请ID"
// @Param request body ApprovalRequest true "审批请求"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Router /expenditure/applications/{id}/approve [post]
func (h *ExpenditureHandler) ApproveExpenditureApplication(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "申请ID格式错误",
		})
		return
	}

	var req ApprovalRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	if err := h.expenditureService.ApproveExpenditureApplication(uint(id), req.Approved, req.Comment); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	message := "审批通过"
	if !req.Approved {
		message = "审批驳回"
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: message,
	})
}

// CreatePayment 创建支付记录
// @Summary 创建支付记录
// @Description 为已审批的申请创建支付记录
// @Tags 支出控制
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body service.CreatePaymentRequest true "创建支付请求"
// @Success 200 {object} models.Response{data=models.Payment}
// @Failure 400 {object} models.Response
// @Router /expenditure/payments [post]
func (h *ExpenditureHandler) CreatePayment(c *gin.Context) {
	var req service.CreatePaymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	userID, err := GetCurrentUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.Response{
			Code:    401,
			Message: err.Error(),
		})
		return
	}

	payment, err := h.expenditureService.CreatePayment(&req, userID)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "支付成功",
		Data:    payment,
	})
}
