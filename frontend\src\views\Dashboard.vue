<template>
  <div class="dashboard">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="welcome-section">
          <h1 class="page-title">工作台</h1>
          <p class="page-subtitle">欢迎回来，{{ user?.real_name || '用户' }}！今天是 {{ currentDate }}</p>
        </div>
        <div class="header-actions">
          <a-button type="primary" size="large" @click="handleShortcut('expense-apply')">
            <template #icon><PlusOutlined /></template>
            新建报销
          </a-button>
        </div>
      </div>
    </div>

    <!-- 核心指标卡片 -->
    <div class="metrics-section">
      <a-row :gutter="[24, 24]">
        <a-col :xs="24" :sm="12" :lg="6">
          <div class="metric-card pending-tasks">
            <div class="metric-icon">
              <ClockCircleOutlined />
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ todoCount }}</div>
              <div class="metric-label">待办任务</div>
              <div class="metric-trend">
                <ArrowUpOutlined class="trend-up" />
                <span>较昨日 +2</span>
              </div>
            </div>
          </div>
        </a-col>

        <a-col :xs="24" :sm="12" :lg="6">
          <div class="metric-card pending-approvals">
            <div class="metric-icon">
              <CheckCircleOutlined />
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ pendingApprovals }}</div>
              <div class="metric-label">待审批</div>
              <div class="metric-trend">
                <ArrowDownOutlined class="trend-down" />
                <span>较昨日 -1</span>
              </div>
            </div>
          </div>
        </a-col>

        <a-col :xs="24" :sm="12" :lg="6">
          <div class="metric-card monthly-expense">
            <div class="metric-icon">
              <DollarOutlined />
            </div>
            <div class="metric-content">
              <div class="metric-value">¥{{ formatMoney(monthlyExpense) }}</div>
              <div class="metric-label">本月支出</div>
              <div class="metric-trend">
                <ArrowUpOutlined class="trend-up" />
                <span>较上月 +15%</span>
              </div>
            </div>
          </div>
        </a-col>

        <a-col :xs="24" :sm="12" :lg="6">
          <div class="metric-card budget-usage">
            <div class="metric-icon">
              <FundOutlined />
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ budgetUsagePercent }}%</div>
              <div class="metric-label">预算执行率</div>
              <div class="metric-trend">
                <ArrowUpOutlined class="trend-up" />
                <span>目标 85%</span>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <a-row :gutter="[24, 24]">
        <!-- 待办事项 -->
        <a-col :xs="24" :lg="8">
          <a-card class="content-card" :bordered="false">
            <template #title>
              <div class="card-title">
                <ClockCircleOutlined class="title-icon" />
                <span>待办事项</span>
                <a-badge :count="todoCount" class="title-badge" />
              </div>
            </template>
            <template #extra>
              <a-button type="link" size="small" @click="goToApprovalCenter">
                查看全部
                <RightOutlined />
              </a-button>
            </template>

            <div class="todo-list">
              <a-list :data-source="todoList" :loading="todoLoading">
                <template #renderItem="{ item }">
                  <a-list-item class="todo-item" @click="handleTodoClick(item)">
                    <a-list-item-meta>
                      <template #avatar>
                        <a-avatar :style="{ backgroundColor: getTaskTypeColor(item.type) }">
                          <component :is="getTaskTypeIcon(item.type)" />
                        </a-avatar>
                      </template>
                      <template #title>
                        <div class="todo-title">{{ item.title }}</div>
                      </template>
                      <template #description>
                        <div class="todo-meta">
                          <a-tag :color="getTaskTypeColor(item.type)" size="small">
                            {{ item.type_name }}
                          </a-tag>
                          <span class="todo-time">{{ formatDate(item.created_at) }}</span>
                        </div>
                      </template>
                    </a-list-item-meta>
                    <div class="todo-priority">
                      <a-tag :color="getPriorityColor(item.priority)" size="small">
                        {{ getPriorityText(item.priority) }}
                      </a-tag>
                    </div>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </a-card>
        </a-col>

        <!-- 快捷操作 -->
        <a-col :xs="24" :lg="8">
          <a-card class="content-card" :bordered="false">
            <template #title>
              <div class="card-title">
                <ThunderboltOutlined class="title-icon" />
                <span>快捷操作</span>
              </div>
            </template>

            <div class="shortcut-container">
              <div class="shortcut-grid">
                <div class="shortcut-item expense" @click="handleShortcut('expense-apply')">
                  <div class="shortcut-icon">
                    <PlusCircleOutlined />
                  </div>
                  <div class="shortcut-content">
                    <div class="shortcut-title">我要报销</div>
                    <div class="shortcut-desc">快速创建报销申请</div>
                  </div>
                  <div class="shortcut-arrow">
                    <RightOutlined />
                  </div>
                </div>

                <div class="shortcut-item contract" @click="handleShortcut('contract-apply')">
                  <div class="shortcut-icon">
                    <FileTextOutlined />
                  </div>
                  <div class="shortcut-content">
                    <div class="shortcut-title">发起合同</div>
                    <div class="shortcut-desc">创建新的合同申请</div>
                  </div>
                  <div class="shortcut-arrow">
                    <RightOutlined />
                  </div>
                </div>

                <div class="shortcut-item procurement" @click="handleShortcut('purchase-apply')">
                  <div class="shortcut-icon">
                    <ShoppingCartOutlined />
                  </div>
                  <div class="shortcut-content">
                    <div class="shortcut-title">采购申请</div>
                    <div class="shortcut-desc">提交采购需求</div>
                  </div>
                  <div class="shortcut-arrow">
                    <RightOutlined />
                  </div>
                </div>

                <div class="shortcut-item query" @click="handleShortcut('expense-query')">
                  <div class="shortcut-icon">
                    <SearchOutlined />
                  </div>
                  <div class="shortcut-content">
                    <div class="shortcut-title">支出查询</div>
                    <div class="shortcut-desc">查看支出记录</div>
                  </div>
                  <div class="shortcut-arrow">
                    <RightOutlined />
                  </div>
                </div>

                <div class="shortcut-item analysis" @click="handleShortcut('budget-analysis')">
                  <div class="shortcut-icon">
                    <BarChartOutlined />
                  </div>
                  <div class="shortcut-content">
                    <div class="shortcut-title">预算分析</div>
                    <div class="shortcut-desc">查看预算执行情况</div>
                  </div>
                  <div class="shortcut-arrow">
                    <RightOutlined />
                  </div>
                </div>

                <div class="shortcut-item settings" @click="handleShortcut('system-settings')" v-if="isAdmin">
                  <div class="shortcut-icon">
                    <SettingOutlined />
                  </div>
                  <div class="shortcut-content">
                    <div class="shortcut-title">系统设置</div>
                    <div class="shortcut-desc">管理系统配置</div>
                  </div>
                  <div class="shortcut-arrow">
                    <RightOutlined />
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 通知公告 -->
        <a-col :xs="24" :lg="8">
          <a-card class="content-card" :bordered="false">
            <template #title>
              <div class="card-title">
                <BellOutlined class="title-icon" />
                <span>通知公告</span>
                <a-badge :count="unreadNoticeCount" class="title-badge" />
              </div>
            </template>
            <template #extra>
              <a-button type="link" size="small" @click="goToNotifications">
                查看全部
                <RightOutlined />
              </a-button>
            </template>

            <div class="notice-list">
              <a-list :data-source="noticeList" :loading="noticeLoading">
                <template #renderItem="{ item }">
                  <a-list-item class="notice-item" @click="handleNoticeClick(item)">
                    <a-list-item-meta>
                      <template #avatar>
                        <a-avatar :style="{ backgroundColor: getNoticeTypeColor(item.type) }">
                          <component :is="getNoticeTypeIcon(item.type)" />
                        </a-avatar>
                      </template>
                      <template #title>
                        <div class="notice-title">
                          {{ item.title }}
                          <a-badge v-if="!item.is_read" status="error" />
                        </div>
                      </template>
                      <template #description>
                        <div class="notice-meta">
                          <span class="notice-content">{{ item.content }}</span>
                          <span class="notice-time">{{ formatDate(item.created_at) }}</span>
                        </div>
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 管理驾驶舱 - 仅管理员可见 -->
    <div v-if="isAdmin" class="management-dashboard">
      <div class="section-header">
        <div class="section-title">
          <DashboardOutlined class="section-icon" />
          <h2>管理驾驶舱</h2>
        </div>
        <div class="section-actions">
          <a-button type="link" @click="goToFullDashboard">
            查看详细报表
            <RightOutlined />
          </a-button>
        </div>
      </div>

      <a-row :gutter="[24, 24]">
        <!-- 预算执行总览 -->
        <a-col :xs="24" :lg="8">
          <a-card class="chart-card" :bordered="false">
            <template #title>
              <div class="card-title">
                <FundOutlined class="title-icon" />
                <span>预算执行总览</span>
              </div>
            </template>

            <div class="budget-overview">
              <div class="progress-container">
                <a-progress
                  type="dashboard"
                  :percent="budgetProgress.percent"
                  :format="() => `${budgetProgress.percent}%`"
                  :stroke-color="getBudgetProgressColor(budgetProgress.percent)"
                  :stroke-width="8"
                  gap-degree={70}
                  size={180}
                />
                <div class="progress-center">
                  <div class="progress-value">¥{{ formatMoney(budgetProgress.used) }}</div>
                  <div class="progress-label">已执行</div>
                </div>
              </div>

              <div class="budget-details">
                <div class="budget-item">
                  <div class="item-label">年度预算</div>
                  <div class="item-value total">¥{{ formatMoney(budgetProgress.total) }}</div>
                </div>
                <div class="budget-item">
                  <div class="item-label">已执行</div>
                  <div class="item-value used">¥{{ formatMoney(budgetProgress.used) }}</div>
                </div>
                <div class="budget-item">
                  <div class="item-label">剩余预算</div>
                  <div class="item-value remaining">¥{{ formatMoney(budgetProgress.remaining) }}</div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 支出分类统计 -->
        <a-col :xs="24" :lg="8">
          <a-card class="chart-card" :bordered="false">
            <template #title>
              <div class="card-title">
                <PieChartOutlined class="title-icon" />
                <span>支出分类统计</span>
              </div>
            </template>
            <template #extra>
              <a-select v-model:value="expenseTimeRange" size="small" style="width: 100px">
                <a-select-option value="month">本月</a-select-option>
                <a-select-option value="quarter">本季度</a-select-option>
                <a-select-option value="year">本年</a-select-option>
              </a-select>
            </template>

            <div class="chart-container">
              <v-chart
                class="chart"
                :option="expenseChartOption"
                :loading="chartLoading"
                autoresize
              />
            </div>
          </a-card>
        </a-col>

        <!-- 科室支出排行 -->
        <a-col :xs="24" :lg="8">
          <a-card class="chart-card" :bordered="false">
            <template #title>
              <div class="card-title">
                <BarChartOutlined class="title-icon" />
                <span>科室支出排行</span>
              </div>
            </template>
            <template #extra>
              <a-tag color="blue">TOP 10</a-tag>
            </template>

            <div class="chart-container">
              <v-chart
                class="chart"
                :option="departmentChartOption"
                :loading="chartLoading"
                autoresize
              />
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 趋势分析 -->
      <a-row :gutter="[24, 24]" style="margin-top: 24px">
        <a-col :span="24">
          <a-card class="chart-card" :bordered="false">
            <template #title>
              <div class="card-title">
                <LineChartOutlined class="title-icon" />
                <span>支出趋势分析</span>
              </div>
            </template>
            <template #extra>
              <a-radio-group v-model:value="trendTimeRange" size="small">
                <a-radio-button value="7d">近7天</a-radio-button>
                <a-radio-button value="30d">近30天</a-radio-button>
                <a-radio-button value="90d">近90天</a-radio-button>
              </a-radio-group>
            </template>

            <div class="trend-chart-container">
              <v-chart
                class="trend-chart"
                :option="trendChartOption"
                :loading="chartLoading"
                autoresize
              />
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import * as echarts from 'echarts/core'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart, BarChart, LineChart } from 'echarts/charts'
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components'
import VChart from 'vue-echarts'
import dayjs from 'dayjs'
import {
  FileTextOutlined,
  PlusOutlined,
  SearchOutlined,
  ShoppingCartOutlined,
  FundOutlined,
  SettingOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  DollarOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  RightOutlined,
  ThunderboltOutlined,
  BellOutlined,
  DashboardOutlined,
  PieChartOutlined,
  BarChartOutlined,
  LineChartOutlined,
  PlusCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  WarningOutlined
} from '@ant-design/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { getUserTodoTasks } from '@/api/workflow'
import { getNotifications } from '@/api/system'
import { getDashboardData } from '@/api/system'

// 注册ECharts组件
use([
  CanvasRenderer,
  PieChart,
  BarChart,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const noticeList = ref([])
const todoLoading = ref(false)
const noticeLoading = ref(false)
const chartLoading = ref(false)

// 统计数据
const todoCount = ref(0)
const pendingApprovals = ref(0)
const monthlyExpense = ref(0)
const budgetUsagePercent = ref(0)
const unreadNoticeCount = ref(0)

// 时间范围选择
const expenseTimeRange = ref('month')
const trendTimeRange = ref('30d')

// 当前日期
const currentDate = computed(() => {
  return dayjs().format('YYYY年MM月DD日 dddd')
})

// 预算进度数据
const budgetProgress = reactive({
  total: 10000000,
  used: 7850000,
  remaining: 2150000,
  percent: 78.5
})

// 计算属性
const user = computed(() => authStore.user)
const isAdmin = computed(() => authStore.isAdmin)

// 待办事项数据
const todoList = ref([
  {
    id: 1,
    title: '张医生的差旅费报销申请',
    type: 'expense',
    type_name: '报销审批',
    priority: 'high',
    applicant: '张医生',
    created_at: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2小时前
  },
  {
    id: 2,
    title: '内科设备采购预算审批',
    type: 'budget',
    type_name: '预算审批',
    priority: 'medium',
    applicant: '内科',
    created_at: new Date(Date.now() - 24 * 60 * 60 * 1000) // 1天前
  },
  {
    id: 3,
    title: '财务科月度预算调整',
    type: 'budget',
    type_name: '预算调整',
    priority: 'normal',
    applicant: '财务科',
    created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000) // 2天前
  },
  {
    id: 4,
    title: '医疗设备采购合同审批',
    type: 'contract',
    type_name: '合同审批',
    priority: 'high',
    applicant: '设备科',
    created_at: new Date(Date.now() - 3 * 60 * 60 * 1000) // 3小时前
  },
  {
    id: 5,
    title: '药品采购申请审核',
    type: 'procurement',
    type_name: '采购审核',
    priority: 'medium',
    applicant: '药剂科',
    created_at: new Date(Date.now() - 6 * 60 * 60 * 1000) // 6小时前
  }
])

// ECharts配置 - 现代化设计
const expenseChartOption = ref({
  tooltip: {
    trigger: 'item',
    formatter: '{b}: ¥{c} ({d}%)',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderColor: 'transparent',
    textStyle: {
      color: '#fff',
      fontSize: 12
    }
  },
  legend: {
    bottom: 0,
    left: 'center',
    textStyle: {
      fontSize: 12,
      color: '#666'
    },
    itemWidth: 12,
    itemHeight: 12
  },
  series: [
    {
      name: '支出分类',
      type: 'pie',
      radius: ['45%', '75%'],
      center: ['50%', '45%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 4,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 14,
          fontWeight: 'bold'
        },
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      data: [
        { value: 335000, name: '人员费用', itemStyle: { color: '#1664FF' } },
        { value: 234000, name: '设备采购', itemStyle: { color: '#00C853' } },
        { value: 156000, name: '药品费用', itemStyle: { color: '#FF9800' } },
        { value: 89000, name: '办公费用', itemStyle: { color: '#F44336' } },
        { value: 67000, name: '其他费用', itemStyle: { color: '#9C27B0' } }
      ]
    }
  ]
})

const departmentChartOption = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: { type: 'shadow' },
    formatter: '{b}: ¥{c}',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderColor: 'transparent',
    textStyle: {
      color: '#fff',
      fontSize: 12
    }
  },
  grid: {
    left: '10%',
    right: '5%',
    top: '5%',
    bottom: '5%',
    containLabel: true
  },
  xAxis: {
    type: 'value',
    axisLabel: {
      formatter: function(value) {
        return '¥' + (value / 10000).toFixed(0) + '万'
      },
      color: '#666',
      fontSize: 11
    },
    axisLine: {
      lineStyle: { color: '#E2E8F0' }
    },
    splitLine: {
      lineStyle: { color: '#F1F5F9' }
    }
  },
  yAxis: {
    type: 'category',
    data: ['ICU', '肿瘤科', '神经科', '心内科', '骨科', '妇产科', '儿科', '内科', '外科', '急诊科'],
    axisLabel: {
      color: '#666',
      fontSize: 11
    },
    axisLine: {
      lineStyle: { color: '#E2E8F0' }
    }
  },
  series: [
    {
      name: '支出金额',
      type: 'bar',
      data: [28000, 32000, 35000, 38000, 43000, 45000, 56000, 67000, 78000, 89000],
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color: '#1664FF' },
          { offset: 1, color: '#4080FF' }
        ]),
        borderRadius: [0, 4, 4, 0]
      },
      barWidth: '60%'
    }
  ]
})

// 趋势图表配置
const trendChartOption = ref({
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderColor: 'transparent',
    textStyle: {
      color: '#fff',
      fontSize: 12
    }
  },
  legend: {
    data: ['支出金额', '预算金额'],
    top: 0,
    textStyle: {
      color: '#666',
      fontSize: 12
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    top: '10%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
    axisLabel: {
      color: '#666',
      fontSize: 11
    },
    axisLine: {
      lineStyle: { color: '#E2E8F0' }
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: function(value) {
        return '¥' + (value / 10000).toFixed(0) + '万'
      },
      color: '#666',
      fontSize: 11
    },
    axisLine: {
      lineStyle: { color: '#E2E8F0' }
    },
    splitLine: {
      lineStyle: { color: '#F1F5F9' }
    }
  },
  series: [
    {
      name: '支出金额',
      type: 'line',
      smooth: true,
      data: [120, 132, 101, 134, 90, 230, 210, 182, 191, 234, 290, 330],
      itemStyle: { color: '#1664FF' },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(22, 100, 255, 0.3)' },
          { offset: 1, color: 'rgba(22, 100, 255, 0.05)' }
        ])
      }
    },
    {
      name: '预算金额',
      type: 'line',
      smooth: true,
      data: [150, 150, 150, 150, 150, 250, 250, 250, 250, 250, 350, 350],
      itemStyle: { color: '#00C853' },
      lineStyle: { type: 'dashed' }
    }
  ]
})

// 工具函数
const formatDate = (date) => {
  const now = dayjs()
  const target = dayjs(date)
  const diff = now.diff(target, 'minute')

  if (diff < 60) return `${diff}分钟前`
  if (diff < 1440) return `${Math.floor(diff / 60)}小时前`
  if (diff < 10080) return `${Math.floor(diff / 1440)}天前`
  return target.format('MM-DD HH:mm')
}

const formatMoney = (amount) => {
  if (amount >= 10000) {
    return (amount / 10000).toFixed(1) + '万'
  }
  return amount.toLocaleString()
}

const getTaskTypeColor = (type) => {
  const colors = {
    'expense': '#1664FF',
    'budget': '#00C853',
    'contract': '#FF9800',
    'procurement': '#9C27B0'
  }
  return colors[type] || '#666666'
}

const getTaskTypeIcon = (type) => {
  const icons = {
    'expense': PlusCircleOutlined,
    'budget': FundOutlined,
    'contract': FileTextOutlined,
    'procurement': ShoppingCartOutlined
  }
  return icons[type] || InfoCircleOutlined
}

const getPriorityColor = (priority) => {
  const colors = {
    'high': 'red',
    'medium': 'orange',
    'normal': 'blue',
    'low': 'green'
  }
  return colors[priority] || 'default'
}

const getPriorityText = (priority) => {
  const texts = {
    'high': '高',
    'medium': '中',
    'normal': '普通',
    'low': '低'
  }
  return texts[priority] || '普通'
}

const getNoticeTypeColor = (type) => {
  const colors = {
    'system': '#1664FF',
    'announcement': '#00C853',
    'warning': '#FF9800',
    'urgent': '#F44336'
  }
  return colors[type] || '#1664FF'
}

const getNoticeTypeIcon = (type) => {
  const icons = {
    'system': SettingOutlined,
    'announcement': BellOutlined,
    'warning': WarningOutlined,
    'urgent': ExclamationCircleOutlined
  }
  return icons[type] || InfoCircleOutlined
}

const getBudgetProgressColor = (percent) => {
  if (percent >= 90) return '#F44336'
  if (percent >= 80) return '#FF9800'
  if (percent >= 60) return '#1664FF'
  return '#00C853'
}

// 数据加载函数
const loadTodoTasks = async () => {
  todoLoading.value = true
  try {
    const response = await getUserTodoTasks({ page_size: 5 })
    if (response.data?.list) {
      todoList.value = response.data.list
      todoCount.value = response.data.total || todoList.value.length
    }
  } catch (error) {
    console.error('加载待办任务失败:', error)
  } finally {
    todoLoading.value = false
  }
}

const loadNotifications = async () => {
  noticeLoading.value = true
  try {
    const response = await getNotifications({ page_size: 5, type: 1 })
    if (response.data?.list) {
      noticeList.value = response.data.list.map(item => ({
        ...item,
        type: item.type || 'system'
      }))
      unreadNoticeCount.value = noticeList.value.filter(n => !n.is_read).length
    }
  } catch (error) {
    console.error('加载通知失败:', error)
    // 使用模拟数据
    noticeList.value = [
      {
        id: 1,
        title: '系统维护通知',
        content: '系统将于今晚22:00-24:00进行维护升级',
        type: 'system',
        is_read: false,
        created_at: new Date(Date.now() - 30 * 60 * 1000)
      },
      {
        id: 2,
        title: '预算执行提醒',
        content: '本月预算执行率已达85%，请注意控制支出',
        type: 'warning',
        is_read: false,
        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000)
      },
      {
        id: 3,
        title: '新功能上线公告',
        content: '报销审批流程优化功能已上线',
        type: 'announcement',
        is_read: true,
        created_at: new Date(Date.now() - 24 * 60 * 60 * 1000)
      }
    ]
    unreadNoticeCount.value = noticeList.value.filter(n => !n.is_read).length
  } finally {
    noticeLoading.value = false
  }
}

const loadDashboardData = async () => {
  if (!isAdmin.value) return

  chartLoading.value = true
  try {
    const response = await getDashboardData()
    const data = response.data

    // 更新预算进度
    if (data.budget_progress) {
      Object.assign(budgetProgress, data.budget_progress)
      budgetUsagePercent.value = data.budget_progress.percent
    }

    // 更新统计数据
    if (data.statistics) {
      monthlyExpense.value = data.statistics.monthly_expense || monthlyExpense.value
      pendingApprovals.value = data.statistics.pending_approvals || pendingApprovals.value
    }

    // 更新图表数据
    if (data.expense_chart) {
      expenseChartOption.value.series[0].data = data.expense_chart
    }

    if (data.department_chart) {
      departmentChartOption.value.yAxis.data = data.department_chart.map(item => item.name)
      departmentChartOption.value.series[0].data = data.department_chart.map(item => item.value)
    }

    if (data.trend_chart) {
      trendChartOption.value.series[0].data = data.trend_chart.expense_data
      trendChartOption.value.series[1].data = data.trend_chart.budget_data
    }
  } catch (error) {
    console.error('加载仪表板数据失败:', error)
  } finally {
    chartLoading.value = false
  }
}

// 事件处理函数
const handleTodoClick = (item) => {
  router.push(`/workflow/tasks/${item.id}`)
}

const handleNoticeClick = (item) => {
  // 标记为已读
  if (!item.is_read) {
    item.is_read = true
    unreadNoticeCount.value = Math.max(0, unreadNoticeCount.value - 1)
  }
  router.push(`/system/notifications/${item.id}`)
}

const goToApprovalCenter = () => {
  router.push('/workflow/tasks')
}

const goToNotifications = () => {
  router.push('/system/notifications')
}

const goToFullDashboard = () => {
  router.push('/dashboard/analytics')
}

// 处理快捷入口点击
const handleShortcut = (key) => {
  const routeMap = {
    'expense-apply': '/expenditure/apply',
    'contract-apply': '/contract/apply',
    'purchase-apply': '/procurement/apply',
    'expense-query': '/expenditure/list',
    'budget-analysis': '/budget/analysis',
    'system-settings': '/system/config'
  }

  if (routeMap[key]) {
    router.push(routeMap[key])
  } else {
    message.info(`${key} 功能开发中...`)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadTodoTasks()
  loadNotifications()
  loadDashboardData()

  // 定时刷新数据
  setInterval(() => {
    loadTodoTasks()
    loadNotifications()
    if (isAdmin.value) {
      loadDashboardData()
    }
  }, 5 * 60 * 1000) // 每5分钟刷新一次
})
</script>

<style scoped>
/* Dashboard 主容器 */
.dashboard {
  padding: 0;
  background: var(--bg-secondary);
  min-height: 100vh;
}

/* 页面头部 */
.dashboard-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-active) 100%);
  color: white;
  padding: var(--spacing-2xl) var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.welcome-section {
  flex: 1;
}

.page-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin: 0 0 var(--spacing-sm) 0;
  color: white;
}

.page-subtitle {
  font-size: var(--font-size-md);
  margin: 0;
  opacity: 0.9;
  font-weight: var(--font-weight-normal);
}

.header-actions {
  display: flex;
  gap: var(--spacing-md);
}

/* 核心指标卡片 */
.metrics-section {
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.metric-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-hover));
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.metric-card.pending-tasks::before {
  background: linear-gradient(90deg, #FF9800, #FFB74D);
}

.metric-card.pending-approvals::before {
  background: linear-gradient(90deg, #00C853, #4CAF50);
}

.metric-card.monthly-expense::before {
  background: linear-gradient(90deg, #F44336, #E57373);
}

.metric-card.budget-usage::before {
  background: linear-gradient(90deg, #1664FF, #4080FF);
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.pending-tasks .metric-icon {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}

.pending-approvals .metric-icon {
  background: linear-gradient(135deg, #00C853, #4CAF50);
}

.monthly-expense .metric-icon {
  background: linear-gradient(135deg, #F44336, #E57373);
}

.budget-usage .metric-icon {
  background: linear-gradient(135deg, #1664FF, #4080FF);
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-xs);
}

.metric-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-xs);
}

.trend-up {
  color: var(--success-color);
}

.trend-down {
  color: var(--error-color);
}

/* 主要内容区域 */
.main-content {
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.content-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
}

.content-card:hover {
  box-shadow: var(--shadow-lg);
}

.card-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.title-icon {
  color: var(--primary-color);
  font-size: 16px;
}

.title-badge {
  margin-left: auto;
}

/* 待办事项样式 */
.todo-list {
  max-height: 400px;
  overflow-y: auto;
}

.todo-item {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.todo-item:hover {
  background: var(--bg-hover);
}

.todo-item:last-child {
  border-bottom: none;
}

.todo-title {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.todo-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.todo-time {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.todo-priority {
  margin-left: auto;
}

/* 快捷操作样式 */
.shortcut-container {
  padding: var(--spacing-md);
}

.shortcut-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.shortcut-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--bg-primary);
}

.shortcut-item:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
  transform: translateX(4px);
}

.shortcut-item.expense:hover {
  border-color: #1664FF;
  background: rgba(22, 100, 255, 0.02);
}

.shortcut-item.contract:hover {
  border-color: #FF9800;
  background: rgba(255, 152, 0, 0.02);
}

.shortcut-item.procurement:hover {
  border-color: #00C853;
  background: rgba(0, 200, 83, 0.02);
}

.shortcut-item.query:hover {
  border-color: #9C27B0;
  background: rgba(156, 39, 176, 0.02);
}

.shortcut-item.analysis:hover {
  border-color: #F44336;
  background: rgba(244, 67, 54, 0.02);
}

.shortcut-item.settings:hover {
  border-color: #607D8B;
  background: rgba(96, 125, 139, 0.02);
}

.shortcut-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
  margin-right: var(--spacing-md);
}

.expense .shortcut-icon {
  background: linear-gradient(135deg, #1664FF, #4080FF);
}

.contract .shortcut-icon {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}

.procurement .shortcut-icon {
  background: linear-gradient(135deg, #00C853, #4CAF50);
}

.query .shortcut-icon {
  background: linear-gradient(135deg, #9C27B0, #BA68C8);
}

.analysis .shortcut-icon {
  background: linear-gradient(135deg, #F44336, #E57373);
}

.settings .shortcut-icon {
  background: linear-gradient(135deg, #607D8B, #90A4AE);
}

.shortcut-content {
  flex: 1;
}

.shortcut-title {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: 2px;
}

.shortcut-desc {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.shortcut-arrow {
  color: var(--text-tertiary);
  font-size: 12px;
  transition: transform 0.2s ease;
}

.shortcut-item:hover .shortcut-arrow {
  transform: translateX(4px);
}

/* 通知样式 */
.notice-list {
  max-height: 400px;
  overflow-y: auto;
}

.notice-item {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.notice-item:hover {
  background: var(--bg-hover);
}

.notice-item:last-child {
  border-bottom: none;
}

.notice-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.notice-meta {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.notice-content {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-normal);
}

.notice-time {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

/* 管理驾驶舱样式 */
.management-dashboard {
  padding: 0 var(--spacing-lg);
  margin-top: var(--spacing-2xl);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.section-icon {
  color: var(--primary-color);
  font-size: 24px;
}

.section-title h2 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.section-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* 图表卡片样式 */
.chart-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
}

.chart-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.chart-container {
  padding: var(--spacing-md);
}

.chart {
  height: 300px;
  width: 100%;
}

.trend-chart-container {
  padding: var(--spacing-md);
}

.trend-chart {
  height: 400px;
  width: 100%;
}

/* 预算总览样式 */
.budget-overview {
  padding: var(--spacing-md);
}

.progress-container {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-lg);
}

.progress-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.progress-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
}

.progress-label {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  margin-top: 2px;
}

.budget-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.budget-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
}

.item-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.item-value {
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
}

.item-value.total {
  color: var(--primary-color);
}

.item-value.used {
  color: var(--warning-color);
}

.item-value.remaining {
  color: var(--success-color);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: var(--spacing-lg);
    text-align: center;
  }

  .metrics-section {
    padding: 0 var(--spacing-md);
  }

  .main-content {
    padding: 0 var(--spacing-md);
  }

  .management-dashboard {
    padding: 0 var(--spacing-md);
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .page-title {
    font-size: var(--font-size-2xl);
  }

  .metric-card {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }

  .metric-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .shortcut-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  .chart {
    height: 250px;
  }

  .trend-chart {
    height: 300px;
  }

  .section-header {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }
}

@media (max-width: 576px) {
  .dashboard-header {
    padding: var(--spacing-md);
  }

  .page-title {
    font-size: var(--font-size-xl);
  }

  .page-subtitle {
    font-size: var(--font-size-sm);
  }

  .metrics-section,
  .main-content,
  .management-dashboard {
    padding: 0 var(--spacing-sm);
  }

  .metric-card {
    padding: var(--spacing-md);
  }

  .content-card {
    margin-bottom: var(--spacing-md);
  }

  .chart {
    height: 200px;
  }

  .trend-chart {
    height: 250px;
  }
}
</style>