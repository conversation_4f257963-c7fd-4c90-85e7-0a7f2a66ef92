<template>
  <div class="expense-analysis">
    <a-card :bordered="false">
      <template #title>
        <h3>费用分析</h3>
      </template>

      <!-- 筛选区 -->
      <div class="filter-section">
        <a-form layout="inline" :model="filterForm" @finish="handleSearch">
          <a-form-item label="分析维度">
            <a-select v-model:value="filterForm.group_by" style="width: 150px">
              <a-select-option value="department">按部门</a-select-option>
              <a-select-option value="type">按类型</a-select-option>
              <a-select-option value="month">按月份</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="时间范围">
            <a-range-picker
              v-model:value="filterForm.date_range"
              format="YYYY-MM-DD"
              placeholder="请选择时间范围"
            />
          </a-form-item>
          
          <a-form-item label="部门">
            <a-tree-select
              v-model:value="filterForm.department_id"
              :tree-data="departmentTree"
              :field-names="{ children: 'children', label: 'name', value: 'id' }"
              placeholder="请选择部门"
              allow-clear
              style="width: 200px"
            />
          </a-form-item>
          
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon><SearchOutlined /></template>
                查询
              </a-button>
              <a-button @click="resetFilter">
                <template #icon><ReloadOutlined /></template>
                重置
              </a-button>
              <a-button @click="exportAnalysis">
                <template #icon><ExportOutlined /></template>
                导出
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 统计卡片 -->
      <div class="statistics-section">
        <a-row :gutter="16">
          <a-col :xs="12" :sm="6">
            <a-card size="small">
              <a-statistic
                title="总申请数"
                :value="statistics.total_applications"
                suffix="笔"
                :value-style="{ color: '#1890ff' }"
              />
            </a-card>
          </a-col>
          <a-col :xs="12" :sm="6">
            <a-card size="small">
              <a-statistic
                title="总金额"
                :value="statistics.total_amount"
                :precision="2"
                suffix="元"
                :value-style="{ color: '#52c41a' }"
              />
            </a-card>
          </a-col>
          <a-col :xs="12" :sm="6">
            <a-card size="small">
              <a-statistic
                title="平均金额"
                :value="statistics.avg_amount"
                :precision="2"
                suffix="元"
                :value-style="{ color: '#faad14' }"
              />
            </a-card>
          </a-col>
          <a-col :xs="12" :sm="6">
            <a-card size="small">
              <a-statistic
                title="通过率"
                :value="statistics.approval_rate"
                :precision="1"
                suffix="%"
                :value-style="{ color: '#fa8c16' }"
              />
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 图表分析 -->
      <div class="charts-section">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-card title="费用趋势分析" size="small">
              <v-chart
                class="chart"
                :option="trendChartOption"
                autoresize
              />
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="费用类型分布" size="small">
              <v-chart
                class="chart"
                :option="typeChartOption"
                autoresize
              />
            </a-card>
          </a-col>
        </a-row>
        
        <a-row :gutter="16" style="margin-top: 16px;">
          <a-col :span="24">
            <a-card title="部门费用对比" size="small">
              <v-chart
                class="chart"
                :option="departmentChartOption"
                autoresize
              />
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 明细表格 -->
      <div class="detail-section">
        <a-card title="费用明细" size="small">
          <a-table
            :columns="detailColumns"
            :data-source="detailData"
            :pagination="pagination"
            :loading="loading"
            row-key="id"
            @change="handleTableChange"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'amount'">
                <span class="amount-text">{{ formatMoney(record.amount) }}</span>
              </template>
              <template v-else-if="column.key === 'type'">
                <a-tag :color="getTypeColor(record.type)">
                  {{ getTypeName(record.type) }}
                </a-tag>
              </template>
            </template>
          </a-table>
        </a-card>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, PieChart, BarChart } from 'echarts/charts'
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components'
import VChart from 'vue-echarts'
import {
  SearchOutlined,
  ReloadOutlined,
  ExportOutlined
} from '@ant-design/icons-vue'
import { getExpenditureAnalysis, getExpenditureStatistics } from '@/api/expenditure'
import { getDepartments } from '@/api/organization'

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  PieChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

// 响应式数据
const loading = ref(false)
const departmentTree = ref([])
const detailData = ref([])

// 筛选表单
const filterForm = reactive({
  group_by: 'department',
  date_range: null,
  department_id: null
})

// 统计数据
const statistics = reactive({
  total_applications: 0,
  total_amount: 0,
  avg_amount: 0,
  approval_rate: 0
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 图表配置
const trendChartOption = ref({
  title: {
    text: '费用趋势',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value}元'
    }
  },
  series: [
    {
      name: '费用金额',
      type: 'line',
      data: [12000, 15000, 13000, 18000, 16000, 20000],
      itemStyle: { color: '#1890ff' }
    }
  ]
})

const typeChartOption = ref({
  title: {
    text: '费用类型分布',
    left: 'center'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c}元 ({d}%)'
  },
  series: [
    {
      name: '费用类型',
      type: 'pie',
      radius: '50%',
      data: [
        { value: 35000, name: '差旅费' },
        { value: 28000, name: '办公费' },
        { value: 15000, name: '培训费' },
        { value: 12000, name: '招待费' }
      ]
    }
  ]
})

const departmentChartOption = ref({
  title: {
    text: '部门费用对比',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  xAxis: {
    type: 'category',
    data: ['急诊科', '外科', '内科', '儿科', '妇产科']
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value}元'
    }
  },
  series: [
    {
      name: '费用金额',
      type: 'bar',
      data: [25000, 32000, 28000, 18000, 22000],
      itemStyle: { color: '#52c41a' }
    }
  ]
})

// 表格列配置
const detailColumns = [
  { title: '部门', dataIndex: 'department_name', key: 'department_name' },
  { title: '类型', key: 'type' },
  { title: '金额', key: 'amount' },
  { title: '申请数', dataIndex: 'count', key: 'count' }
]

// 工具函数
const formatMoney = (amount) => {
  return (amount / 100).toFixed(2) + '元'
}

const getTypeColor = (type) => {
  const colors = {
    'travel': 'blue',
    'office': 'green',
    'training': 'orange',
    'entertainment': 'purple'
  }
  return colors[type] || 'default'
}

const getTypeName = (type) => {
  const names = {
    'travel': '差旅费',
    'office': '办公费',
    'training': '培训费',
    'entertainment': '招待费'
  }
  return names[type] || '其他'
}

// 数据加载函数
const loadAnalysisData = async () => {
  loading.value = true
  try {
    const params = {
      ...filterForm,
      page: pagination.current,
      page_size: pagination.pageSize
    }
    
    if (filterForm.date_range && filterForm.date_range.length === 2) {
      params.start_date = filterForm.date_range[0].format('YYYY-MM-DD')
      params.end_date = filterForm.date_range[1].format('YYYY-MM-DD')
    }
    
    const response = await getExpenditureAnalysis(params)
    detailData.value = response.data.list || []
    pagination.total = response.data.total || 0
  } catch (error) {
    message.error('加载分析数据失败')
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  try {
    const response = await getExpenditureStatistics()
    Object.assign(statistics, response.data)
  } catch (error) {
    message.error('加载统计数据失败')
  }
}

const loadDepartments = async () => {
  try {
    const response = await getDepartments()
    departmentTree.value = response.data || []
  } catch (error) {
    message.error('加载部门数据失败')
  }
}

// 事件处理函数
const handleSearch = () => {
  pagination.current = 1
  loadAnalysisData()
}

const resetFilter = () => {
  Object.assign(filterForm, {
    group_by: 'department',
    date_range: null,
    department_id: null
  })
  pagination.current = 1
  loadAnalysisData()
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadAnalysisData()
}

const exportAnalysis = () => {
  message.info('导出功能开发中...')
}

// 组件挂载时加载数据
onMounted(() => {
  loadAnalysisData()
  loadStatistics()
  loadDepartments()
})
</script>

<style scoped>
.expense-analysis {
  padding: 24px;
}

.filter-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.statistics-section {
  margin-bottom: 24px;
}

.charts-section {
  margin-bottom: 24px;
}

.detail-section {
  margin-bottom: 24px;
}

.chart {
  height: 300px;
  width: 100%;
}

.amount-text {
  font-weight: 500;
  color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .expense-analysis {
    padding: 16px;
  }
  
  .chart {
    height: 250px;
  }
}
</style>
