package handler

import (
	"net/http"
	"quality_control/backend/internal/models"
	"quality_control/backend/internal/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

type RBACHandler struct {
	rbacService *service.RBACService
}

func NewRBACHandler(rbacService *service.RBACService) *RBACHandler {
	return &RBACHandler{
		rbacService: rbacService,
	}
}

// GetRoles 获取角色列表
// @Summary 获取角色列表
// @Description 获取角色列表，支持分页和筛选
// @Tags 角色权限管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param name query string false "角色名称"
// @Param code query string false "角色代码"
// @Param status query string false "状态"
// @Success 200 {object} models.Response{data=service.UserListResponse}
// @Failure 400 {object} models.Response
// @Router /roles [get]
func (h *RBACHandler) GetRoles(c *gin.Context) {
	var req service.RoleListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	req.SetDefaults()

	resp, err := h.rbacService.GetRoles(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "获取角色列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    resp,
	})
}

// GetRole 获取角色详情
// @Summary 获取角色详情
// @Description 根据ID获取角色详情
// @Tags 角色权限管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "角色ID"
// @Success 200 {object} models.Response{data=models.Role}
// @Failure 400 {object} models.Response
// @Router /roles/{id} [get]
func (h *RBACHandler) GetRole(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "角色ID格式错误",
		})
		return
	}

	role, err := h.rbacService.GetRole(uint(id))
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    role,
	})
}

// CreateRole 创建角色
// @Summary 创建角色
// @Description 创建新的角色
// @Tags 角色权限管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body service.CreateRoleRequest true "创建角色请求"
// @Success 200 {object} models.Response{data=models.Role}
// @Failure 400 {object} models.Response
// @Router /roles [post]
func (h *RBACHandler) CreateRole(c *gin.Context) {
	var req service.CreateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	role, err := h.rbacService.CreateRole(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "创建成功",
		Data:    role,
	})
}

// UpdateRole 更新角色
// @Summary 更新角色
// @Description 更新角色信息
// @Tags 角色权限管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "角色ID"
// @Param request body service.UpdateRoleRequest true "更新角色请求"
// @Success 200 {object} models.Response{data=models.Role}
// @Failure 400 {object} models.Response
// @Router /roles/{id} [put]
func (h *RBACHandler) UpdateRole(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "角色ID格式错误",
		})
		return
	}

	var req service.UpdateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	role, err := h.rbacService.UpdateRole(uint(id), &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "更新成功",
		Data:    role,
	})
}

// DeleteRole 删除角色
// @Summary 删除角色
// @Description 删除角色
// @Tags 角色权限管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "角色ID"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Router /roles/{id} [delete]
func (h *RBACHandler) DeleteRole(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "角色ID格式错误",
		})
		return
	}

	if err := h.rbacService.DeleteRole(uint(id)); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "删除成功",
	})
}

// GetPermissions 获取权限列表
// @Summary 获取权限列表
// @Description 获取权限列表，支持分页
// @Tags 角色权限管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} models.Response{data=service.UserListResponse}
// @Failure 500 {object} models.Response
// @Router /permissions [get]
func (h *RBACHandler) GetPermissions(c *gin.Context) {
	var req models.PageRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	req.SetDefaults()

	resp, err := h.rbacService.GetPermissions(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "获取权限列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    resp,
	})
}

// GetPermissionTree 获取权限树
// @Summary 获取权限树
// @Description 获取权限树形结构
// @Tags 角色权限管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.Response{data=[]service.PermissionTreeNode}
// @Failure 500 {object} models.Response
// @Router /permissions/tree [get]
func (h *RBACHandler) GetPermissionTree(c *gin.Context) {
	tree, err := h.rbacService.GetPermissionTree()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "获取权限树失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "获取成功",
		Data:    tree,
	})
}

// CreatePermission 创建权限
// @Summary 创建权限
// @Description 创建新的权限
// @Tags 角色权限管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body service.CreatePermissionRequest true "创建权限请求"
// @Success 200 {object} models.Response{data=models.Permission}
// @Failure 400 {object} models.Response
// @Router /permissions [post]
func (h *RBACHandler) CreatePermission(c *gin.Context) {
	var req service.CreatePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	permission, err := h.rbacService.CreatePermission(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "创建成功",
		Data:    permission,
	})
}

// AssignRolePermissionsRequest 分配角色权限请求
type AssignRolePermissionsRequest struct {
	PermissionIDs []uint `json:"permission_ids" binding:"required"`
}

// AssignRolePermissions 分配角色权限
// @Summary 分配角色权限
// @Description 为角色分配权限
// @Tags 角色权限管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "角色ID"
// @Param request body AssignRolePermissionsRequest true "分配权限请求"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Router /roles/{id}/permissions [post]
func (h *RBACHandler) AssignRolePermissions(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "角色ID格式错误",
		})
		return
	}

	var req AssignRolePermissionsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	if err := h.rbacService.AssignRolePermissions(uint(id), req.PermissionIDs); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    0,
		Message: "分配成功",
	})
}
